#!/usr/bin/env python3
"""
Test script to verify RTSP connectivity
"""
import cv2
import sys
import time

def test_rtsp_connection(rtsp_url):
    """Test RTSP connection and frame reading"""
    print(f"Testing RTSP URL: {rtsp_url}")
    
    try:
        # Try with FFMPEG backend first
        print("Attempting connection with FFMPEG backend...")
        cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
        
        if not cap.isOpened():
            print("FFMPEG backend failed, trying default backend...")
            cap = cv2.VideoCapture(rtsp_url)
        
        if not cap.isOpened():
            print("❌ Failed to open RTSP stream")
            return False
        
        print("✅ RTSP stream opened successfully")
        
        # Set properties
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # Get stream info
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"Stream info: {width}x{height} @ {fps} FPS")
        
        # Try to read frames
        print("Testing frame reading...")
        for i in range(5):
            ret, frame = cap.read()
            if ret:
                print(f"✅ Frame {i+1}: {frame.shape}")
            else:
                print(f"❌ Failed to read frame {i+1}")
                break
            time.sleep(0.1)
        
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Test URLs
    test_urls = [
        "rtsp://admin:Admin@123@192.168.4.243",
        "rtsp://admin:Admin@123@192.168.4.243/profile2",
        "rtsp://admin:123456@192.168.4.243",
        "rtsp://admin:123456@192.168.4.243/profile2"
    ]
    
    if len(sys.argv) > 1:
        test_urls = [sys.argv[1]]
    
    for url in test_urls:
        print("\n" + "="*60)
        success = test_rtsp_connection(url)
        if success:
            print(f"✅ {url} - SUCCESS")
            break
        else:
            print(f"❌ {url} - FAILED")
    
    print("\n" + "="*60)
    print("Test completed")
