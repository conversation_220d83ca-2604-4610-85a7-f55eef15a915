{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/webrtc.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"videoElem\"];\nfunction WebRTCCameraComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12)(3, \"h6\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14)(6, \"input\", 15);\n    i0.ɵɵlistener(\"change\", function WebRTCCameraComponent_div_8_Template_input_change_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const camera_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleStream(camera_r3, $event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 16);\n    i0.ɵɵelement(8, \"video\", 17);\n    i0.ɵɵelementStart(9, \"small\", 18);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const camera_r3 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(camera_r3.channelname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", camera_r3.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"video-\" + camera_r3.channelid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"IP: \", camera_r3.channelip, \"\");\n  }\n}\nfunction WebRTCCameraComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function WebRTCCameraComponent_div_9_div_4_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const stream_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.stopStreamById(stream_r7.streamId || \"\", stream_r7));\n    });\n    i0.ɵɵtext(5, \" Stop \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const stream_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", stream_r7.channelname, \" - \", stream_r7.streamId, \"\");\n  }\n}\nfunction WebRTCCameraComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h5\");\n    i0.ɵɵtext(2, \"Active Streams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, WebRTCCameraComponent_div_9_div_4_Template, 6, 2, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activeStreams);\n  }\n}\nfunction WebRTCCameraComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.errorMessage, \" \");\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bg-success\": a0,\n    \"bg-warning\": a1,\n    \"bg-danger\": a2\n  };\n};\nexport class WebRTCCameraComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    this.camera = [];\n    this.filteredCamera = [];\n    this.searchTerm = '';\n    // WebRTC properties\n    this.connectionState = 'disconnected';\n    this.errorMessage = null;\n    this.activeStreams = [];\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.getCamera();\n    this.setupWebRTCSubscriptions();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopAllStreams();\n  }\n  setupWebRTCSubscriptions() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n    // Subscribe to stream changes\n    this.subscriptions.push(this.webrtcService.stream$.subscribe(stream => {\n      if (stream) {\n        // Find the active camera and attach stream\n        const activeCamera = this.activeStreams[this.activeStreams.length - 1];\n        if (activeCamera) {\n          const videoElement = document.getElementById(`video-${activeCamera.channelid}`);\n          if (videoElement) {\n            videoElement.srcObject = stream;\n          }\n        }\n      }\n    }));\n  }\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [new CameraChannel(1, undefined, 'Camera 1', '**************', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false), new CameraChannel(2, undefined, 'Camera 2', '192.168.11.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false), new CameraChannel(3, undefined, 'Camera 3', '192.168.11.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false)];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n  toggleStream(camera, event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const checked = event.target.checked;\n      if (checked) {\n        yield _this.startStream(camera);\n      } else {\n        yield _this.stopStream(camera);\n      }\n    })();\n  }\n  startStream(camera) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.clearError();\n        const rtsp = `rtsp://${camera.username}:${camera.password}@${camera.channelip}/profile2`;\n        const streamRequest = {\n          rtsp: rtsp,\n          camera: camera.channelid || 0\n        };\n        const response = yield _this2.webrtcService.startStream(streamRequest);\n        // Update camera state\n        camera.checked = true;\n        camera.streamId = response.stream_id;\n        camera.webrtcUrl = response.webrtc_url;\n        // Add to active streams\n        _this2.activeStreams.push(camera);\n        // Subscribe to WebRTC stream and play it\n        setTimeout(() => {\n          _this2.webrtcService.stream$.subscribe(ms => {\n            if (ms) {\n              _this2.playWebRTC(ms, (camera.channelid || 0).toString());\n            }\n          });\n        }, 1000);\n        // Update session storage\n        _this2.updateSessionStorage();\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this2.errorMessage = `Failed to start stream for ${camera.channelname}: ${error}`;\n        camera.checked = false;\n      }\n    })();\n  }\n  stopStream(camera) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (camera.streamId) {\n          yield _this3.webrtcService.stopStream(camera.streamId);\n        }\n        // Update camera state\n        camera.checked = false;\n        camera.streamId = undefined;\n        camera.webrtcUrl = undefined;\n        // Remove from active streams\n        _this3.activeStreams = _this3.activeStreams.filter(s => s.channelid !== camera.channelid);\n        // Clear video element\n        const videoElement = document.getElementById(`video-${camera.channelid}`);\n        if (videoElement) {\n          videoElement.srcObject = null;\n        }\n        // Update session storage\n        _this3.updateSessionStorage();\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this3.errorMessage = `Failed to stop stream for ${camera.channelname}: ${error}`;\n      }\n    })();\n  }\n  stopStreamById(_streamId, camera) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4.stopStream(camera);\n    })();\n  }\n  stopAllStreams() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      for (const camera of _this5.activeStreams) {\n        yield _this5.stopStream(camera);\n      }\n    })();\n  }\n  onSearchKeyup(event) {\n    this.searchTerm = event.target.value;\n    if (this.searchTerm && this.searchTerm.trim() !== '') {\n      this.filteredCamera = this.camera.filter(camera => camera.channelname?.toLowerCase().includes(this.searchTerm.toLowerCase()) || false);\n    } else {\n      this.filteredCamera = this.camera;\n    }\n  }\n  updateSessionStorage() {\n    sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream, cam) {\n    const videoElement = document.getElementById(`video-${cam}`);\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n  static {\n    this.ɵfac = function WebRTCCameraComponent_Factory(t) {\n      return new (t || WebRTCCameraComponent)(i0.ɵɵdirectiveInject(i1.WebRTCService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WebRTCCameraComponent,\n      selectors: [[\"app-webrtc-camera\"]],\n      viewQuery: function WebRTCCameraComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElem = _t.first);\n        }\n      },\n      decls: 15,\n      vars: 11,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [1, \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Search cameras...\", 1, \"form-control\", 3, \"keyup\"], [\"class\", \"col-md-6 col-lg-4 mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-3\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-3\", 4, \"ngIf\"], [1, \"col-md-6\", \"col-lg-4\", \"mb-3\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"card-body\"], [\"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 1, \"w-100\", 2, \"height\", \"200px\", \"background\", \"#000\", 3, \"id\"], [1, \"text-muted\"], [1, \"mt-4\"], [1, \"list-group\"], [\"class\", \"list-group-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [1, \"alert\", \"alert-danger\", \"mt-3\"]],\n      template: function WebRTCCameraComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n          i0.ɵɵtext(4, \"WebRTC Camera Streaming\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"input\", 4);\n          i0.ɵɵlistener(\"keyup\", function WebRTCCameraComponent_Template_input_keyup_6_listener($event) {\n            return ctx.onSearchKeyup($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 1);\n          i0.ɵɵtemplate(8, WebRTCCameraComponent_div_8_Template, 11, 4, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, WebRTCCameraComponent_div_9_Template, 5, 1, \"div\", 6);\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"span\", 8);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"titlecase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, WebRTCCameraComponent_div_14_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredCamera);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeStreams.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c1, ctx.connectionState === \"connected\", ctx.connectionState === \"connecting\", ctx.connectionState === \"disconnected\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 5, ctx.connectionState), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.TitleCasePipe],\n      styles: [\".card[_ngcontent-%COMP%] {\\n      transition: transform 0.2s;\\n    }\\n    .card[_ngcontent-%COMP%]:hover {\\n      transform: translateY(-2px);\\n    }\\n    video[_ngcontent-%COMP%] {\\n      border-radius: 4px;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy93ZWJydGMtY2FtZXJhLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSwwQkFBMEI7SUFDNUI7SUFDQTtNQUNFLDJCQUEyQjtJQUM3QjtJQUNBO01BQ0Usa0JBQWtCO0lBQ3BCIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmNhcmQge1xuICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnM7XG4gICAgfVxuICAgIC5jYXJkOmhvdmVyIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICB9XG4gICAgdmlkZW8ge1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;IAwBYA,+BAA0E;IAGnDA,YAAsB;IAAAA,iBAAK;IAC5CA,+BAAoC;IAKhCA;MAAA;MAAA;MAAA;MAAA,OAAUA,qDAA4B;IAAA,EAAC;IAJzCA,iBAI0C;IAG9CA,+BAAuB;IACrBA,4BAOQ;IACRA,iCAA0B;IAAAA,aAAwB;IAAAA,iBAAQ;;;;IAlBzCA,eAAsB;IAAtBA,2CAAsB;IAKnCA,eAA0B;IAA1BA,2CAA0B;IAM5BA,eAAkC;IAAlCA,mDAAkC;IAOVA,eAAwB;IAAxBA,sDAAwB;;;;;;IAUtDA,+BAAkE;IAExDA,YAA4C;IAAAA,iBAAO;IACzDA,kCAE0D;IAAxDA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAkC,EAAE,YAAS;IAAA,EAAC;IACvDA,sBACF;IAAAA,iBAAS;;;;IALHA,eAA4C;IAA5CA,+EAA4C;;;;;IAL1DA,+BAAmD;IAC7CA,8BAAc;IAAAA,iBAAK;IACvBA,+BAAwB;IACtBA,6EASM;IACRA,iBAAM;;;;IAV4CA,eAAgB;IAAhBA,8CAAgB;;;;;IAyBpEA,+BAA0D;IACxDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,oDACF;;;;;;;;;;AAiBV,OAAM,MAAOC,qBAAqB;EAchCC,YACUC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAZvB,WAAM,GAAoB,EAAE;IAC5B,mBAAc,GAAoB,EAAE;IACpC,eAAU,GAAW,EAAE;IAEvB;IACA,oBAAe,GAAW,cAAc;IACxC,iBAAY,GAAkB,IAAI;IAClC,kBAAa,GAAoB,EAAE;IAE3B,kBAAa,GAAmB,EAAE;EAIvC;EAEHC,QAAQ;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAC,WAAW;IACT,IAAI,CAACC,aAAa,CAACC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQN,wBAAwB;IAC9B;IACA,IAAI,CAACE,aAAa,CAACK,IAAI,CACrB,IAAI,CAACV,aAAa,CAACW,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACR,aAAa,CAACK,IAAI,CACrB,IAAI,CAACV,aAAa,CAACe,OAAO,CAACH,SAAS,CAACI,MAAM,IAAG;MAC5C,IAAIA,MAAM,EAAE;QACV;QACA,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,GAAG,CAAC,CAAC;QACtE,IAAIF,YAAY,EAAE;UAChB,MAAMG,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASL,YAAY,CAACM,SAAS,EAAE,CAAqB;UACnG,IAAIH,YAAY,EAAE;YAChBA,YAAY,CAACI,SAAS,GAAGR,MAAM;;;;IAIvC,CAAC,CAAC,CACH;EACH;EAEAd,SAAS;IACP,MAAMuB,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC;IAC7D,IAAIF,UAAU,EAAE;MACd,IAAI,CAACG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACpC,IAAI,CAACM,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL;MACA,IAAI,CAACA,MAAM,GAAG,CACZ,IAAII,aAAa,CAAC,CAAC,EAAEC,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC,EACxM,IAAID,aAAa,CAAC,CAAC,EAAEC,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC,EACxM,IAAID,aAAa,CAAC,CAAC,EAAEC,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC,CACzM;MACD,IAAI,CAACF,cAAc,GAAG,IAAI,CAACH,MAAM;MACjCF,cAAc,CAACQ,OAAO,CAAC,kBAAkB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACP,MAAM,CAAC,CAAC;;EAE3E;EAEMQ,YAAY,CAACR,MAAqB,EAAES,KAAU;IAAA;IAAA;MAClD,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAACD,OAAO;MAEpC,IAAIA,OAAO,EAAE;QACX,MAAM,KAAI,CAACE,WAAW,CAACZ,MAAM,CAAC;OAC/B,MAAM;QACL,MAAM,KAAI,CAACa,UAAU,CAACb,MAAM,CAAC;;IAC9B;EACH;EAEMY,WAAW,CAACZ,MAAqB;IAAA;IAAA;MACrC,IAAI;QACF,MAAI,CAACc,UAAU,EAAE;QAEjB,MAAMC,IAAI,GAAG,UAAUf,MAAM,CAACgB,QAAQ,IAAIhB,MAAM,CAACiB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,WAAW;QAExF,MAAMC,aAAa,GAAkB;UACnCJ,IAAI,EAAEA,IAAI;UACVf,MAAM,EAAEA,MAAM,CAACL,SAAS,IAAI;SAC7B;QAED,MAAMyB,QAAQ,SAAyB,MAAI,CAAChD,aAAa,CAACwC,WAAW,CAACO,aAAa,CAAC;QAEpF;QACAnB,MAAM,CAACU,OAAO,GAAG,IAAI;QACrBV,MAAM,CAACqB,QAAQ,GAAGD,QAAQ,CAACE,SAAS;QACpCtB,MAAM,CAACuB,SAAS,GAAGH,QAAQ,CAACI,UAAU;QAEtC;QACA,MAAI,CAAClC,aAAa,CAACR,IAAI,CAACkB,MAAM,CAAC;QAE/B;QACAyB,UAAU,CAAC,MAAK;UACd,MAAI,CAACrD,aAAa,CAACe,OAAO,CAACH,SAAS,CAAC0C,EAAE,IAAG;YACxC,IAAIA,EAAE,EAAE;cACN,MAAI,CAACC,UAAU,CAACD,EAAE,EAAE,CAAC1B,MAAM,CAACL,SAAS,IAAI,CAAC,EAAEiC,QAAQ,EAAE,CAAC;;UAE3D,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAER;QACA,MAAI,CAACC,oBAAoB,EAAE;QAE3BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEX,QAAQ,CAAC;OAEtD,CAAC,OAAOY,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAI,CAACC,YAAY,GAAG,8BAA8BjC,MAAM,CAACkC,WAAW,KAAKF,KAAK,EAAE;QAChFhC,MAAM,CAACU,OAAO,GAAG,KAAK;;IACvB;EACH;EAEMG,UAAU,CAACb,MAAqB;IAAA;IAAA;MACpC,IAAI;QACF,IAAIA,MAAM,CAACqB,QAAQ,EAAE;UACnB,MAAM,MAAI,CAACjD,aAAa,CAACyC,UAAU,CAACb,MAAM,CAACqB,QAAQ,CAAC;;QAGtD;QACArB,MAAM,CAACU,OAAO,GAAG,KAAK;QACtBV,MAAM,CAACqB,QAAQ,GAAGhB,SAAS;QAC3BL,MAAM,CAACuB,SAAS,GAAGlB,SAAS;QAE5B;QACA,MAAI,CAACf,aAAa,GAAG,MAAI,CAACA,aAAa,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,SAAS,KAAKK,MAAM,CAACL,SAAS,CAAC;QAErF;QACA,MAAMH,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASM,MAAM,CAACL,SAAS,EAAE,CAAqB;QAC7F,IAAIH,YAAY,EAAE;UAChBA,YAAY,CAACI,SAAS,GAAG,IAAI;;QAG/B;QACA,MAAI,CAACiC,oBAAoB,EAAE;QAE3BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAE3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACC,YAAY,GAAG,6BAA6BjC,MAAM,CAACkC,WAAW,KAAKF,KAAK,EAAE;;IAChF;EACH;EAEMK,cAAc,CAACC,SAAiB,EAAEtC,MAAqB;IAAA;IAAA;MAC3D,MAAM,MAAI,CAACa,UAAU,CAACb,MAAM,CAAC;IAAC;EAChC;EAEMnB,cAAc;IAAA;IAAA;MAClB,KAAK,MAAMmB,MAAM,IAAI,MAAI,CAACV,aAAa,EAAE;QACvC,MAAM,MAAI,CAACuB,UAAU,CAACb,MAAM,CAAC;;IAC9B;EACH;EAEAuC,aAAa,CAAC9B,KAAoB;IAChC,IAAI,CAAC+B,UAAU,GAAI/B,KAAK,CAACE,MAA2B,CAAC8B,KAAK;IAE1D,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACA,UAAU,CAACE,IAAI,EAAE,KAAK,EAAE,EAAE;MACpD,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACH,MAAM,CAACmC,MAAM,CAACnC,MAAM,IAC7CA,MAAM,CAACkC,WAAW,EAAES,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACG,WAAW,EAAE,CAAC,IAAI,KAAK,CACnF;KACF,MAAM;MACL,IAAI,CAACxC,cAAc,GAAG,IAAI,CAACH,MAAM;;EAErC;EAEQ6B,oBAAoB;IAC1B/B,cAAc,CAACQ,OAAO,CAAC,kBAAkB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACP,MAAM,CAAC,CAAC;EACzE;EAEQc,UAAU;IAChB,IAAI,CAACmB,YAAY,GAAG,IAAI;EAC1B;EAEA;EACAN,UAAU,CAACvC,MAAmB,EAAEyD,GAAW;IACzC,MAAMrD,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASmD,GAAG,EAAE,CAAqB;IAEhF,IAAI,CAACzD,MAAM,EAAE;MACX0C,OAAO,CAACE,KAAK,CAAC,mCAAmCa,GAAG,EAAE,CAAC;MACvD;;IAGF,IAAIrD,YAAY,EAAE;MAChBA,YAAY,CAACI,SAAS,GAAGR,MAAM;MAC/BI,YAAY,CAACsD,IAAI,EAAE,CAACC,KAAK,CAACf,KAAK,IAAG;QAChCF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;MACFF,OAAO,CAACC,GAAG,CAAC,6BAA6Bc,GAAG,EAAE,CAAC;;EAEnD;;;uBAvMW3E,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAA8E;MAAAC;QAAA;;;;;;;;;;;;;UA3F9BhF,8BAA6B;UAGnBA,uCAAuB;UAAAA,iBAAK;UAGhCA,8BAAkB;UAKdA;YAAA,OAASiF,yBAAqB;UAAA,EAAC;UAJjCjF,iBAIkC;UAIpCA,8BAAiB;UACfA,uEAwBM;UACRA,iBAAM;UAGNA,sEAcM;UAGNA,+BAAkB;UAMdA,aACF;;UAAAA,iBAAO;UAITA,wEAEM;UACRA,iBAAM;;;UA3DqDA,eAAiB;UAAjBA,4CAAiB;UA4BvDA,eAA8B;UAA9BA,mDAA8B;UAkB3BA,eAIlB;UAJkBA,uKAIlB;UACAA,eACF;UADEA,2EACF;UAIoCA,eAAkB;UAAlBA,uCAAkB", "names": ["i0", "WebRTCCameraComponent", "constructor", "webrtcService", "ngOnInit", "getCamera", "setupWebRTCSubscriptions", "ngOnDestroy", "subscriptions", "for<PERSON>ach", "sub", "unsubscribe", "stopAllStreams", "push", "connectionState$", "subscribe", "state", "connectionState", "stream$", "stream", "activeCamera", "activeStreams", "length", "videoElement", "document", "getElementById", "channelid", "srcObject", "storedData", "sessionStorage", "getItem", "camera", "JSON", "parse", "filteredCamera", "CameraChannel", "undefined", "setItem", "stringify", "toggleStream", "event", "checked", "target", "startStream", "stopStream", "clearError", "rtsp", "username", "password", "channelip", "streamRequest", "response", "streamId", "stream_id", "webrtcUrl", "webrtc_url", "setTimeout", "ms", "playWebRTC", "toString", "updateSessionStorage", "console", "log", "error", "errorMessage", "channelname", "filter", "s", "stopStreamById", "_streamId", "onSearchKeyup", "searchTerm", "value", "trim", "toLowerCase", "includes", "cam", "play", "catch", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\webrtc-camera.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { WebRTCService } from '../services/webrtc.service';\nimport { CameraChannel, StreamRequest, StreamResponse } from '../models/camera.models';\n\n@Component({\n  selector: 'app-webrtc-camera',\n  template: `\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <h3>WebRTC Camera Streaming</h3>\n\n          <!-- Search -->\n          <div class=\"mb-3\">\n            <input\n              type=\"text\"\n              class=\"form-control\"\n              placeholder=\"Search cameras...\"\n              (keyup)=\"onSearchKeyup($event)\">\n          </div>\n\n          <!-- Camera List -->\n          <div class=\"row\">\n            <div class=\"col-md-6 col-lg-4 mb-3\" *ngFor=\"let camera of filteredCamera\">\n              <div class=\"card\">\n                <div class=\"card-header d-flex justify-content-between align-items-center\">\n                  <h6 class=\"mb-0\">{{camera.channelname}}</h6>\n                  <div class=\"form-check form-switch\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      [checked]=\"camera.checked\"\n                      (change)=\"toggleStream(camera, $event)\">\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <video\n                    [id]=\"'video-' + camera.channelid\"\n                    class=\"w-100\"\n                    style=\"height: 200px; background: #000;\"\n                    controls\n                    muted\n                    autoplay>\n                  </video>\n                  <small class=\"text-muted\">IP: {{camera.channelip}}</small>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Active Streams -->\n          <div class=\"mt-4\" *ngIf=\"activeStreams.length > 0\">\n            <h5>Active Streams</h5>\n            <div class=\"list-group\">\n              <div class=\"list-group-item\" *ngFor=\"let stream of activeStreams\">\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span>{{stream.channelname}} - {{stream.streamId}}</span>\n                  <button\n                    class=\"btn btn-sm btn-danger\"\n                    (click)=\"stopStreamById(stream.streamId || '', stream)\">\n                    Stop\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Connection Status -->\n          <div class=\"mt-3\">\n            <span class=\"badge\" [ngClass]=\"{\n              'bg-success': connectionState === 'connected',\n              'bg-warning': connectionState === 'connecting',\n              'bg-danger': connectionState === 'disconnected'\n            }\">\n              {{connectionState | titlecase}}\n            </span>\n          </div>\n\n          <!-- Error Message -->\n          <div class=\"alert alert-danger mt-3\" *ngIf=\"errorMessage\">\n            {{errorMessage}}\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .card {\n      transition: transform 0.2s;\n    }\n    .card:hover {\n      transform: translateY(-2px);\n    }\n    video {\n      border-radius: 4px;\n    }\n  `]\n})\nexport class WebRTCCameraComponent implements OnInit, OnDestroy {\n  @ViewChild('videoElem') videoElem!: ElementRef<HTMLVideoElement>;\n\n  camera: CameraChannel[] = [];\n  filteredCamera: CameraChannel[] = [];\n  searchTerm: string = '';\n\n  // WebRTC properties\n  connectionState: string = 'disconnected';\n  errorMessage: string | null = null;\n  activeStreams: CameraChannel[] = [];\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private webrtcService: WebRTCService\n  ) {}\n\n  ngOnInit(): void {\n    this.getCamera();\n    this.setupWebRTCSubscriptions();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopAllStreams();\n  }\n\n  private setupWebRTCSubscriptions(): void {\n    // Subscribe to connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n\n    // Subscribe to stream changes\n    this.subscriptions.push(\n      this.webrtcService.stream$.subscribe(stream => {\n        if (stream) {\n          // Find the active camera and attach stream\n          const activeCamera = this.activeStreams[this.activeStreams.length - 1];\n          if (activeCamera) {\n            const videoElement = document.getElementById(`video-${activeCamera.channelid}`) as HTMLVideoElement;\n            if (videoElement) {\n              videoElement.srcObject = stream;\n            }\n          }\n        }\n      })\n    );\n  }\n\n  getCamera(): void {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [\n        new CameraChannel(1, undefined, 'Camera 1', '**************', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false),\n        new CameraChannel(2, undefined, 'Camera 2', '192.168.11.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false),\n        new CameraChannel(3, undefined, 'Camera 3', '192.168.11.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false)\n      ];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n\n  async toggleStream(camera: CameraChannel, event: any): Promise<void> {\n    const checked = event.target.checked;\n\n    if (checked) {\n      await this.startStream(camera);\n    } else {\n      await this.stopStream(camera);\n    }\n  }\n\n  async startStream(camera: CameraChannel): Promise<void> {\n    try {\n      this.clearError();\n\n      const rtsp = `rtsp://${camera.username}:${camera.password}@${camera.channelip}/profile2`;\n\n      const streamRequest: StreamRequest = {\n        rtsp: rtsp,\n        camera: camera.channelid || 0\n      };\n\n      const response: StreamResponse = await this.webrtcService.startStream(streamRequest);\n\n      // Update camera state\n      camera.checked = true;\n      camera.streamId = response.stream_id;\n      camera.webrtcUrl = response.webrtc_url;\n\n      // Add to active streams\n      this.activeStreams.push(camera);\n\n      // Subscribe to WebRTC stream and play it\n      setTimeout(() => {\n        this.webrtcService.stream$.subscribe(ms => {\n          if (ms) {\n            this.playWebRTC(ms, (camera.channelid || 0).toString());\n          }\n        });\n      }, 1000);\n\n      // Update session storage\n      this.updateSessionStorage();\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream for ${camera.channelname}: ${error}`;\n      camera.checked = false;\n    }\n  }\n\n  async stopStream(camera: CameraChannel): Promise<void> {\n    try {\n      if (camera.streamId) {\n        await this.webrtcService.stopStream(camera.streamId);\n      }\n\n      // Update camera state\n      camera.checked = false;\n      camera.streamId = undefined;\n      camera.webrtcUrl = undefined;\n\n      // Remove from active streams\n      this.activeStreams = this.activeStreams.filter(s => s.channelid !== camera.channelid);\n\n      // Clear video element\n      const videoElement = document.getElementById(`video-${camera.channelid}`) as HTMLVideoElement;\n      if (videoElement) {\n        videoElement.srcObject = null;\n      }\n\n      // Update session storage\n      this.updateSessionStorage();\n\n      console.log('Stream stopped successfully');\n\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream for ${camera.channelname}: ${error}`;\n    }\n  }\n\n  async stopStreamById(_streamId: string, camera: CameraChannel): Promise<void> {\n    await this.stopStream(camera);\n  }\n\n  async stopAllStreams(): Promise<void> {\n    for (const camera of this.activeStreams) {\n      await this.stopStream(camera);\n    }\n  }\n\n  onSearchKeyup(event: KeyboardEvent): void {\n    this.searchTerm = (event.target as HTMLInputElement).value;\n\n    if (this.searchTerm && this.searchTerm.trim() !== '') {\n      this.filteredCamera = this.camera.filter(camera =>\n        camera.channelname?.toLowerCase().includes(this.searchTerm.toLowerCase()) || false\n      );\n    } else {\n      this.filteredCamera = this.camera;\n    }\n  }\n\n  private updateSessionStorage(): void {\n    sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n  }\n\n  private clearError(): void {\n    this.errorMessage = null;\n  }\n\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream: MediaStream, cam: string): void {\n    const videoElement = document.getElementById(`video-${cam}`) as HTMLVideoElement;\n\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}