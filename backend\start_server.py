#!/usr/bin/env python3
"""
Startup script for the RTSP to WebRTC streaming server.
This script provides a convenient way to start the server with proper configuration.
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        sys.exit(1)

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'fastapi',
        'uvicorn',
        'aiortc',
        'cv2',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Error: Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages using:")
        print("pip install -r requirements.txt")
        sys.exit(1)

def main():
    """Main startup function."""
    print("RTSP to WebRTC Streaming Server")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    print("✓ Python version check passed")
    
    # Check dependencies
    check_dependencies()
    print("✓ Dependencies check passed")
    
    # Get the directory of this script
    script_dir = Path(__file__).parent
    main_py = script_dir / "main.py"
    
    if not main_py.exists():
        print(f"Error: main.py not found in {script_dir}")
        sys.exit(1)
    
    print("\nStarting server...")
    print("Server will be available at: http://**************:8000")
    print("API documentation: http://**************:8000/docs")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 40)
    
    try:
        # Start the server
        subprocess.run([
            sys.executable, 
            str(main_py)
        ], cwd=script_dir)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
