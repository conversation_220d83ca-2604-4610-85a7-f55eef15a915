{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'RTSP WebRTC Client';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 11,\n      vars: 1,\n      consts: [[1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"bg-dark\"], [1, \"container-fluid\"], [\"href\", \"#\", 1, \"navbar-brand\"], [1, \"navbar-nav\"], [\"routerLink\", \"/cameras\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/viewer\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"container-fluid\", \"mt-3\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"a\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"Cameras\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Stream Viewer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(9, \"main\", 6);\n          i0.ɵɵelement(10, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.title);\n        }\n      },\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAOA,OAAM,MAAOA,YAAY;EALzBC;IAME,UAAK,GAAG,oBAAoB;;;;uBADjBD,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAE;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCNzBC,8BAAyD;UAEpBA,YAAS;UAAAA,iBAAI;UAC9CA,8BAAwB;UAC8CA,uBAAO;UAAAA,iBAAI;UAC/EA,4BAAmE;UAAAA,6BAAa;UAAAA,iBAAI;UAK1FA,+BAAmC;UACjCA,iCAA+B;UACjCA,iBAAO;;;UAV8BA,eAAS;UAATA,+BAAS", "names": ["AppComponent", "constructor", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.component.ts", "E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.html',\r\n  styleUrls: ['./app.css']\r\n})\r\nexport class AppComponent {\r\n  title = 'RTSP WebRTC Client';\r\n}\r\n", "<!-- RTSP to WebRTC Stream Viewer Application -->\r\n<nav class=\"navbar navbar-expand-lg navbar-dark bg-dark\">\r\n  <div class=\"container-fluid\">\r\n    <a class=\"navbar-brand\" href=\"#\">{{title}}</a>\r\n    <div class=\"navbar-nav\">\r\n      <a class=\"nav-link\" routerLink=\"/cameras\" routerLinkActive=\"active\">Cameras</a>\r\n      <a class=\"nav-link\" routerLink=\"/viewer\" routerLinkActive=\"active\">Stream Viewer</a>\r\n    </div>\r\n  </div>\r\n</nav>\r\n\r\n<main class=\"container-fluid mt-3\">\r\n  <router-outlet></router-outlet>\r\n</main>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}