{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CameraChannel } from '../models/camera.models';\nlet IcccCameraComponent = class IcccCameraComponent {\n  constructor(cameraService, webrtcService, http) {\n    this.cameraService = cameraService;\n    this.webrtcService = webrtcService;\n    this.http = http;\n    this.camera = [];\n    this.filteredCamera = [];\n  }\n  ngOnInit() {\n    this.getCamera();\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [new CameraChannel(1, undefined, 'Camera 1', '192.168.4.244', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined), new CameraChannel(2, undefined, 'Camera 2', '192.168.4.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined), new CameraChannel(3, undefined, 'Camera 3', '192.168.4.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined)];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream, cam) {\n    const videoElement = document.getElementById(`video-${cam}`);\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n  toggleStream(username, password, channelip, channelid, checked) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!checked) {\n        _this.stopStream(channelip, channelid, username, password, checked);\n      } else {\n        const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n        let cam = String(channelid);\n        try {\n          const response = yield _this.webrtcService.startCameraStream(rtsp, cam);\n          const camera = _this.camera.find(data => data.channelid === channelid);\n          if (camera) {\n            camera.webrtcUrl = response.webrtc_url;\n            camera.streamId = response.stream_id;\n            camera.checked = true;\n            // Subscribe to WebRTC stream and play it\n            setTimeout(() => {\n              _this.webrtcService.stream$.subscribe(ms => {\n                if (ms) {\n                  _this.playWebRTC(ms, cam);\n                }\n              });\n              console.log(`WebRTC URL for ${cam}:`, response.webrtc_url);\n            }, 1000);\n          }\n        } catch (error) {\n          console.error('Error starting WebRTC stream:', error);\n        }\n      }\n    })();\n  }\n  stopStream(channelip, channelid, username, password, checked) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n      let cam = String(channelid);\n      try {\n        yield _this2.webrtcService.stopCameraStream(rtsp, cam);\n        const camera = _this2.camera.find(data => data.channelid === channelid);\n        if (camera) {\n          camera.webrtcUrl = undefined;\n          camera.streamId = undefined;\n          camera.checked = false;\n          // Stop video element\n          const videoElement = document.getElementById(`video-${cam}`);\n          if (videoElement) {\n            videoElement.srcObject = null;\n          }\n        }\n      } catch (error) {\n        console.error('Error stopping WebRTC stream:', error);\n      }\n    })();\n  }\n  filterCameras() {\n    if (!this.searchTerm) {\n      this.filteredCamera = this.camera;\n    } else {\n      this.filteredCamera = this.camera.filter(camera => camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()) || camera.channelip.includes(this.searchTerm));\n    }\n  }\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n};\n__decorate([ViewChild('videoElem')], IcccCameraComponent.prototype, \"videoElem\", void 0);\nIcccCameraComponent = __decorate([Component({\n  selector: 'app-iccc-camera',\n  template: `\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <!-- Search Section -->\n        <div class=\"col-12 col-lg-3\">\n          <div class=\"card\">\n            <div class=\"card-header\">\n              <h5>Camera Search</h5>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"mb-3\">\n                <input\n                  type=\"text\"\n                  class=\"form-control\"\n                  placeholder=\"Search cameras...\"\n                  [(ngModel)]=\"searchTerm\"\n                  (input)=\"filterCameras()\">\n              </div>\n\n              <!-- Camera List -->\n              <div class=\"camera-list\" style=\"max-height: 400px; overflow-y: auto;\">\n                <div *ngFor=\"let camera of filteredCamera\" class=\"camera-item mb-2\">\n                  <div class=\"form-check form-switch\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      [id]=\"'camera-' + camera.channelid\"\n                      [checked]=\"camera.checked\"\n                      (change)=\"toggleStream(camera.username, camera.password, camera.channelip, camera.channelid, ($event.target as HTMLInputElement).checked)\">\n                    <label class=\"form-check-label\" [for]=\"'camera-' + camera.channelid\">\n                      {{camera.channelname}}\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Video Grid Section -->\n        <div class=\"col-12 col-lg-9\">\n          <div>\n            <div class=\"row\" *ngIf=\"resultLive\">\n              <div class=\"mb-4\">\n                <div class=\"video-grid\">\n                  <div *ngFor=\"let data of resultLive\" class=\"video-card\">\n                    <div class=\"card\">\n                      <div class=\"card-header\">\n                        <h6>{{ data.channelname }}</h6>\n                      </div>\n                      <div class=\"card-body\">\n                        <video\n                          *ngIf=\"data.webrtcUrl\"\n                          [id]=\"'video-' + data.channelid\"\n                          class=\"w-100\"\n                          style=\"height: 200px; background: #000;\"\n                          controls\n                          muted\n                          autoplay>\n                        </video>\n                        <div *ngIf=\"!data.webrtcUrl\" class=\"video-placeholder\">\n                          <p>No stream available</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .video-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n      gap: 1rem;\n    }\n\n    .video-card {\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .video-placeholder {\n      height: 200px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: #f8f9fa;\n      color: #6c757d;\n    }\n\n    .camera-item {\n      padding: 0.5rem;\n      border: 1px solid #e9ecef;\n      border-radius: 4px;\n      background: #f8f9fa;\n    }\n  `]\n})], IcccCameraComponent);\nexport { IcccCameraComponent };", "map": {"version": 3, "mappings": ";;AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AAIxE,SAASC,aAAa,QAAwB,yBAAyB;AA4GhE,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAQ9BC,YACSC,aAA4B,EAC3BC,aAA4B,EAC5BC,IAAgB;IAFjB,kBAAa,GAAbF,aAAa;IACZ,kBAAa,GAAbC,aAAa;IACb,SAAI,GAAJC,IAAI;IAPd,WAAM,GAAoB,EAAE;IAE5B,mBAAc,GAAU,EAAE;EAMvB;EAEHC,QAAQ;IACN,IAAI,CAACC,SAAS,EAAE;IAChBC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAK;MAC3CC,cAAc,CAACC,UAAU,CAAC,kBAAkB,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAJ,SAAS;IACP,MAAMK,UAAU,GAAGF,cAAc,CAACG,OAAO,CAAC,kBAAkB,CAAC;IAC7D,IAAID,UAAU,EAAE;MACd,IAAI,CAACE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MACpC,IAAI,CAACK,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL;MACA,IAAI,CAACA,MAAM,GAAG,CACZ,IAAId,aAAa,CAAC,CAAC,EAAEkB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,EACnP,IAAIlB,aAAa,CAAC,CAAC,EAAEkB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,EACnP,IAAIlB,aAAa,CAAC,CAAC,EAAEkB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,CACpP;MACD,IAAI,CAACD,cAAc,GAAG,IAAI,CAACH,MAAM;MACjCJ,cAAc,CAACS,OAAO,CAAC,kBAAkB,EAAEJ,IAAI,CAACK,SAAS,CAAC,IAAI,CAACN,MAAM,CAAC,CAAC;;EAE3E;EAEA;EACAO,UAAU,CAACC,MAAmB,EAAEC,GAAW;IACzC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASH,GAAG,EAAE,CAAqB;IAEhF,IAAI,CAACD,MAAM,EAAE;MACXK,OAAO,CAACC,KAAK,CAAC,mCAAmCL,GAAG,EAAE,CAAC;MACvD;;IAGF,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACK,SAAS,GAAGP,MAAM;MAC/BE,YAAY,CAACM,IAAI,EAAE,CAACC,KAAK,CAACH,KAAK,IAAG;QAChCD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;MACFD,OAAO,CAACK,GAAG,CAAC,6BAA6BT,GAAG,EAAE,CAAC;;EAEnD;EAEMU,YAAY,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,SAAiB,EAAEC,SAAiB,EAAEC,OAAgB;IAAA;IAAA;MAC3G,IAAI,CAACA,OAAO,EAAE;QACZ,KAAI,CAACC,UAAU,CAACH,SAAS,EAAEC,SAAS,EAAEH,QAAQ,EAAEC,QAAQ,EAAEG,OAAO,CAAC;OACnE,MAAM;QACL,MAAME,IAAI,GAAG,UAAUN,QAAQ,IAAIC,QAAQ,IAAIC,SAAS,WAAW;QACnE,IAAIb,GAAG,GAAWkB,MAAM,CAACJ,SAAS,CAAC;QAEnC,IAAI;UACF,MAAMK,QAAQ,SAAS,KAAI,CAACtC,aAAa,CAACuC,iBAAiB,CAACH,IAAI,EAAEjB,GAAG,CAAC;UACtE,MAAMT,MAAM,GAAG,KAAI,CAACA,MAAM,CAAC8B,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACR,SAAS,KAAKA,SAAS,CAAC;UACvE,IAAIvB,MAAM,EAAE;YACVA,MAAM,CAACgC,SAAS,GAAGJ,QAAQ,CAACK,UAAU;YACtCjC,MAAM,CAACkC,QAAQ,GAAGN,QAAQ,CAACO,SAAS;YACpCnC,MAAM,CAACwB,OAAO,GAAG,IAAI;YAErB;YACAY,UAAU,CAAC,MAAK;cACd,KAAI,CAAC9C,aAAa,CAAC+C,OAAO,CAACC,SAAS,CAACC,EAAE,IAAG;gBACxC,IAAIA,EAAE,EAAE;kBACN,KAAI,CAAChC,UAAU,CAACgC,EAAE,EAAE9B,GAAG,CAAC;;cAE5B,CAAC,CAAC;cACFI,OAAO,CAACK,GAAG,CAAC,kBAAkBT,GAAG,GAAG,EAAEmB,QAAQ,CAACK,UAAU,CAAC;YAC5D,CAAC,EAAE,IAAI,CAAC;;SAEX,CAAC,OAAOnB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;;IAExD;EACH;EAEMW,UAAU,CAACH,SAAiB,EAAEC,SAAiB,EAAEH,QAAgB,EAAEC,QAAgB,EAAEG,OAAgB;IAAA;IAAA;MACzG,MAAME,IAAI,GAAG,UAAUN,QAAQ,IAAIC,QAAQ,IAAIC,SAAS,WAAW;MACnE,IAAIb,GAAG,GAAWkB,MAAM,CAACJ,SAAS,CAAC;MAEnC,IAAI;QACF,MAAM,MAAI,CAACjC,aAAa,CAACkD,gBAAgB,CAACd,IAAI,EAAEjB,GAAG,CAAC;QACpD,MAAMT,MAAM,GAAG,MAAI,CAACA,MAAM,CAAC8B,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACR,SAAS,KAAKA,SAAS,CAAC;QACvE,IAAIvB,MAAM,EAAE;UACVA,MAAM,CAACgC,SAAS,GAAG5B,SAAS;UAC5BJ,MAAM,CAACkC,QAAQ,GAAG9B,SAAS;UAC3BJ,MAAM,CAACwB,OAAO,GAAG,KAAK;UAEtB;UACA,MAAMd,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASH,GAAG,EAAE,CAAqB;UAChF,IAAIC,YAAY,EAAE;YAChBA,YAAY,CAACK,SAAS,GAAG,IAAI;;;OAGlC,CAAC,OAAOD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEA2B,aAAa;IACX,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL,IAAI,CAACG,cAAc,GAAG,IAAI,CAACH,MAAM,CAAC2C,MAAM,CAAC3C,MAAM,IAC7CA,MAAM,CAAC4C,WAAW,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACG,WAAW,EAAE,CAAC,IACxE7C,MAAM,CAACsB,SAAS,CAACwB,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAAC,CAC3C;;EAEL;EAEA,IAAIK,UAAU;IACZ,OAAO,IAAI,CAAC/C,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC2C,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACxB,OAAO,CAAC,GAAG,EAAE;EACpE;CACD;AA3HyByB,YAAvBhE,SAAS,CAAC,WAAW,CAAC,sDAA0C;AADtDE,mBAAmB,eA1G/BH,SAAS,CAAC;EACTkE,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyET;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BR;CACF,CAAC,GACWjE,mBAAmB,CA4H/B;SA5HYA,mBAAmB", "names": ["Component", "ViewChild", "CameraChannel", "IcccCameraComponent", "constructor", "cameraService", "webrtcService", "http", "ngOnInit", "getCamera", "window", "addEventListener", "sessionStorage", "removeItem", "storedData", "getItem", "camera", "JSON", "parse", "filteredCamera", "undefined", "setItem", "stringify", "playWebRTC", "stream", "cam", "videoElement", "document", "getElementById", "console", "error", "srcObject", "play", "catch", "log", "toggleStream", "username", "password", "channelip", "channelid", "checked", "stopStream", "rtsp", "String", "response", "startCameraStream", "find", "data", "webrtcUrl", "webrtc_url", "streamId", "stream_id", "setTimeout", "stream$", "subscribe", "ms", "stopCameraStream", "filterCameras", "searchTerm", "filter", "channelname", "toLowerCase", "includes", "resultLive", "item", "__decorate", "selector", "template", "styles"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\iccc-camera.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { CameraService } from '../services/camera.service';\nimport { WebRTCService } from '../services/webrtc.service';\nimport { CameraChannel, CameraResponse } from '../models/camera.models';\n\n@Component({\n  selector: 'app-iccc-camera',\n  template: `\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <!-- Search Section -->\n        <div class=\"col-12 col-lg-3\">\n          <div class=\"card\">\n            <div class=\"card-header\">\n              <h5>Camera Search</h5>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"mb-3\">\n                <input\n                  type=\"text\"\n                  class=\"form-control\"\n                  placeholder=\"Search cameras...\"\n                  [(ngModel)]=\"searchTerm\"\n                  (input)=\"filterCameras()\">\n              </div>\n\n              <!-- Camera List -->\n              <div class=\"camera-list\" style=\"max-height: 400px; overflow-y: auto;\">\n                <div *ngFor=\"let camera of filteredCamera\" class=\"camera-item mb-2\">\n                  <div class=\"form-check form-switch\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      [id]=\"'camera-' + camera.channelid\"\n                      [checked]=\"camera.checked\"\n                      (change)=\"toggleStream(camera.username, camera.password, camera.channelip, camera.channelid, ($event.target as HTMLInputElement).checked)\">\n                    <label class=\"form-check-label\" [for]=\"'camera-' + camera.channelid\">\n                      {{camera.channelname}}\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Video Grid Section -->\n        <div class=\"col-12 col-lg-9\">\n          <div>\n            <div class=\"row\" *ngIf=\"resultLive\">\n              <div class=\"mb-4\">\n                <div class=\"video-grid\">\n                  <div *ngFor=\"let data of resultLive\" class=\"video-card\">\n                    <div class=\"card\">\n                      <div class=\"card-header\">\n                        <h6>{{ data.channelname }}</h6>\n                      </div>\n                      <div class=\"card-body\">\n                        <video\n                          *ngIf=\"data.webrtcUrl\"\n                          [id]=\"'video-' + data.channelid\"\n                          class=\"w-100\"\n                          style=\"height: 200px; background: #000;\"\n                          controls\n                          muted\n                          autoplay>\n                        </video>\n                        <div *ngIf=\"!data.webrtcUrl\" class=\"video-placeholder\">\n                          <p>No stream available</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .video-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n      gap: 1rem;\n    }\n\n    .video-card {\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .video-placeholder {\n      height: 200px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: #f8f9fa;\n      color: #6c757d;\n    }\n\n    .camera-item {\n      padding: 0.5rem;\n      border: 1px solid #e9ecef;\n      border-radius: 4px;\n      background: #f8f9fa;\n    }\n  `]\n})\nexport class IcccCameraComponent implements OnInit {\n  @ViewChild('videoElem') videoElem!: ElementRef<HTMLVideoElement>;\n\n  camerares!: CameraResponse;\n  camera: CameraChannel[] = [];\n  searchTerm: any;\n  filteredCamera: any[] = [];\n\n  constructor(\n    public cameraService: CameraService,\n    private webrtcService: WebRTCService,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    this.getCamera();\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [\n        new CameraChannel(1, undefined, 'Camera 1', '192.168.4.244', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined),\n        new CameraChannel(2, undefined, 'Camera 2', '192.168.4.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined),\n        new CameraChannel(3, undefined, 'Camera 3', '192.168.4.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined)\n      ];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream: MediaStream, cam: string): void {\n    const videoElement = document.getElementById(`video-${cam}`) as HTMLVideoElement;\n\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n\n  async toggleStream(username: string, password: string, channelip: string, channelid: number, checked: boolean) {\n    if (!checked) {\n      this.stopStream(channelip, channelid, username, password, checked);\n    } else {\n      const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n      let cam: string = String(channelid);\n\n      try {\n        const response = await this.webrtcService.startCameraStream(rtsp, cam);\n        const camera = this.camera.find((data) => data.channelid === channelid);\n        if (camera) {\n          camera.webrtcUrl = response.webrtc_url;\n          camera.streamId = response.stream_id;\n          camera.checked = true;\n\n          // Subscribe to WebRTC stream and play it\n          setTimeout(() => {\n            this.webrtcService.stream$.subscribe(ms => {\n              if (ms) {\n                this.playWebRTC(ms, cam);\n              }\n            });\n            console.log(`WebRTC URL for ${cam}:`, response.webrtc_url);\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Error starting WebRTC stream:', error);\n      }\n    }\n  }\n\n  async stopStream(channelip: string, channelid: number, username: string, password: string, checked: boolean) {\n    const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n    let cam: string = String(channelid);\n\n    try {\n      await this.webrtcService.stopCameraStream(rtsp, cam);\n      const camera = this.camera.find((data) => data.channelid === channelid);\n      if (camera) {\n        camera.webrtcUrl = undefined;\n        camera.streamId = undefined;\n        camera.checked = false;\n\n        // Stop video element\n        const videoElement = document.getElementById(`video-${cam}`) as HTMLVideoElement;\n        if (videoElement) {\n          videoElement.srcObject = null;\n        }\n      }\n    } catch (error) {\n      console.error('Error stopping WebRTC stream:', error);\n    }\n  }\n\n  filterCameras() {\n    if (!this.searchTerm) {\n      this.filteredCamera = this.camera;\n    } else {\n      this.filteredCamera = this.camera.filter(camera =>\n        camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        camera.channelip.includes(this.searchTerm)\n      );\n    }\n  }\n\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}