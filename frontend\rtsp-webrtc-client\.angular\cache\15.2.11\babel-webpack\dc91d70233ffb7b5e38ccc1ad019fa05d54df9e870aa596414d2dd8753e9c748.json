{"ast": null, "code": "// Models to match iccc-operator structure\nexport class CameraChannel {\n  constructor(channelid = null, status = null, channelname = null, channelip = null, channeltype = null, snapurl = null, majorurl = null, minorurl = null, analyticurl = null, username = null, password = null, latitude = null, longitude = null, description = null, recordingserverid = null, recordingservername = null, checked = null, hlsUrl = null, webrtcUrl = null, streamId = null, type = null) {\n    this.channelid = channelid;\n    this.status = status;\n    this.channelname = channelname;\n    this.channelip = channelip;\n    this.channeltype = channeltype;\n    this.snapurl = snapurl;\n    this.majorurl = majorurl;\n    this.minorurl = minorurl;\n    this.analyticurl = analyticurl;\n    this.username = username;\n    this.password = password;\n    this.latitude = latitude;\n    this.longitude = longitude;\n    this.description = description;\n    this.recordingserverid = recordingserverid;\n    this.recordingservername = recordingservername;\n    this.checked = checked;\n    this.hlsUrl = hlsUrl;\n    this.webrtcUrl = webrtcUrl;\n    this.streamId = streamId;\n    this.type = type;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}