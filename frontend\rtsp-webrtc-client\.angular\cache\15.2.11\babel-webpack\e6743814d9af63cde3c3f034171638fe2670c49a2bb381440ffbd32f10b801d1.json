{"ast": null, "code": "'use strict';\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * @license Angular v15.1.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n(function (factory) {\n  typeof define === 'function' && define.amd ? define(factory) : factory();\n})(function () {\n  'use strict';\n\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  (function (global) {\n    var performance = global['performance'];\n    function mark(name) {\n      performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n      performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    // Initialize before it's accessed below.\n    // __Zone_symbol_prefix global can be used to override the default zone\n    // symbol prefix with a custom one if needed.\n    var symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    function __symbol__(name) {\n      return symbolPrefix + name;\n    }\n    var checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone']) {\n      // if global['Zone'] already exists (maybe zone.js was already loaded or\n      // some other lib also registered a global object named Zone), we may need\n      // to throw an error, but sometimes user may not want this error.\n      // For example,\n      // we have two web pages, page1 includes zone.js, page2 doesn't.\n      // and the 1st time user load page1 and page2, everything work fine,\n      // but when user load page2 again, error occurs because global['Zone'] already exists.\n      // so we add a flag to let user choose whether to throw this error or not.\n      // By default, if existing Zone is from zone.js, we will not throw the error.\n      if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n        throw new Error('Zone already loaded.');\n      } else {\n        return global['Zone'];\n      }\n    }\n    var Zone = /** @class */function () {\n      function Zone(parent, zoneSpec) {\n        this._parent = parent;\n        this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n        this._properties = zoneSpec && zoneSpec.properties || {};\n        this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n      }\n      Zone.assertZonePatched = function () {\n        if (global['Promise'] !== patches['ZoneAwarePromise']) {\n          throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n        }\n      };\n      Object.defineProperty(Zone, \"root\", {\n        get: function () {\n          var zone = Zone.current;\n          while (zone.parent) {\n            zone = zone.parent;\n          }\n          return zone;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Object.defineProperty(Zone, \"current\", {\n        get: function () {\n          return _currentZoneFrame.zone;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Object.defineProperty(Zone, \"currentTask\", {\n        get: function () {\n          return _currentTask;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      // tslint:disable-next-line:require-internal-with-underscore\n      Zone.__load_patch = function (name, fn, ignoreDuplicate) {\n        if (ignoreDuplicate === void 0) {\n          ignoreDuplicate = false;\n        }\n        if (patches.hasOwnProperty(name)) {\n          // `checkDuplicate` option is defined from global variable\n          // so it works for all modules.\n          // `ignoreDuplicate` can work for the specified module\n          if (!ignoreDuplicate && checkDuplicate) {\n            throw Error('Already loaded patch: ' + name);\n          }\n        } else if (!global['__Zone_disable_' + name]) {\n          var perfName = 'Zone:' + name;\n          mark(perfName);\n          patches[name] = fn(global, Zone, _api);\n          performanceMeasure(perfName, perfName);\n        }\n      };\n      Object.defineProperty(Zone.prototype, \"parent\", {\n        get: function () {\n          return this._parent;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Object.defineProperty(Zone.prototype, \"name\", {\n        get: function () {\n          return this._name;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Zone.prototype.get = function (key) {\n        var zone = this.getZoneWith(key);\n        if (zone) return zone._properties[key];\n      };\n      Zone.prototype.getZoneWith = function (key) {\n        var current = this;\n        while (current) {\n          if (current._properties.hasOwnProperty(key)) {\n            return current;\n          }\n          current = current._parent;\n        }\n        return null;\n      };\n      Zone.prototype.fork = function (zoneSpec) {\n        if (!zoneSpec) throw new Error('ZoneSpec required!');\n        return this._zoneDelegate.fork(this, zoneSpec);\n      };\n      Zone.prototype.wrap = function (callback, source) {\n        if (typeof callback !== 'function') {\n          throw new Error('Expecting function got: ' + callback);\n        }\n        var _callback = this._zoneDelegate.intercept(this, callback, source);\n        var zone = this;\n        return function () {\n          return zone.runGuarded(_callback, this, arguments, source);\n        };\n      };\n      Zone.prototype.run = function (callback, applyThis, applyArgs, source) {\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } finally {\n          _currentZoneFrame = _currentZoneFrame.parent;\n        }\n      };\n      Zone.prototype.runGuarded = function (callback, applyThis, applyArgs, source) {\n        if (applyThis === void 0) {\n          applyThis = null;\n        }\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n        try {\n          try {\n            return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n          } catch (error) {\n            if (this._zoneDelegate.handleError(this, error)) {\n              throw error;\n            }\n          }\n        } finally {\n          _currentZoneFrame = _currentZoneFrame.parent;\n        }\n      };\n      Zone.prototype.runTask = function (task, applyThis, applyArgs) {\n        if (task.zone != this) {\n          throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n        }\n        // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n        // will run in notScheduled(canceled) state, we should not try to\n        // run such kind of task but just return\n        if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n          return;\n        }\n        var reEntryGuard = task.state != running;\n        reEntryGuard && task._transitionTo(running, scheduled);\n        task.runCount++;\n        var previousTask = _currentTask;\n        _currentTask = task;\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n        try {\n          if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n            task.cancelFn = undefined;\n          }\n          try {\n            return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n          } catch (error) {\n            if (this._zoneDelegate.handleError(this, error)) {\n              throw error;\n            }\n          }\n        } finally {\n          // if the task's state is notScheduled or unknown, then it has already been cancelled\n          // we should not reset the state to scheduled\n          if (task.state !== notScheduled && task.state !== unknown) {\n            if (task.type == eventTask || task.data && task.data.isPeriodic) {\n              reEntryGuard && task._transitionTo(scheduled, running);\n            } else {\n              task.runCount = 0;\n              this._updateTaskCount(task, -1);\n              reEntryGuard && task._transitionTo(notScheduled, running, notScheduled);\n            }\n          }\n          _currentZoneFrame = _currentZoneFrame.parent;\n          _currentTask = previousTask;\n        }\n      };\n      Zone.prototype.scheduleTask = function (task) {\n        if (task.zone && task.zone !== this) {\n          // check if the task was rescheduled, the newZone\n          // should not be the children of the original zone\n          var newZone = this;\n          while (newZone) {\n            if (newZone === task.zone) {\n              throw Error(\"can not reschedule task to \".concat(this.name, \" which is descendants of the original zone \").concat(task.zone.name));\n            }\n            newZone = newZone.parent;\n          }\n        }\n        task._transitionTo(scheduling, notScheduled);\n        var zoneDelegates = [];\n        task._zoneDelegates = zoneDelegates;\n        task._zone = this;\n        try {\n          task = this._zoneDelegate.scheduleTask(this, task);\n        } catch (err) {\n          // should set task's state to unknown when scheduleTask throw error\n          // because the err may from reschedule, so the fromState maybe notScheduled\n          task._transitionTo(unknown, scheduling, notScheduled);\n          // TODO: @JiaLiPassion, should we check the result from handleError?\n          this._zoneDelegate.handleError(this, err);\n          throw err;\n        }\n        if (task._zoneDelegates === zoneDelegates) {\n          // we have to check because internally the delegate can reschedule the task.\n          this._updateTaskCount(task, 1);\n        }\n        if (task.state == scheduling) {\n          task._transitionTo(scheduled, scheduling);\n        }\n        return task;\n      };\n      Zone.prototype.scheduleMicroTask = function (source, callback, data, customSchedule) {\n        return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n      };\n      Zone.prototype.scheduleMacroTask = function (source, callback, data, customSchedule, customCancel) {\n        return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n      };\n      Zone.prototype.scheduleEventTask = function (source, callback, data, customSchedule, customCancel) {\n        return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n      };\n      Zone.prototype.cancelTask = function (task) {\n        if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n        if (task.state !== scheduled && task.state !== running) {\n          return;\n        }\n        task._transitionTo(canceling, scheduled, running);\n        try {\n          this._zoneDelegate.cancelTask(this, task);\n        } catch (err) {\n          // if error occurs when cancelTask, transit the state to unknown\n          task._transitionTo(unknown, canceling);\n          this._zoneDelegate.handleError(this, err);\n          throw err;\n        }\n        this._updateTaskCount(task, -1);\n        task._transitionTo(notScheduled, canceling);\n        task.runCount = 0;\n        return task;\n      };\n      Zone.prototype._updateTaskCount = function (task, count) {\n        var zoneDelegates = task._zoneDelegates;\n        if (count == -1) {\n          task._zoneDelegates = null;\n        }\n        for (var i = 0; i < zoneDelegates.length; i++) {\n          zoneDelegates[i]._updateTaskCount(task.type, count);\n        }\n      };\n      return Zone;\n    }();\n    // tslint:disable-next-line:require-internal-with-underscore\n    Zone.__symbol__ = __symbol__;\n    var DELEGATE_ZS = {\n      name: '',\n      onHasTask: function (delegate, _, target, hasTaskState) {\n        return delegate.hasTask(target, hasTaskState);\n      },\n      onScheduleTask: function (delegate, _, target, task) {\n        return delegate.scheduleTask(target, task);\n      },\n      onInvokeTask: function (delegate, _, target, task, applyThis, applyArgs) {\n        return delegate.invokeTask(target, task, applyThis, applyArgs);\n      },\n      onCancelTask: function (delegate, _, target, task) {\n        return delegate.cancelTask(target, task);\n      }\n    };\n    var _ZoneDelegate = /** @class */function () {\n      function _ZoneDelegate(zone, parentDelegate, zoneSpec) {\n        this._taskCounts = {\n          'microTask': 0,\n          'macroTask': 0,\n          'eventTask': 0\n        };\n        this.zone = zone;\n        this._parentDelegate = parentDelegate;\n        this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n        this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n        this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n        this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n        this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n        this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n        this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n        this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n        this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n        this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n        this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n        this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n        this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n        this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n        this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n        this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n        this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n        this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n        this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n        this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n        this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n        this._hasTaskZS = null;\n        this._hasTaskDlgt = null;\n        this._hasTaskDlgtOwner = null;\n        this._hasTaskCurrZone = null;\n        var zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n        var parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n        if (zoneSpecHasTask || parentHasTask) {\n          // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n          // a case all task related interceptors must go through this ZD. We can't short circuit it.\n          this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n          this._hasTaskDlgt = parentDelegate;\n          this._hasTaskDlgtOwner = this;\n          this._hasTaskCurrZone = zone;\n          if (!zoneSpec.onScheduleTask) {\n            this._scheduleTaskZS = DELEGATE_ZS;\n            this._scheduleTaskDlgt = parentDelegate;\n            this._scheduleTaskCurrZone = this.zone;\n          }\n          if (!zoneSpec.onInvokeTask) {\n            this._invokeTaskZS = DELEGATE_ZS;\n            this._invokeTaskDlgt = parentDelegate;\n            this._invokeTaskCurrZone = this.zone;\n          }\n          if (!zoneSpec.onCancelTask) {\n            this._cancelTaskZS = DELEGATE_ZS;\n            this._cancelTaskDlgt = parentDelegate;\n            this._cancelTaskCurrZone = this.zone;\n          }\n        }\n      }\n      _ZoneDelegate.prototype.fork = function (targetZone, zoneSpec) {\n        return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new Zone(targetZone, zoneSpec);\n      };\n      _ZoneDelegate.prototype.intercept = function (targetZone, callback, source) {\n        return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n      };\n      _ZoneDelegate.prototype.invoke = function (targetZone, callback, applyThis, applyArgs, source) {\n        return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n      };\n      _ZoneDelegate.prototype.handleError = function (targetZone, error) {\n        return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n      };\n      _ZoneDelegate.prototype.scheduleTask = function (targetZone, task) {\n        var returnTask = task;\n        if (this._scheduleTaskZS) {\n          if (this._hasTaskZS) {\n            returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n          }\n          // clang-format off\n          returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n          // clang-format on\n          if (!returnTask) returnTask = task;\n        } else {\n          if (task.scheduleFn) {\n            task.scheduleFn(task);\n          } else if (task.type == microTask) {\n            scheduleMicroTask(task);\n          } else {\n            throw new Error('Task is missing scheduleFn.');\n          }\n        }\n        return returnTask;\n      };\n      _ZoneDelegate.prototype.invokeTask = function (targetZone, task, applyThis, applyArgs) {\n        return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n      };\n      _ZoneDelegate.prototype.cancelTask = function (targetZone, task) {\n        var value;\n        if (this._cancelTaskZS) {\n          value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n        } else {\n          if (!task.cancelFn) {\n            throw Error('Task is not cancelable');\n          }\n          value = task.cancelFn(task);\n        }\n        return value;\n      };\n      _ZoneDelegate.prototype.hasTask = function (targetZone, isEmpty) {\n        // hasTask should not throw error so other ZoneDelegate\n        // can still trigger hasTask callback\n        try {\n          this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n        } catch (err) {\n          this.handleError(targetZone, err);\n        }\n      };\n      // tslint:disable-next-line:require-internal-with-underscore\n      _ZoneDelegate.prototype._updateTaskCount = function (type, count) {\n        var counts = this._taskCounts;\n        var prev = counts[type];\n        var next = counts[type] = prev + count;\n        if (next < 0) {\n          throw new Error('More tasks executed then were scheduled.');\n        }\n        if (prev == 0 || next == 0) {\n          var isEmpty = {\n            microTask: counts['microTask'] > 0,\n            macroTask: counts['macroTask'] > 0,\n            eventTask: counts['eventTask'] > 0,\n            change: type\n          };\n          this.hasTask(this.zone, isEmpty);\n        }\n      };\n      return _ZoneDelegate;\n    }();\n    var ZoneTask = /** @class */function () {\n      function ZoneTask(type, source, callback, options, scheduleFn, cancelFn) {\n        // tslint:disable-next-line:require-internal-with-underscore\n        this._zone = null;\n        this.runCount = 0;\n        // tslint:disable-next-line:require-internal-with-underscore\n        this._zoneDelegates = null;\n        // tslint:disable-next-line:require-internal-with-underscore\n        this._state = 'notScheduled';\n        this.type = type;\n        this.source = source;\n        this.data = options;\n        this.scheduleFn = scheduleFn;\n        this.cancelFn = cancelFn;\n        if (!callback) {\n          throw new Error('callback is not defined');\n        }\n        this.callback = callback;\n        var self = this;\n        // TODO: @JiaLiPassion options should have interface\n        if (type === eventTask && options && options.useG) {\n          this.invoke = ZoneTask.invokeTask;\n        } else {\n          this.invoke = function () {\n            return ZoneTask.invokeTask.call(global, self, this, arguments);\n          };\n        }\n      }\n      ZoneTask.invokeTask = function (task, target, args) {\n        if (!task) {\n          task = this;\n        }\n        _numberOfNestedTaskFrames++;\n        try {\n          task.runCount++;\n          return task.zone.runTask(task, target, args);\n        } finally {\n          if (_numberOfNestedTaskFrames == 1) {\n            drainMicroTaskQueue();\n          }\n          _numberOfNestedTaskFrames--;\n        }\n      };\n      Object.defineProperty(ZoneTask.prototype, \"zone\", {\n        get: function () {\n          return this._zone;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Object.defineProperty(ZoneTask.prototype, \"state\", {\n        get: function () {\n          return this._state;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      ZoneTask.prototype.cancelScheduleRequest = function () {\n        this._transitionTo(notScheduled, scheduling);\n      };\n      // tslint:disable-next-line:require-internal-with-underscore\n      ZoneTask.prototype._transitionTo = function (toState, fromState1, fromState2) {\n        if (this._state === fromState1 || this._state === fromState2) {\n          this._state = toState;\n          if (toState == notScheduled) {\n            this._zoneDelegates = null;\n          }\n        } else {\n          throw new Error(\"\".concat(this.type, \" '\").concat(this.source, \"': can not transition to '\").concat(toState, \"', expecting state '\").concat(fromState1, \"'\").concat(fromState2 ? ' or \\'' + fromState2 + '\\'' : '', \", was '\").concat(this._state, \"'.\"));\n        }\n      };\n      ZoneTask.prototype.toString = function () {\n        if (this.data && typeof this.data.handleId !== 'undefined') {\n          return this.data.handleId.toString();\n        } else {\n          return Object.prototype.toString.call(this);\n        }\n      };\n      // add toJSON method to prevent cyclic error when\n      // call JSON.stringify(zoneTask)\n      ZoneTask.prototype.toJSON = function () {\n        return {\n          type: this.type,\n          state: this.state,\n          source: this.source,\n          zone: this.zone.name,\n          runCount: this.runCount\n        };\n      };\n      return ZoneTask;\n    }();\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    var symbolSetTimeout = __symbol__('setTimeout');\n    var symbolPromise = __symbol__('Promise');\n    var symbolThen = __symbol__('then');\n    var _microTaskQueue = [];\n    var _isDrainingMicrotaskQueue = false;\n    var nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n      if (!nativeMicroTaskQueuePromise) {\n        if (global[symbolPromise]) {\n          nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n        }\n      }\n      if (nativeMicroTaskQueuePromise) {\n        var nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n        if (!nativeThen) {\n          // native Promise is not patchable, we need to use `then` directly\n          // issue 1078\n          nativeThen = nativeMicroTaskQueuePromise['then'];\n        }\n        nativeThen.call(nativeMicroTaskQueuePromise, func);\n      } else {\n        global[symbolSetTimeout](func, 0);\n      }\n    }\n    function scheduleMicroTask(task) {\n      // if we are not running in any task, and there has not been anything scheduled\n      // we must bootstrap the initial task creation by manually scheduling the drain\n      if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n        // We are not running in Task, so we need to kickstart the microtask queue.\n        nativeScheduleMicroTask(drainMicroTaskQueue);\n      }\n      task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n      if (!_isDrainingMicrotaskQueue) {\n        _isDrainingMicrotaskQueue = true;\n        while (_microTaskQueue.length) {\n          var queue = _microTaskQueue;\n          _microTaskQueue = [];\n          for (var i = 0; i < queue.length; i++) {\n            var task = queue[i];\n            try {\n              task.zone.runTask(task, null, null);\n            } catch (error) {\n              _api.onUnhandledError(error);\n            }\n          }\n        }\n        _api.microtaskDrainDone();\n        _isDrainingMicrotaskQueue = false;\n      }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    var NO_ZONE = {\n      name: 'NO ZONE'\n    };\n    var notScheduled = 'notScheduled',\n      scheduling = 'scheduling',\n      scheduled = 'scheduled',\n      running = 'running',\n      canceling = 'canceling',\n      unknown = 'unknown';\n    var microTask = 'microTask',\n      macroTask = 'macroTask',\n      eventTask = 'eventTask';\n    var patches = {};\n    var _api = {\n      symbol: __symbol__,\n      currentZoneFrame: function () {\n        return _currentZoneFrame;\n      },\n      onUnhandledError: noop,\n      microtaskDrainDone: noop,\n      scheduleMicroTask: scheduleMicroTask,\n      showUncaughtError: function () {\n        return !Zone[__symbol__('ignoreConsoleErrorUncaughtError')];\n      },\n      patchEventTarget: function () {\n        return [];\n      },\n      patchOnProperties: noop,\n      patchMethod: function () {\n        return noop;\n      },\n      bindArguments: function () {\n        return [];\n      },\n      patchThen: function () {\n        return noop;\n      },\n      patchMacroTask: function () {\n        return noop;\n      },\n      patchEventPrototype: function () {\n        return noop;\n      },\n      isIEOrEdge: function () {\n        return false;\n      },\n      getGlobalObjects: function () {\n        return undefined;\n      },\n      ObjectDefineProperty: function () {\n        return noop;\n      },\n      ObjectGetOwnPropertyDescriptor: function () {\n        return undefined;\n      },\n      ObjectCreate: function () {\n        return undefined;\n      },\n      ArraySlice: function () {\n        return [];\n      },\n      patchClass: function () {\n        return noop;\n      },\n      wrapWithCurrentZone: function () {\n        return noop;\n      },\n      filterProperties: function () {\n        return [];\n      },\n      attachOriginToPatched: function () {\n        return noop;\n      },\n      _redefineProperty: function () {\n        return noop;\n      },\n      patchCallbacks: function () {\n        return noop;\n      },\n      nativeScheduleMicroTask: nativeScheduleMicroTask\n    };\n    var _currentZoneFrame = {\n      parent: null,\n      zone: new Zone(null, null)\n    };\n    var _currentTask = null;\n    var _numberOfNestedTaskFrames = 0;\n    function noop() {}\n    performanceMeasure('Zone', 'Zone');\n    return global['Zone'] = Zone;\n  })(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  /**\n   * Suppress closure compiler errors about unknown 'Zone' variable\n   * @fileoverview\n   * @suppress {undefinedVars,globalThis,missingRequire}\n   */\n  /// <reference types=\"node\"/>\n  // issue #989, to reduce bundle size, use short name\n  /** Object.getOwnPropertyDescriptor */\n  var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  /** Object.defineProperty */\n  var ObjectDefineProperty = Object.defineProperty;\n  /** Object.getPrototypeOf */\n  var ObjectGetPrototypeOf = Object.getPrototypeOf;\n  /** Object.create */\n  var ObjectCreate = Object.create;\n  /** Array.prototype.slice */\n  var ArraySlice = Array.prototype.slice;\n  /** addEventListener string const */\n  var ADD_EVENT_LISTENER_STR = 'addEventListener';\n  /** removeEventListener string const */\n  var REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n  /** zoneSymbol addEventListener */\n  var ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n  /** zoneSymbol removeEventListener */\n  var ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n  /** true string const */\n  var TRUE_STR = 'true';\n  /** false string const */\n  var FALSE_STR = 'false';\n  /** Zone symbol prefix string const. */\n  var ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\n  function wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n  }\n  function scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n  }\n  var zoneSymbol$1 = Zone.__symbol__;\n  var isWindowExists = typeof window !== 'undefined';\n  var internalWindow = isWindowExists ? window : undefined;\n  var _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\n  var REMOVE_ATTRIBUTE = 'removeAttribute';\n  function bindArguments(args, source) {\n    for (var i = args.length - 1; i >= 0; i--) {\n      if (typeof args[i] === 'function') {\n        args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n      }\n    }\n    return args;\n  }\n  function patchPrototype(prototype, fnNames) {\n    var source = prototype.constructor['name'];\n    var _loop_1 = function (i) {\n      var name_1 = fnNames[i];\n      var delegate = prototype[name_1];\n      if (delegate) {\n        var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name_1);\n        if (!isPropertyWritable(prototypeDesc)) {\n          return \"continue\";\n        }\n        prototype[name_1] = function (delegate) {\n          var patched = function () {\n            return delegate.apply(this, bindArguments(arguments, source + '.' + name_1));\n          };\n          attachOriginToPatched(patched, delegate);\n          return patched;\n        }(delegate);\n      }\n    };\n    for (var i = 0; i < fnNames.length; i++) {\n      _loop_1(i);\n    }\n  }\n  function isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n      return true;\n    }\n    if (propertyDesc.writable === false) {\n      return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n  }\n  var isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n  // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n  // this code.\n  var isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]';\n  var isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n  // we are in electron of nw, so we are both browser and nodejs\n  // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n  // this code.\n  var isMix = typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n  var zoneSymbolEventNames$1 = {};\n  var wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n      return;\n    }\n    var eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n      eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol$1('ON_PROPERTY' + event.type);\n    }\n    var target = this || event.target || _global;\n    var listener = target[eventNameSymbol];\n    var result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n      // window.onerror have different signature\n      // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n      // and onerror callback will prevent default when callback return true\n      var errorEvent = event;\n      result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n      if (result === true) {\n        event.preventDefault();\n      }\n    } else {\n      result = listener && listener.apply(this, arguments);\n      if (result != undefined && !result) {\n        event.preventDefault();\n      }\n    }\n    return result;\n  };\n  function patchProperty(obj, prop, prototype) {\n    var desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n      // when patch window object, use prototype to check prop exist or not\n      var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n      if (prototypeDesc) {\n        desc = {\n          enumerable: true,\n          configurable: true\n        };\n      }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n      return;\n    }\n    var onPropPatchedSymbol = zoneSymbol$1('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n      return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    var originalDescGet = desc.get;\n    var originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    var eventName = prop.slice(2);\n    var eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n      eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol$1('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n      // in some of windows's onproperty callback, this is undefined\n      // so we need to check it\n      var target = this;\n      if (!target && obj === _global) {\n        target = _global;\n      }\n      if (!target) {\n        return;\n      }\n      var previousValue = target[eventNameSymbol];\n      if (typeof previousValue === 'function') {\n        target.removeEventListener(eventName, wrapFn);\n      }\n      // issue #978, when onload handler was added before loading zone.js\n      // we should remove it with originalDescSet\n      originalDescSet && originalDescSet.call(target, null);\n      target[eventNameSymbol] = newValue;\n      if (typeof newValue === 'function') {\n        target.addEventListener(eventName, wrapFn, false);\n      }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n      // in some of windows's onproperty callback, this is undefined\n      // so we need to check it\n      var target = this;\n      if (!target && obj === _global) {\n        target = _global;\n      }\n      if (!target) {\n        return null;\n      }\n      var listener = target[eventNameSymbol];\n      if (listener) {\n        return listener;\n      } else if (originalDescGet) {\n        // result will be null when use inline event attribute,\n        // such as <button onclick=\"func();\">OK</button>\n        // because the onclick function is internal raw uncompiled handler\n        // the onclick will be evaluated when first time event was triggered or\n        // the property is accessed, https://github.com/angular/zone.js/issues/525\n        // so we should use original native get to retrieve the handler\n        var value = originalDescGet.call(this);\n        if (value) {\n          desc.set.call(this, value);\n          if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n            target.removeAttribute(prop);\n          }\n          return value;\n        }\n      }\n      return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n  }\n  function patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n      for (var i = 0; i < properties.length; i++) {\n        patchProperty(obj, 'on' + properties[i], prototype);\n      }\n    } else {\n      var onProperties = [];\n      for (var prop in obj) {\n        if (prop.slice(0, 2) == 'on') {\n          onProperties.push(prop);\n        }\n      }\n      for (var j = 0; j < onProperties.length; j++) {\n        patchProperty(obj, onProperties[j], prototype);\n      }\n    }\n  }\n  var originalInstanceKey = zoneSymbol$1('originalInstance');\n  // wrap some native API on `window`\n  function patchClass(className) {\n    var OriginalClass = _global[className];\n    if (!OriginalClass) return;\n    // keep original class in global\n    _global[zoneSymbol$1(className)] = OriginalClass;\n    _global[className] = function () {\n      var a = bindArguments(arguments, className);\n      switch (a.length) {\n        case 0:\n          this[originalInstanceKey] = new OriginalClass();\n          break;\n        case 1:\n          this[originalInstanceKey] = new OriginalClass(a[0]);\n          break;\n        case 2:\n          this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n          break;\n        case 3:\n          this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n          break;\n        case 4:\n          this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n          break;\n        default:\n          throw new Error('Arg list too long.');\n      }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    var instance = new OriginalClass(function () {});\n    var prop;\n    for (prop in instance) {\n      // https://bugs.webkit.org/show_bug.cgi?id=44721\n      if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n      (function (prop) {\n        if (typeof instance[prop] === 'function') {\n          _global[className].prototype[prop] = function () {\n            return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n          };\n        } else {\n          ObjectDefineProperty(_global[className].prototype, prop, {\n            set: function (fn) {\n              if (typeof fn === 'function') {\n                this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                // keep callback in wrapped function so we can\n                // use it in Function.prototype.toString to return\n                // the native one.\n                attachOriginToPatched(this[originalInstanceKey][prop], fn);\n              } else {\n                this[originalInstanceKey][prop] = fn;\n              }\n            },\n            get: function () {\n              return this[originalInstanceKey][prop];\n            }\n          });\n        }\n      })(prop);\n    }\n    for (prop in OriginalClass) {\n      if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n        _global[className][prop] = OriginalClass[prop];\n      }\n    }\n  }\n  function patchMethod(target, name, patchFn) {\n    var proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = target;\n    }\n    var delegateName = zoneSymbol$1(name);\n    var delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n      delegate = proto[delegateName] = proto[name];\n      // check whether proto[name] is writable\n      // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n      var desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n      if (isPropertyWritable(desc)) {\n        var patchDelegate_1 = patchFn(delegate, delegateName, name);\n        proto[name] = function () {\n          return patchDelegate_1(this, arguments);\n        };\n        attachOriginToPatched(proto[name], delegate);\n      }\n    }\n    return delegate;\n  }\n  // TODO: @JiaLiPassion, support cancel task later if necessary\n  function patchMacroTask(obj, funcName, metaCreator) {\n    var setNative = null;\n    function scheduleTask(task) {\n      var data = task.data;\n      data.args[data.cbIdx] = function () {\n        task.invoke.apply(this, arguments);\n      };\n      setNative.apply(data.target, data.args);\n      return task;\n    }\n    setNative = patchMethod(obj, funcName, function (delegate) {\n      return function (self, args) {\n        var meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n          return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        } else {\n          // cause an error by calling it directly.\n          return delegate.apply(self, args);\n        }\n      };\n    });\n  }\n  function attachOriginToPatched(patched, original) {\n    patched[zoneSymbol$1('OriginalDelegate')] = original;\n  }\n  var isDetectedIEOrEdge = false;\n  var ieOrEdge = false;\n  function isIE() {\n    try {\n      var ua = internalWindow.navigator.userAgent;\n      if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n        return true;\n      }\n    } catch (error) {}\n    return false;\n  }\n  function isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n      return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n      var ua = internalWindow.navigator.userAgent;\n      if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n        ieOrEdge = true;\n      }\n    } catch (error) {}\n    return ieOrEdge;\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  Zone.__load_patch('ZoneAwarePromise', function (global, Zone, api) {\n    var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    var ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n      if (obj && obj.toString === Object.prototype.toString) {\n        var className = obj.constructor && obj.constructor.name;\n        return (className ? className : '') + ': ' + JSON.stringify(obj);\n      }\n      return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    var __symbol__ = api.symbol;\n    var _uncaughtPromiseErrors = [];\n    var isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n    var symbolPromise = __symbol__('Promise');\n    var symbolThen = __symbol__('then');\n    var creationTrace = '__creationTrace__';\n    api.onUnhandledError = function (e) {\n      if (api.showUncaughtError()) {\n        var rejection = e && e.rejection;\n        if (rejection) {\n          console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n        } else {\n          console.error(e);\n        }\n      }\n    };\n    api.microtaskDrainDone = function () {\n      var _loop_2 = function () {\n        var uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n        try {\n          uncaughtPromiseError.zone.runGuarded(function () {\n            if (uncaughtPromiseError.throwOriginal) {\n              throw uncaughtPromiseError.rejection;\n            }\n            throw uncaughtPromiseError;\n          });\n        } catch (error) {\n          handleUnhandledRejection(error);\n        }\n      };\n      while (_uncaughtPromiseErrors.length) {\n        _loop_2();\n      }\n    };\n    var UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n      api.onUnhandledError(e);\n      try {\n        var handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n        if (typeof handler === 'function') {\n          handler.call(this, e);\n        }\n      } catch (err) {}\n    }\n    function isThenable(value) {\n      return value && value.then;\n    }\n    function forwardResolution(value) {\n      return value;\n    }\n    function forwardRejection(rejection) {\n      return ZoneAwarePromise.reject(rejection);\n    }\n    var symbolState = __symbol__('state');\n    var symbolValue = __symbol__('value');\n    var symbolFinally = __symbol__('finally');\n    var symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    var symbolParentPromiseState = __symbol__('parentPromiseState');\n    var source = 'Promise.then';\n    var UNRESOLVED = null;\n    var RESOLVED = true;\n    var REJECTED = false;\n    var REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n      return function (v) {\n        try {\n          resolvePromise(promise, state, v);\n        } catch (err) {\n          resolvePromise(promise, false, err);\n        }\n        // Do not return value or you will break the Promise spec.\n      };\n    }\n\n    var once = function () {\n      var wasCalled = false;\n      return function wrapper(wrappedFunction) {\n        return function () {\n          if (wasCalled) {\n            return;\n          }\n          wasCalled = true;\n          wrappedFunction.apply(null, arguments);\n        };\n      };\n    };\n    var TYPE_ERROR = 'Promise resolved with itself';\n    var CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n      var onceWrapper = once();\n      if (promise === value) {\n        throw new TypeError(TYPE_ERROR);\n      }\n      if (promise[symbolState] === UNRESOLVED) {\n        // should only get value.then once based on promise spec.\n        var then = null;\n        try {\n          if (typeof value === 'object' || typeof value === 'function') {\n            then = value && value.then;\n          }\n        } catch (err) {\n          onceWrapper(function () {\n            resolvePromise(promise, false, err);\n          })();\n          return promise;\n        }\n        // if (value instanceof ZoneAwarePromise) {\n        if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n          clearRejectedNoCatch(value);\n          resolvePromise(promise, value[symbolState], value[symbolValue]);\n        } else if (state !== REJECTED && typeof then === 'function') {\n          try {\n            then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n          } catch (err) {\n            onceWrapper(function () {\n              resolvePromise(promise, false, err);\n            })();\n          }\n        } else {\n          promise[symbolState] = state;\n          var queue = promise[symbolValue];\n          promise[symbolValue] = value;\n          if (promise[symbolFinally] === symbolFinally) {\n            // the promise is generated by Promise.prototype.finally\n            if (state === RESOLVED) {\n              // the state is resolved, should ignore the value\n              // and use parent promise value\n              promise[symbolState] = promise[symbolParentPromiseState];\n              promise[symbolValue] = promise[symbolParentPromiseValue];\n            }\n          }\n          // record task information in value when error occurs, so we can\n          // do some additional work such as render longStackTrace\n          if (state === REJECTED && value instanceof Error) {\n            // check if longStackTraceZone is here\n            var trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n            if (trace) {\n              // only keep the long stack trace into error when in longStackTraceZone\n              ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                configurable: true,\n                enumerable: false,\n                writable: true,\n                value: trace\n              });\n            }\n          }\n          for (var i = 0; i < queue.length;) {\n            scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n          }\n          if (queue.length == 0 && state == REJECTED) {\n            promise[symbolState] = REJECTED_NO_CATCH;\n            var uncaughtPromiseError = value;\n            try {\n              // Here we throws a new Error to print more readable error log\n              // and if the value is not an error, zone.js builds an `Error`\n              // Object here to attach the stack information.\n              throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n            } catch (err) {\n              uncaughtPromiseError = err;\n            }\n            if (isDisableWrappingUncaughtPromiseRejection) {\n              // If disable wrapping uncaught promise reject\n              // use the value instead of wrapping it.\n              uncaughtPromiseError.throwOriginal = true;\n            }\n            uncaughtPromiseError.rejection = value;\n            uncaughtPromiseError.promise = promise;\n            uncaughtPromiseError.zone = Zone.current;\n            uncaughtPromiseError.task = Zone.currentTask;\n            _uncaughtPromiseErrors.push(uncaughtPromiseError);\n            api.scheduleMicroTask(); // to make sure that it is running\n          }\n        }\n      }\n      // Resolving an already resolved promise is a noop.\n      return promise;\n    }\n    var REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n      if (promise[symbolState] === REJECTED_NO_CATCH) {\n        // if the promise is rejected no catch status\n        // and queue.length > 0, means there is a error handler\n        // here to handle the rejected promise, we should trigger\n        // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n        // eventHandler\n        try {\n          var handler = Zone[REJECTION_HANDLED_HANDLER];\n          if (handler && typeof handler === 'function') {\n            handler.call(this, {\n              rejection: promise[symbolValue],\n              promise: promise\n            });\n          }\n        } catch (err) {}\n        promise[symbolState] = REJECTED;\n        for (var i = 0; i < _uncaughtPromiseErrors.length; i++) {\n          if (promise === _uncaughtPromiseErrors[i].promise) {\n            _uncaughtPromiseErrors.splice(i, 1);\n          }\n        }\n      }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n      clearRejectedNoCatch(promise);\n      var promiseState = promise[symbolState];\n      var delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n      zone.scheduleMicroTask(source, function () {\n        try {\n          var parentPromiseValue = promise[symbolValue];\n          var isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n          if (isFinallyPromise) {\n            // if the promise is generated from finally call, keep parent promise's state and value\n            chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n            chainPromise[symbolParentPromiseState] = promiseState;\n          }\n          // should not pass value to finally callback\n          var value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n          resolvePromise(chainPromise, true, value);\n        } catch (error) {\n          // if error occurs, should always return this error\n          resolvePromise(chainPromise, false, error);\n        }\n      }, chainPromise);\n    }\n    var ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    var noop = function () {};\n    var AggregateError = global.AggregateError;\n    var ZoneAwarePromise = /** @class */function () {\n      function ZoneAwarePromise(executor) {\n        var promise = this;\n        if (!(promise instanceof ZoneAwarePromise)) {\n          throw new Error('Must be an instanceof Promise.');\n        }\n        promise[symbolState] = UNRESOLVED;\n        promise[symbolValue] = []; // queue;\n        try {\n          var onceWrapper = once();\n          executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n        } catch (error) {\n          resolvePromise(promise, false, error);\n        }\n      }\n      ZoneAwarePromise.toString = function () {\n        return ZONE_AWARE_PROMISE_TO_STRING;\n      };\n      ZoneAwarePromise.resolve = function (value) {\n        return resolvePromise(new this(null), RESOLVED, value);\n      };\n      ZoneAwarePromise.reject = function (error) {\n        return resolvePromise(new this(null), REJECTED, error);\n      };\n      ZoneAwarePromise.any = function (values) {\n        if (!values || typeof values[Symbol.iterator] !== 'function') {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        var promises = [];\n        var count = 0;\n        try {\n          for (var _i = 0, values_1 = values; _i < values_1.length; _i++) {\n            var v = values_1[_i];\n            count++;\n            promises.push(ZoneAwarePromise.resolve(v));\n          }\n        } catch (err) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        if (count === 0) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        var finished = false;\n        var errors = [];\n        return new ZoneAwarePromise(function (resolve, reject) {\n          for (var i = 0; i < promises.length; i++) {\n            promises[i].then(function (v) {\n              if (finished) {\n                return;\n              }\n              finished = true;\n              resolve(v);\n            }, function (err) {\n              errors.push(err);\n              count--;\n              if (count === 0) {\n                finished = true;\n                reject(new AggregateError(errors, 'All promises were rejected'));\n              }\n            });\n          }\n        });\n      };\n      ;\n      ZoneAwarePromise.race = function (values) {\n        var resolve;\n        var reject;\n        var promise = new this(function (res, rej) {\n          resolve = res;\n          reject = rej;\n        });\n        function onResolve(value) {\n          resolve(value);\n        }\n        function onReject(error) {\n          reject(error);\n        }\n        for (var _i = 0, values_2 = values; _i < values_2.length; _i++) {\n          var value = values_2[_i];\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n          value.then(onResolve, onReject);\n        }\n        return promise;\n      };\n      ZoneAwarePromise.all = function (values) {\n        return ZoneAwarePromise.allWithCallback(values);\n      };\n      ZoneAwarePromise.allSettled = function (values) {\n        var P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n        return P.allWithCallback(values, {\n          thenCallback: function (value) {\n            return {\n              status: 'fulfilled',\n              value: value\n            };\n          },\n          errorCallback: function (err) {\n            return {\n              status: 'rejected',\n              reason: err\n            };\n          }\n        });\n      };\n      ZoneAwarePromise.allWithCallback = function (values, callback) {\n        var resolve;\n        var reject;\n        var promise = new this(function (res, rej) {\n          resolve = res;\n          reject = rej;\n        });\n        // Start at 2 to prevent prematurely resolving if .then is called immediately.\n        var unresolvedCount = 2;\n        var valueIndex = 0;\n        var resolvedValues = [];\n        var _loop_3 = function (value) {\n          if (!isThenable(value)) {\n            value = this_1.resolve(value);\n          }\n          var curValueIndex = valueIndex;\n          try {\n            value.then(function (value) {\n              resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n              unresolvedCount--;\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }, function (err) {\n              if (!callback) {\n                reject(err);\n              } else {\n                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                unresolvedCount--;\n                if (unresolvedCount === 0) {\n                  resolve(resolvedValues);\n                }\n              }\n            });\n          } catch (thenErr) {\n            reject(thenErr);\n          }\n          unresolvedCount++;\n          valueIndex++;\n        };\n        var this_1 = this;\n        for (var _i = 0, values_3 = values; _i < values_3.length; _i++) {\n          var value = values_3[_i];\n          _loop_3(value);\n        }\n        // Make the unresolvedCount zero-based again.\n        unresolvedCount -= 2;\n        if (unresolvedCount === 0) {\n          resolve(resolvedValues);\n        }\n        return promise;\n      };\n      Object.defineProperty(ZoneAwarePromise.prototype, Symbol.toStringTag, {\n        get: function () {\n          return 'Promise';\n        },\n        enumerable: false,\n        configurable: true\n      });\n      Object.defineProperty(ZoneAwarePromise.prototype, Symbol.species, {\n        get: function () {\n          return ZoneAwarePromise;\n        },\n        enumerable: false,\n        configurable: true\n      });\n      ZoneAwarePromise.prototype.then = function (onFulfilled, onRejected) {\n        var _a;\n        // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n        // may be an object without a prototype (created through `Object.create(null)`); thus\n        // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n        // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n        // object and copies promise properties into that object (within the `getOrCreateLoad`\n        // function). The zone.js then checks if the resolved value has the `then` method and invokes\n        // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n        // properties of undefined (reading 'Symbol(Symbol.species)')`.\n        var C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = this.constructor || ZoneAwarePromise;\n        }\n        var chainPromise = new C(noop);\n        var zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n        }\n        return chainPromise;\n      };\n      ZoneAwarePromise.prototype.catch = function (onRejected) {\n        return this.then(null, onRejected);\n      };\n      ZoneAwarePromise.prototype.finally = function (onFinally) {\n        var _a;\n        // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n        var C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = ZoneAwarePromise;\n        }\n        var chainPromise = new C(noop);\n        chainPromise[symbolFinally] = symbolFinally;\n        var zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n        }\n        return chainPromise;\n      };\n      return ZoneAwarePromise;\n    }();\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    var NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    var symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n      var proto = Ctor.prototype;\n      var prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n      if (prop && (prop.writable === false || !prop.configurable)) {\n        // check Ctor.prototype.then propertyDescriptor is writable or not\n        // in meteor env, writable is false, we should ignore such case\n        return;\n      }\n      var originalThen = proto.then;\n      // Keep a reference to the original method.\n      proto[symbolThen] = originalThen;\n      Ctor.prototype.then = function (onResolve, onReject) {\n        var _this = this;\n        var wrapped = new ZoneAwarePromise(function (resolve, reject) {\n          originalThen.call(_this, resolve, reject);\n        });\n        return wrapped.then(onResolve, onReject);\n      };\n      Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n      return function (self, args) {\n        var resultPromise = fn.apply(self, args);\n        if (resultPromise instanceof ZoneAwarePromise) {\n          return resultPromise;\n        }\n        var ctor = resultPromise.constructor;\n        if (!ctor[symbolThenPatched]) {\n          patchThen(ctor);\n        }\n        return resultPromise;\n      };\n    }\n    if (NativePromise) {\n      patchThen(NativePromise);\n      patchMethod(global, 'fetch', function (delegate) {\n        return zoneify(delegate);\n      });\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n  });\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  // override Function.prototype.toString to make zone.js patched function\n  // look like native function\n  Zone.__load_patch('toString', function (global) {\n    // patch Func.prototype.toString to let them look like native\n    var originalFunctionToString = Function.prototype.toString;\n    var ORIGINAL_DELEGATE_SYMBOL = zoneSymbol$1('OriginalDelegate');\n    var PROMISE_SYMBOL = zoneSymbol$1('Promise');\n    var ERROR_SYMBOL = zoneSymbol$1('Error');\n    var newFunctionToString = function toString() {\n      if (typeof this === 'function') {\n        var originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n        if (originalDelegate) {\n          if (typeof originalDelegate === 'function') {\n            return originalFunctionToString.call(originalDelegate);\n          } else {\n            return Object.prototype.toString.call(originalDelegate);\n          }\n        }\n        if (this === Promise) {\n          var nativePromise = global[PROMISE_SYMBOL];\n          if (nativePromise) {\n            return originalFunctionToString.call(nativePromise);\n          }\n        }\n        if (this === Error) {\n          var nativeError = global[ERROR_SYMBOL];\n          if (nativeError) {\n            return originalFunctionToString.call(nativeError);\n          }\n        }\n      }\n      return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    var originalObjectToString = Object.prototype.toString;\n    var PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n      if (typeof Promise === 'function' && this instanceof Promise) {\n        return PROMISE_OBJECT_TO_STRING;\n      }\n      return originalObjectToString.call(this);\n    };\n  });\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  var passiveSupported = false;\n  if (typeof window !== 'undefined') {\n    try {\n      var options = Object.defineProperty({}, 'passive', {\n        get: function () {\n          passiveSupported = true;\n        }\n      });\n      // Note: We pass the `options` object as the event handler too. This is not compatible with the\n      // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n      // without an actual handler.\n      window.addEventListener('test', options, options);\n      window.removeEventListener('test', options, options);\n    } catch (err) {\n      passiveSupported = false;\n    }\n  }\n  // an identifier to tell ZoneTask do not create a new invoke closure\n  var OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true\n  };\n  var zoneSymbolEventNames = {};\n  var globalSources = {};\n  var EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\n  var IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol$1('propagationStopped');\n  function prepareEventNames(eventName, eventNameToString) {\n    var falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    var trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n  function patchEventTarget(_global, api, apis, patchOptions) {\n    var ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n    var REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n    var LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n    var REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n    var zoneSymbolAddEventListener = zoneSymbol$1(ADD_EVENT_LISTENER);\n    var ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    var PREPEND_EVENT_LISTENER = 'prependListener';\n    var PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    var invokeTask = function (task, target, event) {\n      // for better performance, check isRemoved which is set\n      // by removeEventListener\n      if (task.isRemoved) {\n        return;\n      }\n      var delegate = task.callback;\n      if (typeof delegate === 'object' && delegate.handleEvent) {\n        // create the bind version of handleEvent when invoke\n        task.callback = function (event) {\n          return delegate.handleEvent(event);\n        };\n        task.originalDelegate = delegate;\n      }\n      // invoke static task.invoke\n      // need to try/catch error here, otherwise, the error in one event listener\n      // will break the executions of the other event listeners. Also error will\n      // not remove the event listener when `once` options is true.\n      var error;\n      try {\n        task.invoke(task, target, [event]);\n      } catch (err) {\n        error = err;\n      }\n      var options = task.options;\n      if (options && typeof options === 'object' && options.once) {\n        // if options.once is true, after invoke once remove listener here\n        // only browser need to do this, nodejs eventEmitter will cal removeListener\n        // inside EventEmitter.once\n        var delegate_1 = task.originalDelegate ? task.originalDelegate : task.callback;\n        target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate_1, options);\n      }\n      return error;\n    };\n    function globalCallback(context, event, isCapture) {\n      // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n      // event will be undefined, so we need to use window.event\n      event = event || _global.event;\n      if (!event) {\n        return;\n      }\n      // event.target is needed for Samsung TV and SourceBuffer\n      // || global is needed https://github.com/angular/zone.js/issues/190\n      var target = context || event.target || _global;\n      var tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n      if (tasks) {\n        var errors = [];\n        // invoke all tasks which attached to current target with given event.type and capture = false\n        // for performance concern, if task.length === 1, just invoke\n        if (tasks.length === 1) {\n          var err = invokeTask(tasks[0], target, event);\n          err && errors.push(err);\n        } else {\n          // https://github.com/angular/zone.js/issues/836\n          // copy the tasks array before invoke, to avoid\n          // the callback will remove itself or other listener\n          var copyTasks = tasks.slice();\n          for (var i = 0; i < copyTasks.length; i++) {\n            if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n              break;\n            }\n            var err = invokeTask(copyTasks[i], target, event);\n            err && errors.push(err);\n          }\n        }\n        // Since there is only one error, we don't need to schedule microTask\n        // to throw the error.\n        if (errors.length === 1) {\n          throw errors[0];\n        } else {\n          var _loop_4 = function (i) {\n            var err = errors[i];\n            api.nativeScheduleMicroTask(function () {\n              throw err;\n            });\n          };\n          for (var i = 0; i < errors.length; i++) {\n            _loop_4(i);\n          }\n        }\n      }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    var globalZoneAwareCallback = function (event) {\n      return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    var globalZoneAwareCaptureCallback = function (event) {\n      return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n      if (!obj) {\n        return false;\n      }\n      var useGlobalCallback = true;\n      if (patchOptions && patchOptions.useG !== undefined) {\n        useGlobalCallback = patchOptions.useG;\n      }\n      var validateHandler = patchOptions && patchOptions.vh;\n      var checkDuplicate = true;\n      if (patchOptions && patchOptions.chkDup !== undefined) {\n        checkDuplicate = patchOptions.chkDup;\n      }\n      var returnTarget = false;\n      if (patchOptions && patchOptions.rt !== undefined) {\n        returnTarget = patchOptions.rt;\n      }\n      var proto = obj;\n      while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n        proto = ObjectGetPrototypeOf(proto);\n      }\n      if (!proto && obj[ADD_EVENT_LISTENER]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = obj;\n      }\n      if (!proto) {\n        return false;\n      }\n      if (proto[zoneSymbolAddEventListener]) {\n        return false;\n      }\n      var eventNameToString = patchOptions && patchOptions.eventNameToString;\n      // a shared global taskData to pass data for scheduleEventTask\n      // so we do not need to create a new object just for pass some data\n      var taskData = {};\n      var nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n      var nativeRemoveEventListener = proto[zoneSymbol$1(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n      var nativeListeners = proto[zoneSymbol$1(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n      var nativeRemoveAllListeners = proto[zoneSymbol$1(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n      var nativePrependEventListener;\n      if (patchOptions && patchOptions.prepend) {\n        nativePrependEventListener = proto[zoneSymbol$1(patchOptions.prepend)] = proto[patchOptions.prepend];\n      }\n      /**\n       * This util function will build an option object with passive option\n       * to handle all possible input from the user.\n       */\n      function buildEventListenerOptions(options, passive) {\n        if (!passiveSupported && typeof options === 'object' && options) {\n          // doesn't support passive but user want to pass an object as options.\n          // this will not work on some old browser, so we just pass a boolean\n          // as useCapture parameter\n          return !!options.capture;\n        }\n        if (!passiveSupported || !passive) {\n          return options;\n        }\n        if (typeof options === 'boolean') {\n          return {\n            capture: options,\n            passive: true\n          };\n        }\n        if (!options) {\n          return {\n            passive: true\n          };\n        }\n        if (typeof options === 'object' && options.passive !== false) {\n          return Object.assign(Object.assign({}, options), {\n            passive: true\n          });\n        }\n        return options;\n      }\n      var customScheduleGlobal = function (task) {\n        // if there is already a task for the eventName + capture,\n        // just return, because we use the shared globalZoneAwareCallback here.\n        if (taskData.isExisting) {\n          return;\n        }\n        return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n      };\n      var customCancelGlobal = function (task) {\n        // if task is not marked as isRemoved, this call is directly\n        // from Zone.prototype.cancelTask, we should remove the task\n        // from tasksList of target first\n        if (!task.isRemoved) {\n          var symbolEventNames = zoneSymbolEventNames[task.eventName];\n          var symbolEventName = void 0;\n          if (symbolEventNames) {\n            symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n          }\n          var existingTasks = symbolEventName && task.target[symbolEventName];\n          if (existingTasks) {\n            for (var i = 0; i < existingTasks.length; i++) {\n              var existingTask = existingTasks[i];\n              if (existingTask === task) {\n                existingTasks.splice(i, 1);\n                // set isRemoved to data for faster invokeTask check\n                task.isRemoved = true;\n                if (existingTasks.length === 0) {\n                  // all tasks for the eventName + capture have gone,\n                  // remove globalZoneAwareCallback and remove the task cache from target\n                  task.allRemoved = true;\n                  task.target[symbolEventName] = null;\n                }\n                break;\n              }\n            }\n          }\n        }\n        // if all tasks for the eventName + capture have gone,\n        // we will really remove the global event callback,\n        // if not, return\n        if (!task.allRemoved) {\n          return;\n        }\n        return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n      };\n      var customScheduleNonGlobal = function (task) {\n        return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n      };\n      var customSchedulePrepend = function (task) {\n        return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n      };\n      var customCancelNonGlobal = function (task) {\n        return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n      };\n      var customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n      var customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n      var compareTaskCallbackVsDelegate = function (task, delegate) {\n        var typeOfDelegate = typeof delegate;\n        return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n      };\n      var compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n      var unpatchedEvents = Zone[zoneSymbol$1('UNPATCHED_EVENTS')];\n      var passiveEvents = _global[zoneSymbol$1('PASSIVE_EVENTS')];\n      var makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget, prepend) {\n        if (returnTarget === void 0) {\n          returnTarget = false;\n        }\n        if (prepend === void 0) {\n          prepend = false;\n        }\n        return function () {\n          var target = this || _global;\n          var eventName = arguments[0];\n          if (patchOptions && patchOptions.transferEventName) {\n            eventName = patchOptions.transferEventName(eventName);\n          }\n          var delegate = arguments[1];\n          if (!delegate) {\n            return nativeListener.apply(this, arguments);\n          }\n          if (isNode && eventName === 'uncaughtException') {\n            // don't patch uncaughtException of nodejs to prevent endless loop\n            return nativeListener.apply(this, arguments);\n          }\n          // don't create the bind delegate function for handleEvent\n          // case here to improve addEventListener performance\n          // we will create the bind delegate when invoke\n          var isHandleEvent = false;\n          if (typeof delegate !== 'function') {\n            if (!delegate.handleEvent) {\n              return nativeListener.apply(this, arguments);\n            }\n            isHandleEvent = true;\n          }\n          if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n            return;\n          }\n          var passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n          var options = buildEventListenerOptions(arguments[2], passive);\n          if (unpatchedEvents) {\n            // check unpatched list\n            for (var i = 0; i < unpatchedEvents.length; i++) {\n              if (eventName === unpatchedEvents[i]) {\n                if (passive) {\n                  return nativeListener.call(target, eventName, delegate, options);\n                } else {\n                  return nativeListener.apply(this, arguments);\n                }\n              }\n            }\n          }\n          var capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n          var once = options && typeof options === 'object' ? options.once : false;\n          var zone = Zone.current;\n          var symbolEventNames = zoneSymbolEventNames[eventName];\n          if (!symbolEventNames) {\n            prepareEventNames(eventName, eventNameToString);\n            symbolEventNames = zoneSymbolEventNames[eventName];\n          }\n          var symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n          var existingTasks = target[symbolEventName];\n          var isExisting = false;\n          if (existingTasks) {\n            // already have task registered\n            isExisting = true;\n            if (checkDuplicate) {\n              for (var i = 0; i < existingTasks.length; i++) {\n                if (compare(existingTasks[i], delegate)) {\n                  // same callback, same capture, same event name, just return\n                  return;\n                }\n              }\n            }\n          } else {\n            existingTasks = target[symbolEventName] = [];\n          }\n          var source;\n          var constructorName = target.constructor['name'];\n          var targetSource = globalSources[constructorName];\n          if (targetSource) {\n            source = targetSource[eventName];\n          }\n          if (!source) {\n            source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n          }\n          // do not create a new object as task.data to pass those things\n          // just use the global shared one\n          taskData.options = options;\n          if (once) {\n            // if addEventListener with once options, we don't pass it to\n            // native addEventListener, instead we keep the once setting\n            // and handle ourselves.\n            taskData.options.once = false;\n          }\n          taskData.target = target;\n          taskData.capture = capture;\n          taskData.eventName = eventName;\n          taskData.isExisting = isExisting;\n          var data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n          // keep taskData into data to allow onScheduleEventTask to access the task information\n          if (data) {\n            data.taskData = taskData;\n          }\n          var task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n          // should clear taskData.target to avoid memory leak\n          // issue, https://github.com/angular/angular/issues/20442\n          taskData.target = null;\n          // need to clear up taskData because it is a global object\n          if (data) {\n            data.taskData = null;\n          }\n          // have to save those information to task in case\n          // application may call task.zone.cancelTask() directly\n          if (once) {\n            options.once = true;\n          }\n          if (!(!passiveSupported && typeof task.options === 'boolean')) {\n            // if not support passive, and we pass an option object\n            // to addEventListener, we should save the options to task\n            task.options = options;\n          }\n          task.target = target;\n          task.capture = capture;\n          task.eventName = eventName;\n          if (isHandleEvent) {\n            // save original delegate for compare to check duplicate\n            task.originalDelegate = delegate;\n          }\n          if (!prepend) {\n            existingTasks.push(task);\n          } else {\n            existingTasks.unshift(task);\n          }\n          if (returnTarget) {\n            return target;\n          }\n        };\n      };\n      proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n      if (nativePrependEventListener) {\n        proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n      }\n      proto[REMOVE_EVENT_LISTENER] = function () {\n        var target = this || _global;\n        var eventName = arguments[0];\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        var options = arguments[2];\n        var capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        var delegate = arguments[1];\n        if (!delegate) {\n          return nativeRemoveEventListener.apply(this, arguments);\n        }\n        if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n          return;\n        }\n        var symbolEventNames = zoneSymbolEventNames[eventName];\n        var symbolEventName;\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        }\n        var existingTasks = symbolEventName && target[symbolEventName];\n        if (existingTasks) {\n          for (var i = 0; i < existingTasks.length; i++) {\n            var existingTask = existingTasks[i];\n            if (compare(existingTask, delegate)) {\n              existingTasks.splice(i, 1);\n              // set isRemoved to data for faster invokeTask check\n              existingTask.isRemoved = true;\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                existingTask.allRemoved = true;\n                target[symbolEventName] = null;\n                // in the target, we have an event listener which is added by on_property\n                // such as target.onclick = function() {}, so we need to clear this internal\n                // property too if all delegates all removed\n                if (typeof eventName === 'string') {\n                  var onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                  target[onPropertySymbol] = null;\n                }\n              }\n              existingTask.zone.cancelTask(existingTask);\n              if (returnTarget) {\n                return target;\n              }\n              return;\n            }\n          }\n        }\n        // issue 930, didn't find the event name or callback\n        // from zone kept existingTasks, the callback maybe\n        // added outside of zone, we need to call native removeEventListener\n        // to try to remove it.\n        return nativeRemoveEventListener.apply(this, arguments);\n      };\n      proto[LISTENERS_EVENT_LISTENER] = function () {\n        var target = this || _global;\n        var eventName = arguments[0];\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        var listeners = [];\n        var tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n        for (var i = 0; i < tasks.length; i++) {\n          var task = tasks[i];\n          var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n          listeners.push(delegate);\n        }\n        return listeners;\n      };\n      proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n        var target = this || _global;\n        var eventName = arguments[0];\n        if (!eventName) {\n          var keys = Object.keys(target);\n          for (var i = 0; i < keys.length; i++) {\n            var prop = keys[i];\n            var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            var evtName = match && match[1];\n            // in nodejs EventEmitter, removeListener event is\n            // used for monitoring the removeListener call,\n            // so just keep removeListener eventListener until\n            // all other eventListeners are removed\n            if (evtName && evtName !== 'removeListener') {\n              this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n            }\n          }\n          // remove removeListener listener finally\n          this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n        } else {\n          if (patchOptions && patchOptions.transferEventName) {\n            eventName = patchOptions.transferEventName(eventName);\n          }\n          var symbolEventNames = zoneSymbolEventNames[eventName];\n          if (symbolEventNames) {\n            var symbolEventName = symbolEventNames[FALSE_STR];\n            var symbolCaptureEventName = symbolEventNames[TRUE_STR];\n            var tasks = target[symbolEventName];\n            var captureTasks = target[symbolCaptureEventName];\n            if (tasks) {\n              var removeTasks = tasks.slice();\n              for (var i = 0; i < removeTasks.length; i++) {\n                var task = removeTasks[i];\n                var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n              }\n            }\n            if (captureTasks) {\n              var removeTasks = captureTasks.slice();\n              for (var i = 0; i < removeTasks.length; i++) {\n                var task = removeTasks[i];\n                var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n              }\n            }\n          }\n        }\n        if (returnTarget) {\n          return this;\n        }\n      };\n      // for native toString patch\n      attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n      attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n      if (nativeRemoveAllListeners) {\n        attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n      }\n      if (nativeListeners) {\n        attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n      }\n      return true;\n    }\n    var results = [];\n    for (var i = 0; i < apis.length; i++) {\n      results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n  }\n  function findEventTasks(target, eventName) {\n    if (!eventName) {\n      var foundTasks = [];\n      for (var prop in target) {\n        var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n        var evtName = match && match[1];\n        if (evtName && (!eventName || evtName === eventName)) {\n          var tasks = target[prop];\n          if (tasks) {\n            for (var i = 0; i < tasks.length; i++) {\n              foundTasks.push(tasks[i]);\n            }\n          }\n        }\n      }\n      return foundTasks;\n    }\n    var symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n      prepareEventNames(eventName);\n      symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    var captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    var captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n      return captureTrueTasks ? captureTrueTasks.slice() : [];\n    } else {\n      return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n    }\n  }\n  function patchEventPrototype(global, api) {\n    var Event = global['Event'];\n    if (Event && Event.prototype) {\n      api.patchMethod(Event.prototype, 'stopImmediatePropagation', function (delegate) {\n        return function (self, args) {\n          self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n          // we need to call the native stopImmediatePropagation\n          // in case in some hybrid application, some part of\n          // application will be controlled by zone, some are not\n          delegate && delegate.apply(self, args);\n        };\n      });\n    }\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function patchCallbacks(api, target, targetName, method, callbacks) {\n    var symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n      return;\n    }\n    var nativeDelegate = target[symbol] = target[method];\n    target[method] = function (name, opts, options) {\n      if (opts && opts.prototype) {\n        callbacks.forEach(function (callback) {\n          var source = \"\".concat(targetName, \".\").concat(method, \"::\") + callback;\n          var prototype = opts.prototype;\n          // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n          // `customElements.define`. We explicitly wrap the patching code into try-catch since\n          // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n          // make those properties non-writable. This means that patching callback will throw an error\n          // `cannot assign to read-only property`. See this code as an example:\n          // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n          // We don't want to stop the application rendering if we couldn't patch some\n          // callback, e.g. `attributeChangedCallback`.\n          try {\n            if (prototype.hasOwnProperty(callback)) {\n              var descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n              if (descriptor && descriptor.value) {\n                descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                api._redefineProperty(opts.prototype, callback, descriptor);\n              } else if (prototype[callback]) {\n                prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n              }\n            } else if (prototype[callback]) {\n              prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n            }\n          } catch (_a) {\n            // Note: we leave the catch block empty since there's no way to handle the error related\n            // to non-writable property.\n          }\n        });\n      }\n      return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n      return onProperties;\n    }\n    var tip = ignoreProperties.filter(function (ip) {\n      return ip.target === target;\n    });\n    if (!tip || tip.length === 0) {\n      return onProperties;\n    }\n    var targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter(function (op) {\n      return targetIgnoreProperties.indexOf(op) === -1;\n    });\n  }\n  function patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n      return;\n    }\n    var filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n  }\n  /**\n   * Get all event name properties which the event name startsWith `on`\n   * from the target object itself, inherited properties are not considered.\n   */\n  function getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target).filter(function (name) {\n      return name.startsWith('on') && name.length > 2;\n    }).map(function (name) {\n      return name.substring(2);\n    });\n  }\n  function propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n      return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n      // events are already been patched by legacy patch.\n      return;\n    }\n    var ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    var patchTargets = [];\n    if (isBrowser) {\n      var internalWindow_1 = window;\n      patchTargets = patchTargets.concat(['Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement', 'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker']);\n      var ignoreErrorProperties = isIE() ? [{\n        target: internalWindow_1,\n        ignoreProperties: ['error']\n      }] : [];\n      // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n      // so we need to pass WindowPrototype to check onProp exist or not\n      patchFilteredProperties(internalWindow_1, getOnEventNames(internalWindow_1), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow_1));\n    }\n    patchTargets = patchTargets.concat(['XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest', 'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket']);\n    for (var i = 0; i < patchTargets.length; i++) {\n      var target = _global[patchTargets[i]];\n      target && target.prototype && patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  Zone.__load_patch('util', function (global, Zone, api) {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    var eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n    // define which events will not be patched by `Zone.js`.\n    // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n    // the name consistent with angular repo.\n    // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n    // backwards compatibility.\n    var SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    var SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n      global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n      Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = function () {\n      return {\n        globalSources: globalSources,\n        zoneSymbolEventNames: zoneSymbolEventNames,\n        eventNames: eventNames,\n        isBrowser: isBrowser,\n        isMix: isMix,\n        isNode: isNode,\n        TRUE_STR: TRUE_STR,\n        FALSE_STR: FALSE_STR,\n        ZONE_SYMBOL_PREFIX: ZONE_SYMBOL_PREFIX,\n        ADD_EVENT_LISTENER_STR: ADD_EVENT_LISTENER_STR,\n        REMOVE_EVENT_LISTENER_STR: REMOVE_EVENT_LISTENER_STR\n      };\n    };\n  });\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  /*\n   * This is necessary for Chrome and Chrome mobile, to enable\n   * things like redefining `createdCallback` on an element.\n   */\n  var zoneSymbol;\n  var _defineProperty;\n  var _getOwnPropertyDescriptor;\n  var _create;\n  var unconfigurablesKey;\n  function propertyPatch() {\n    zoneSymbol = Zone.__symbol__;\n    _defineProperty = Object[zoneSymbol('defineProperty')] = Object.defineProperty;\n    _getOwnPropertyDescriptor = Object[zoneSymbol('getOwnPropertyDescriptor')] = Object.getOwnPropertyDescriptor;\n    _create = Object.create;\n    unconfigurablesKey = zoneSymbol('unconfigurables');\n    Object.defineProperty = function (obj, prop, desc) {\n      if (isUnconfigurable(obj, prop)) {\n        throw new TypeError('Cannot assign to read only property \\'' + prop + '\\' of ' + obj);\n      }\n      var originalConfigurableFlag = desc.configurable;\n      if (prop !== 'prototype') {\n        desc = rewriteDescriptor(obj, prop, desc);\n      }\n      return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n    };\n    Object.defineProperties = function (obj, props) {\n      Object.keys(props).forEach(function (prop) {\n        Object.defineProperty(obj, prop, props[prop]);\n      });\n      for (var _i = 0, _b = Object.getOwnPropertySymbols(props); _i < _b.length; _i++) {\n        var sym = _b[_i];\n        var desc = Object.getOwnPropertyDescriptor(props, sym);\n        // Since `Object.getOwnPropertySymbols` returns *all* symbols,\n        // including non-enumerable ones, retrieve property descriptor and check\n        // enumerability there. Proceed with the rewrite only when a property is\n        // enumerable to make the logic consistent with the way regular\n        // properties are retrieved (via `Object.keys`, which respects\n        // `enumerable: false` flag). More information:\n        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Enumerability_and_ownership_of_properties#retrieval\n        if (desc === null || desc === void 0 ? void 0 : desc.enumerable) {\n          Object.defineProperty(obj, sym, props[sym]);\n        }\n      }\n      return obj;\n    };\n    Object.create = function (proto, propertiesObject) {\n      if (typeof propertiesObject === 'object' && !Object.isFrozen(propertiesObject)) {\n        Object.keys(propertiesObject).forEach(function (prop) {\n          propertiesObject[prop] = rewriteDescriptor(proto, prop, propertiesObject[prop]);\n        });\n      }\n      return _create(proto, propertiesObject);\n    };\n    Object.getOwnPropertyDescriptor = function (obj, prop) {\n      var desc = _getOwnPropertyDescriptor(obj, prop);\n      if (desc && isUnconfigurable(obj, prop)) {\n        desc.configurable = false;\n      }\n      return desc;\n    };\n  }\n  function _redefineProperty(obj, prop, desc) {\n    var originalConfigurableFlag = desc.configurable;\n    desc = rewriteDescriptor(obj, prop, desc);\n    return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n  }\n  function isUnconfigurable(obj, prop) {\n    return obj && obj[unconfigurablesKey] && obj[unconfigurablesKey][prop];\n  }\n  function rewriteDescriptor(obj, prop, desc) {\n    // issue-927, if the desc is frozen, don't try to change the desc\n    if (!Object.isFrozen(desc)) {\n      desc.configurable = true;\n    }\n    if (!desc.configurable) {\n      // issue-927, if the obj is frozen, don't try to set the desc to obj\n      if (!obj[unconfigurablesKey] && !Object.isFrozen(obj)) {\n        _defineProperty(obj, unconfigurablesKey, {\n          writable: true,\n          value: {}\n        });\n      }\n      if (obj[unconfigurablesKey]) {\n        obj[unconfigurablesKey][prop] = true;\n      }\n    }\n    return desc;\n  }\n  function _tryDefineProperty(obj, prop, desc, originalConfigurableFlag) {\n    try {\n      return _defineProperty(obj, prop, desc);\n    } catch (error) {\n      if (desc.configurable) {\n        // In case of errors, when the configurable flag was likely set by rewriteDescriptor(),\n        // let's retry with the original flag value\n        if (typeof originalConfigurableFlag == 'undefined') {\n          delete desc.configurable;\n        } else {\n          desc.configurable = originalConfigurableFlag;\n        }\n        try {\n          return _defineProperty(obj, prop, desc);\n        } catch (error) {\n          var swallowError = false;\n          if (prop === 'createdCallback' || prop === 'attachedCallback' || prop === 'detachedCallback' || prop === 'attributeChangedCallback') {\n            // We only swallow the error in registerElement patch\n            // this is the work around since some applications\n            // fail if we throw the error\n            swallowError = true;\n          }\n          if (!swallowError) {\n            throw error;\n          }\n          // TODO: @JiaLiPassion, Some application such as `registerElement` patch\n          // still need to swallow the error, in the future after these applications\n          // are updated, the following logic can be removed.\n          var descJson = null;\n          try {\n            descJson = JSON.stringify(desc);\n          } catch (error) {\n            descJson = desc.toString();\n          }\n          console.log(\"Attempting to configure '\".concat(prop, \"' with descriptor '\").concat(descJson, \"' on object '\").concat(obj, \"' and got error, giving up: \").concat(error));\n        }\n      } else {\n        throw error;\n      }\n    }\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function eventTargetLegacyPatch(_global, api) {\n    var _b = api.getGlobalObjects(),\n      eventNames = _b.eventNames,\n      globalSources = _b.globalSources,\n      zoneSymbolEventNames = _b.zoneSymbolEventNames,\n      TRUE_STR = _b.TRUE_STR,\n      FALSE_STR = _b.FALSE_STR,\n      ZONE_SYMBOL_PREFIX = _b.ZONE_SYMBOL_PREFIX;\n    var WTF_ISSUE_555 = 'Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video';\n    var NO_EVENT_TARGET = 'ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket'.split(',');\n    var EVENT_TARGET = 'EventTarget';\n    var apis = [];\n    var isWtf = _global['wtf'];\n    var WTF_ISSUE_555_ARRAY = WTF_ISSUE_555.split(',');\n    if (isWtf) {\n      // Workaround for: https://github.com/google/tracing-framework/issues/555\n      apis = WTF_ISSUE_555_ARRAY.map(function (v) {\n        return 'HTML' + v + 'Element';\n      }).concat(NO_EVENT_TARGET);\n    } else if (_global[EVENT_TARGET]) {\n      apis.push(EVENT_TARGET);\n    } else {\n      // Note: EventTarget is not available in all browsers,\n      // if it's not available, we instead patch the APIs in the IDL that inherit from EventTarget\n      apis = NO_EVENT_TARGET;\n    }\n    var isDisableIECheck = _global['__Zone_disable_IE_check'] || false;\n    var isEnableCrossContextCheck = _global['__Zone_enable_cross_context_check'] || false;\n    var ieOrEdge = api.isIEOrEdge();\n    var ADD_EVENT_LISTENER_SOURCE = '.addEventListener:';\n    var FUNCTION_WRAPPER = '[object FunctionWrapper]';\n    var BROWSER_TOOLS = 'function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }';\n    var pointerEventsMap = {\n      'MSPointerCancel': 'pointercancel',\n      'MSPointerDown': 'pointerdown',\n      'MSPointerEnter': 'pointerenter',\n      'MSPointerHover': 'pointerhover',\n      'MSPointerLeave': 'pointerleave',\n      'MSPointerMove': 'pointermove',\n      'MSPointerOut': 'pointerout',\n      'MSPointerOver': 'pointerover',\n      'MSPointerUp': 'pointerup'\n    };\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (var i = 0; i < eventNames.length; i++) {\n      var eventName = eventNames[i];\n      var falseEventName = eventName + FALSE_STR;\n      var trueEventName = eventName + TRUE_STR;\n      var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n      var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n      zoneSymbolEventNames[eventName] = {};\n      zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n      zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    //  predefine all task.source string\n    for (var i = 0; i < WTF_ISSUE_555_ARRAY.length; i++) {\n      var target = WTF_ISSUE_555_ARRAY[i];\n      var targets = globalSources[target] = {};\n      for (var j = 0; j < eventNames.length; j++) {\n        var eventName = eventNames[j];\n        targets[eventName] = target + ADD_EVENT_LISTENER_SOURCE + eventName;\n      }\n    }\n    var checkIEAndCrossContext = function (nativeDelegate, delegate, target, args) {\n      if (!isDisableIECheck && ieOrEdge) {\n        if (isEnableCrossContextCheck) {\n          try {\n            var testString = delegate.toString();\n            if (testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS) {\n              nativeDelegate.apply(target, args);\n              return false;\n            }\n          } catch (error) {\n            nativeDelegate.apply(target, args);\n            return false;\n          }\n        } else {\n          var testString = delegate.toString();\n          if (testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS) {\n            nativeDelegate.apply(target, args);\n            return false;\n          }\n        }\n      } else if (isEnableCrossContextCheck) {\n        try {\n          delegate.toString();\n        } catch (error) {\n          nativeDelegate.apply(target, args);\n          return false;\n        }\n      }\n      return true;\n    };\n    var apiTypes = [];\n    for (var i = 0; i < apis.length; i++) {\n      var type = _global[apis[i]];\n      apiTypes.push(type && type.prototype);\n    }\n    // vh is validateHandler to check event handler\n    // is valid or not(for security check)\n    api.patchEventTarget(_global, api, apiTypes, {\n      vh: checkIEAndCrossContext,\n      transferEventName: function (eventName) {\n        var pointerEventName = pointerEventsMap[eventName];\n        return pointerEventName || eventName;\n      }\n    });\n    Zone[api.symbol('patchEventTarget')] = !!_global[EVENT_TARGET];\n    return true;\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  // we have to patch the instance since the proto is non-configurable\n  function apply(api, _global) {\n    var _b = api.getGlobalObjects(),\n      ADD_EVENT_LISTENER_STR = _b.ADD_EVENT_LISTENER_STR,\n      REMOVE_EVENT_LISTENER_STR = _b.REMOVE_EVENT_LISTENER_STR;\n    var WS = _global.WebSocket;\n    // On Safari window.EventTarget doesn't exist so need to patch WS add/removeEventListener\n    // On older Chrome, no need since EventTarget was already patched\n    if (!_global.EventTarget) {\n      api.patchEventTarget(_global, api, [WS.prototype]);\n    }\n    _global.WebSocket = function (x, y) {\n      var socket = arguments.length > 1 ? new WS(x, y) : new WS(x);\n      var proxySocket;\n      var proxySocketProto;\n      // Safari 7.0 has non-configurable own 'onmessage' and friends properties on the socket instance\n      var onmessageDesc = api.ObjectGetOwnPropertyDescriptor(socket, 'onmessage');\n      if (onmessageDesc && onmessageDesc.configurable === false) {\n        proxySocket = api.ObjectCreate(socket);\n        // socket have own property descriptor 'onopen', 'onmessage', 'onclose', 'onerror'\n        // but proxySocket not, so we will keep socket as prototype and pass it to\n        // patchOnProperties method\n        proxySocketProto = socket;\n        [ADD_EVENT_LISTENER_STR, REMOVE_EVENT_LISTENER_STR, 'send', 'close'].forEach(function (propName) {\n          proxySocket[propName] = function () {\n            var args = api.ArraySlice.call(arguments);\n            if (propName === ADD_EVENT_LISTENER_STR || propName === REMOVE_EVENT_LISTENER_STR) {\n              var eventName = args.length > 0 ? args[0] : undefined;\n              if (eventName) {\n                var propertySymbol = Zone.__symbol__('ON_PROPERTY' + eventName);\n                socket[propertySymbol] = proxySocket[propertySymbol];\n              }\n            }\n            return socket[propName].apply(socket, args);\n          };\n        });\n      } else {\n        // we can patch the real socket\n        proxySocket = socket;\n      }\n      api.patchOnProperties(proxySocket, ['close', 'error', 'message', 'open'], proxySocketProto);\n      return proxySocket;\n    };\n    var globalWebSocket = _global['WebSocket'];\n    for (var prop in WS) {\n      globalWebSocket[prop] = WS[prop];\n    }\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function propertyDescriptorLegacyPatch(api, _global) {\n    var _b = api.getGlobalObjects(),\n      isNode = _b.isNode,\n      isMix = _b.isMix;\n    if (isNode && !isMix) {\n      return;\n    }\n    if (!canPatchViaPropertyDescriptor(api, _global)) {\n      var supportsWebSocket = typeof WebSocket !== 'undefined';\n      // Safari, Android browsers (Jelly Bean)\n      patchViaCapturingAllTheEvents(api);\n      api.patchClass('XMLHttpRequest');\n      if (supportsWebSocket) {\n        apply(api, _global);\n      }\n      Zone[api.symbol('patchEvents')] = true;\n    }\n  }\n  function canPatchViaPropertyDescriptor(api, _global) {\n    var _b = api.getGlobalObjects(),\n      isBrowser = _b.isBrowser,\n      isMix = _b.isMix;\n    if ((isBrowser || isMix) && !api.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype, 'onclick') && typeof Element !== 'undefined') {\n      // WebKit https://bugs.webkit.org/show_bug.cgi?id=134364\n      // IDL interface attributes are not configurable\n      var desc = api.ObjectGetOwnPropertyDescriptor(Element.prototype, 'onclick');\n      if (desc && !desc.configurable) return false;\n      // try to use onclick to detect whether we can patch via propertyDescriptor\n      // because XMLHttpRequest is not available in service worker\n      if (desc) {\n        api.ObjectDefineProperty(Element.prototype, 'onclick', {\n          enumerable: true,\n          configurable: true,\n          get: function () {\n            return true;\n          }\n        });\n        var div = document.createElement('div');\n        var result = !!div.onclick;\n        api.ObjectDefineProperty(Element.prototype, 'onclick', desc);\n        return result;\n      }\n    }\n    var XMLHttpRequest = _global['XMLHttpRequest'];\n    if (!XMLHttpRequest) {\n      // XMLHttpRequest is not available in service worker\n      return false;\n    }\n    var ON_READY_STATE_CHANGE = 'onreadystatechange';\n    var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n    var xhrDesc = api.ObjectGetOwnPropertyDescriptor(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE);\n    // add enumerable and configurable here because in opera\n    // by default XMLHttpRequest.prototype.onreadystatechange is undefined\n    // without adding enumerable and configurable will cause onreadystatechange\n    // non-configurable\n    // and if XMLHttpRequest.prototype.onreadystatechange is undefined,\n    // we should set a real desc instead a fake one\n    if (xhrDesc) {\n      api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n        enumerable: true,\n        configurable: true,\n        get: function () {\n          return true;\n        }\n      });\n      var req = new XMLHttpRequest();\n      var result = !!req.onreadystatechange;\n      // restore original desc\n      api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, xhrDesc || {});\n      return result;\n    } else {\n      var SYMBOL_FAKE_ONREADYSTATECHANGE_1 = api.symbol('fake');\n      api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n        enumerable: true,\n        configurable: true,\n        get: function () {\n          return this[SYMBOL_FAKE_ONREADYSTATECHANGE_1];\n        },\n        set: function (value) {\n          this[SYMBOL_FAKE_ONREADYSTATECHANGE_1] = value;\n        }\n      });\n      var req = new XMLHttpRequest();\n      var detectFunc = function () {};\n      req.onreadystatechange = detectFunc;\n      var result = req[SYMBOL_FAKE_ONREADYSTATECHANGE_1] === detectFunc;\n      req.onreadystatechange = null;\n      return result;\n    }\n  }\n  var globalEventHandlersEventNames = ['abort', 'animationcancel', 'animationend', 'animationiteration', 'auxclick', 'beforeinput', 'blur', 'cancel', 'canplay', 'canplaythrough', 'change', 'compositionstart', 'compositionupdate', 'compositionend', 'cuechange', 'click', 'close', 'contextmenu', 'curechange', 'dblclick', 'drag', 'dragend', 'dragenter', 'dragexit', 'dragleave', 'dragover', 'drop', 'durationchange', 'emptied', 'ended', 'error', 'focus', 'focusin', 'focusout', 'gotpointercapture', 'input', 'invalid', 'keydown', 'keypress', 'keyup', 'load', 'loadstart', 'loadeddata', 'loadedmetadata', 'lostpointercapture', 'mousedown', 'mouseenter', 'mouseleave', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'mousewheel', 'orientationchange', 'pause', 'play', 'playing', 'pointercancel', 'pointerdown', 'pointerenter', 'pointerleave', 'pointerlockchange', 'mozpointerlockchange', 'webkitpointerlockerchange', 'pointerlockerror', 'mozpointerlockerror', 'webkitpointerlockerror', 'pointermove', 'pointout', 'pointerover', 'pointerup', 'progress', 'ratechange', 'reset', 'resize', 'scroll', 'seeked', 'seeking', 'select', 'selectionchange', 'selectstart', 'show', 'sort', 'stalled', 'submit', 'suspend', 'timeupdate', 'volumechange', 'touchcancel', 'touchmove', 'touchstart', 'touchend', 'transitioncancel', 'transitionend', 'waiting', 'wheel'];\n  var documentEventNames = ['afterscriptexecute', 'beforescriptexecute', 'DOMContentLoaded', 'freeze', 'fullscreenchange', 'mozfullscreenchange', 'webkitfullscreenchange', 'msfullscreenchange', 'fullscreenerror', 'mozfullscreenerror', 'webkitfullscreenerror', 'msfullscreenerror', 'readystatechange', 'visibilitychange', 'resume'];\n  var windowEventNames = ['absolutedeviceorientation', 'afterinput', 'afterprint', 'appinstalled', 'beforeinstallprompt', 'beforeprint', 'beforeunload', 'devicelight', 'devicemotion', 'deviceorientation', 'deviceorientationabsolute', 'deviceproximity', 'hashchange', 'languagechange', 'message', 'mozbeforepaint', 'offline', 'online', 'paint', 'pageshow', 'pagehide', 'popstate', 'rejectionhandled', 'storage', 'unhandledrejection', 'unload', 'userproximity', 'vrdisplayconnected', 'vrdisplaydisconnected', 'vrdisplaypresentchange'];\n  var htmlElementEventNames = ['beforecopy', 'beforecut', 'beforepaste', 'copy', 'cut', 'paste', 'dragstart', 'loadend', 'animationstart', 'search', 'transitionrun', 'transitionstart', 'webkitanimationend', 'webkitanimationiteration', 'webkitanimationstart', 'webkittransitionend'];\n  var ieElementEventNames = ['activate', 'afterupdate', 'ariarequest', 'beforeactivate', 'beforedeactivate', 'beforeeditfocus', 'beforeupdate', 'cellchange', 'controlselect', 'dataavailable', 'datasetchanged', 'datasetcomplete', 'errorupdate', 'filterchange', 'layoutcomplete', 'losecapture', 'move', 'moveend', 'movestart', 'propertychange', 'resizeend', 'resizestart', 'rowenter', 'rowexit', 'rowsdelete', 'rowsinserted', 'command', 'compassneedscalibration', 'deactivate', 'help', 'mscontentzoom', 'msmanipulationstatechanged', 'msgesturechange', 'msgesturedoubletap', 'msgestureend', 'msgesturehold', 'msgesturestart', 'msgesturetap', 'msgotpointercapture', 'msinertiastart', 'mslostpointercapture', 'mspointercancel', 'mspointerdown', 'mspointerenter', 'mspointerhover', 'mspointerleave', 'mspointermove', 'mspointerout', 'mspointerover', 'mspointerup', 'pointerout', 'mssitemodejumplistitemremoved', 'msthumbnailclick', 'stop', 'storagecommit'];\n  var webglEventNames = ['webglcontextrestored', 'webglcontextlost', 'webglcontextcreationerror'];\n  var formEventNames = ['autocomplete', 'autocompleteerror'];\n  var detailEventNames = ['toggle'];\n  var eventNames = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], globalEventHandlersEventNames, true), webglEventNames, true), formEventNames, true), detailEventNames, true), documentEventNames, true), windowEventNames, true), htmlElementEventNames, true), ieElementEventNames, true);\n  // Whenever any eventListener fires, we check the eventListener target and all parents\n  // for `onwhatever` properties and replace them with zone-bound functions\n  // - Chrome (for now)\n  function patchViaCapturingAllTheEvents(api) {\n    var unboundKey = api.symbol('unbound');\n    var _loop_5 = function (i) {\n      var property = eventNames[i];\n      var onproperty = 'on' + property;\n      self.addEventListener(property, function (event) {\n        var elt = event.target,\n          bound,\n          source;\n        if (elt) {\n          source = elt.constructor['name'] + '.' + onproperty;\n        } else {\n          source = 'unknown.' + onproperty;\n        }\n        while (elt) {\n          if (elt[onproperty] && !elt[onproperty][unboundKey]) {\n            bound = api.wrapWithCurrentZone(elt[onproperty], source);\n            bound[unboundKey] = elt[onproperty];\n            elt[onproperty] = bound;\n          }\n          elt = elt.parentElement;\n        }\n      }, true);\n    };\n    for (var i = 0; i < eventNames.length; i++) {\n      _loop_5(i);\n    }\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function registerElementPatch(_global, api) {\n    var _b = api.getGlobalObjects(),\n      isBrowser = _b.isBrowser,\n      isMix = _b.isMix;\n    if (!isBrowser && !isMix || !('registerElement' in _global.document)) {\n      return;\n    }\n    var callbacks = ['createdCallback', 'attachedCallback', 'detachedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, document, 'Document', 'registerElement', callbacks);\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  (function (_global) {\n    var symbolPrefix = _global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    function __symbol__(name) {\n      return symbolPrefix + name;\n    }\n    _global[__symbol__('legacyPatch')] = function () {\n      var Zone = _global['Zone'];\n      Zone.__load_patch('defineProperty', function (global, Zone, api) {\n        api._redefineProperty = _redefineProperty;\n        propertyPatch();\n      });\n      Zone.__load_patch('registerElement', function (global, Zone, api) {\n        registerElementPatch(global, api);\n      });\n      Zone.__load_patch('EventTargetLegacy', function (global, Zone, api) {\n        eventTargetLegacyPatch(global, api);\n        propertyDescriptorLegacyPatch(api, global);\n      });\n    };\n  })(typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {});\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  var taskSymbol = zoneSymbol$1('zoneTask');\n  function patchTimer(window, setName, cancelName, nameSuffix) {\n    var setNative = null;\n    var clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    var tasksByHandleId = {};\n    function scheduleTask(task) {\n      var data = task.data;\n      data.args[0] = function () {\n        return task.invoke.apply(this, arguments);\n      };\n      data.handleId = setNative.apply(window, data.args);\n      return task;\n    }\n    function clearTask(task) {\n      return clearNative.call(window, task.data.handleId);\n    }\n    setNative = patchMethod(window, setName, function (delegate) {\n      return function (self, args) {\n        if (typeof args[0] === 'function') {\n          var options_1 = {\n            isPeriodic: nameSuffix === 'Interval',\n            delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n            args: args\n          };\n          var callback_1 = args[0];\n          args[0] = function timer() {\n            try {\n              return callback_1.apply(this, arguments);\n            } finally {\n              // issue-934, task will be cancelled\n              // even it is a periodic task such as\n              // setInterval\n              // https://github.com/angular/angular/issues/40387\n              // Cleanup tasksByHandleId should be handled before scheduleTask\n              // Since some zoneSpec may intercept and doesn't trigger\n              // scheduleFn(scheduleTask) provided here.\n              if (!options_1.isPeriodic) {\n                if (typeof options_1.handleId === 'number') {\n                  // in non-nodejs env, we remove timerId\n                  // from local cache\n                  delete tasksByHandleId[options_1.handleId];\n                } else if (options_1.handleId) {\n                  // Node returns complex objects as handleIds\n                  // we remove task reference from timer object\n                  options_1.handleId[taskSymbol] = null;\n                }\n              }\n            }\n          };\n          var task = scheduleMacroTaskWithCurrentZone(setName, args[0], options_1, scheduleTask, clearTask);\n          if (!task) {\n            return task;\n          }\n          // Node.js must additionally support the ref and unref functions.\n          var handle = task.data.handleId;\n          if (typeof handle === 'number') {\n            // for non nodejs env, we save handleId: task\n            // mapping in local cache for clearTimeout\n            tasksByHandleId[handle] = task;\n          } else if (handle) {\n            // for nodejs env, we save task\n            // reference in timerId Object for clearTimeout\n            handle[taskSymbol] = task;\n          }\n          // check whether handle is null, because some polyfill or browser\n          // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n          if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' && typeof handle.unref === 'function') {\n            task.ref = handle.ref.bind(handle);\n            task.unref = handle.unref.bind(handle);\n          }\n          if (typeof handle === 'number' || handle) {\n            return handle;\n          }\n          return task;\n        } else {\n          // cause an error by calling it directly.\n          return delegate.apply(window, args);\n        }\n      };\n    });\n    clearNative = patchMethod(window, cancelName, function (delegate) {\n      return function (self, args) {\n        var id = args[0];\n        var task;\n        if (typeof id === 'number') {\n          // non nodejs env.\n          task = tasksByHandleId[id];\n        } else {\n          // nodejs env.\n          task = id && id[taskSymbol];\n          // other environments.\n          if (!task) {\n            task = id;\n          }\n        }\n        if (task && typeof task.type === 'string') {\n          if (task.state !== 'notScheduled' && (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n            if (typeof id === 'number') {\n              delete tasksByHandleId[id];\n            } else if (id) {\n              id[taskSymbol] = null;\n            }\n            // Do not cancel already canceled functions\n            task.zone.cancelTask(task);\n          }\n        } else {\n          // cause an error by calling it directly.\n          delegate.apply(window, args);\n        }\n      };\n    });\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function patchCustomElements(_global, api) {\n    var _b = api.getGlobalObjects(),\n      isBrowser = _b.isBrowser,\n      isMix = _b.isMix;\n    if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n      return;\n    }\n    var callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  function eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n      // EventTarget is already patched.\n      return;\n    }\n    var _b = api.getGlobalObjects(),\n      eventNames = _b.eventNames,\n      zoneSymbolEventNames = _b.zoneSymbolEventNames,\n      TRUE_STR = _b.TRUE_STR,\n      FALSE_STR = _b.FALSE_STR,\n      ZONE_SYMBOL_PREFIX = _b.ZONE_SYMBOL_PREFIX;\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (var i = 0; i < eventNames.length; i++) {\n      var eventName = eventNames[i];\n      var falseEventName = eventName + FALSE_STR;\n      var trueEventName = eventName + TRUE_STR;\n      var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n      var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n      zoneSymbolEventNames[eventName] = {};\n      zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n      zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    var EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n      return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n  }\n  function patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n  }\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n  Zone.__load_patch('legacy', function (global) {\n    var legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n      legacyPatch();\n    }\n  });\n  Zone.__load_patch('queueMicrotask', function (global, Zone, api) {\n    api.patchMethod(global, 'queueMicrotask', function (delegate) {\n      return function (self, args) {\n        Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n      };\n    });\n  });\n  Zone.__load_patch('timers', function (global) {\n    var set = 'set';\n    var clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n  });\n  Zone.__load_patch('requestAnimationFrame', function (global) {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n  });\n  Zone.__load_patch('blocking', function (global, Zone) {\n    var blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (var i = 0; i < blockingMethods.length; i++) {\n      var name_2 = blockingMethods[i];\n      patchMethod(global, name_2, function (delegate, symbol, name) {\n        return function (s, args) {\n          return Zone.current.run(delegate, global, args, name);\n        };\n      });\n    }\n  });\n  Zone.__load_patch('EventTarget', function (global, Zone, api) {\n    patchEvent(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    var XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n      api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n  });\n  Zone.__load_patch('MutationObserver', function (global, Zone, api) {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n  });\n  Zone.__load_patch('IntersectionObserver', function (global, Zone, api) {\n    patchClass('IntersectionObserver');\n  });\n  Zone.__load_patch('FileReader', function (global, Zone, api) {\n    patchClass('FileReader');\n  });\n  Zone.__load_patch('on_property', function (global, Zone, api) {\n    propertyDescriptorPatch(api, global);\n  });\n  Zone.__load_patch('customElements', function (global, Zone, api) {\n    patchCustomElements(global, api);\n  });\n  Zone.__load_patch('XHR', function (global, Zone) {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    var XHR_TASK = zoneSymbol$1('xhrTask');\n    var XHR_SYNC = zoneSymbol$1('xhrSync');\n    var XHR_LISTENER = zoneSymbol$1('xhrListener');\n    var XHR_SCHEDULED = zoneSymbol$1('xhrScheduled');\n    var XHR_URL = zoneSymbol$1('xhrURL');\n    var XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol$1('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n      var XMLHttpRequest = window['XMLHttpRequest'];\n      if (!XMLHttpRequest) {\n        // XMLHttpRequest is not available in service worker\n        return;\n      }\n      var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n      function findPendingTask(target) {\n        return target[XHR_TASK];\n      }\n      var oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n      var oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      if (!oriAddListener) {\n        var XMLHttpRequestEventTarget_1 = window['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget_1) {\n          var XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget_1.prototype;\n          oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n      }\n      var READY_STATE_CHANGE = 'readystatechange';\n      var SCHEDULED = 'scheduled';\n      function scheduleTask(task) {\n        var data = task.data;\n        var target = data.target;\n        target[XHR_SCHEDULED] = false;\n        target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n        // remove existing event listener\n        var listener = target[XHR_LISTENER];\n        if (!oriAddListener) {\n          oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n        if (listener) {\n          oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n        }\n        var newListener = target[XHR_LISTENER] = function () {\n          if (target.readyState === target.DONE) {\n            // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n            // readyState=4 multiple times, so we need to check task state here\n            if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n              // check whether the xhr has registered onload listener\n              // if that is the case, the task should invoke after all\n              // onload listeners finish.\n              // Also if the request failed without response (status = 0), the load event handler\n              // will not be triggered, in that case, we should also invoke the placeholder callback\n              // to close the XMLHttpRequest::send macroTask.\n              // https://github.com/angular/angular/issues/38795\n              var loadTasks = target[Zone.__symbol__('loadfalse')];\n              if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                var oriInvoke_1 = task.invoke;\n                task.invoke = function () {\n                  // need to load the tasks again, because in other\n                  // load listener, they may remove themselves\n                  var loadTasks = target[Zone.__symbol__('loadfalse')];\n                  for (var i = 0; i < loadTasks.length; i++) {\n                    if (loadTasks[i] === task) {\n                      loadTasks.splice(i, 1);\n                    }\n                  }\n                  if (!data.aborted && task.state === SCHEDULED) {\n                    oriInvoke_1.call(task);\n                  }\n                };\n                loadTasks.push(task);\n              } else {\n                task.invoke();\n              }\n            } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n              // error occurs when xhr.send()\n              target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n            }\n          }\n        };\n        oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n        var storedTask = target[XHR_TASK];\n        if (!storedTask) {\n          target[XHR_TASK] = task;\n        }\n        sendNative.apply(target, data.args);\n        target[XHR_SCHEDULED] = true;\n        return task;\n      }\n      function placeholderCallback() {}\n      function clearTask(task) {\n        var data = task.data;\n        // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n        // to prevent it from firing. So instead, we store info for the event listener.\n        data.aborted = true;\n        return abortNative.apply(data.target, data.args);\n      }\n      var openNative = patchMethod(XMLHttpRequestPrototype, 'open', function () {\n        return function (self, args) {\n          self[XHR_SYNC] = args[2] == false;\n          self[XHR_URL] = args[1];\n          return openNative.apply(self, args);\n        };\n      });\n      var XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n      var fetchTaskAborting = zoneSymbol$1('fetchTaskAborting');\n      var fetchTaskScheduling = zoneSymbol$1('fetchTaskScheduling');\n      var sendNative = patchMethod(XMLHttpRequestPrototype, 'send', function () {\n        return function (self, args) {\n          if (Zone.current[fetchTaskScheduling] === true) {\n            // a fetch is scheduling, so we are using xhr to polyfill fetch\n            // and because we already schedule macroTask for fetch, we should\n            // not schedule a macroTask for xhr again\n            return sendNative.apply(self, args);\n          }\n          if (self[XHR_SYNC]) {\n            // if the XHR is sync there is no task to schedule, just execute the code.\n            return sendNative.apply(self, args);\n          } else {\n            var options = {\n              target: self,\n              url: self[XHR_URL],\n              isPeriodic: false,\n              args: args,\n              aborted: false\n            };\n            var task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n            if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n              // xhr request throw error when send\n              // we should invoke task instead of leaving a scheduled\n              // pending macroTask\n              task.invoke();\n            }\n          }\n        };\n      });\n      var abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', function () {\n        return function (self, args) {\n          var task = findPendingTask(self);\n          if (task && typeof task.type == 'string') {\n            // If the XHR has already completed, do nothing.\n            // If the XHR has already been aborted, do nothing.\n            // Fix #569, call abort multiple times before done will cause\n            // macroTask task count be negative number\n            if (task.cancelFn == null || task.data && task.data.aborted) {\n              return;\n            }\n            task.zone.cancelTask(task);\n          } else if (Zone.current[fetchTaskAborting] === true) {\n            // the abort is called from fetch polyfill, we need to call native abort of XHR.\n            return abortNative.apply(self, args);\n          }\n          // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n          // task\n          // to cancel. Do nothing.\n        };\n      });\n    }\n  });\n\n  Zone.__load_patch('geolocation', function (global) {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n      patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n  });\n  Zone.__load_patch('PromiseRejectionEvent', function (global, Zone) {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n      return function (e) {\n        var eventTasks = findEventTasks(global, evtName);\n        eventTasks.forEach(function (eventTask) {\n          // windows has added unhandledrejection event listener\n          // trigger the event listener\n          var PromiseRejectionEvent = global['PromiseRejectionEvent'];\n          if (PromiseRejectionEvent) {\n            var evt = new PromiseRejectionEvent(evtName, {\n              promise: e.promise,\n              reason: e.rejection\n            });\n            eventTask.invoke(evt);\n          }\n        });\n      };\n    }\n    if (global['PromiseRejectionEvent']) {\n      Zone[zoneSymbol$1('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n      Zone[zoneSymbol$1('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n    }\n  });\n});", "map": {"version": 3, "names": ["__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "i", "l", "ar", "Array", "prototype", "slice", "call", "concat", "factory", "define", "amd", "global", "performance", "mark", "name", "performanceMeasure", "label", "symbolPrefix", "__symbol__", "checkDuplicate", "Error", "Zone", "parent", "zoneSpec", "_parent", "_name", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "assertZonePatched", "patches", "Object", "defineProperty", "get", "zone", "current", "enumerable", "configurable", "_currentZoneFrame", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "perfName", "_api", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "window", "getOwnPropertyDescriptor", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "scheduleMacroTaskWithCurrentZone", "zoneSymbol$1", "isWindowExists", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "constructor", "_loop_1", "name_1", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate_1", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "api", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "e", "rejection", "console", "stack", "_loop_2", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "promise", "v", "resolvePromise", "once", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "currentTask", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "splice", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "executor", "any", "values", "Symbol", "iterator", "Promise", "promises", "_i", "values_1", "finished", "errors", "race", "res", "rej", "onResolve", "onReject", "values_2", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "status", "<PERSON><PERSON><PERSON><PERSON>", "reason", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "_loop_3", "this_1", "curValueIndex", "thenErr", "values_3", "toStringTag", "species", "_a", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "_this", "wrapped", "zoneify", "resultPromise", "ctor", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "originalDelegate", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "delegate_1", "globalCallback", "context", "isCapture", "tasks", "copyTasks", "_loop_4", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "assign", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "constructorName", "targetSource", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "Event", "targetName", "method", "callbacks", "nativeDelegate", "opts", "for<PERSON>ach", "descriptor", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "internalWindow_1", "ignoreErrorProperties", "eventNames", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "zoneSymbol", "_defineProperty", "_getOwnPropertyDescriptor", "_create", "unconfigurablesKey", "propertyPatch", "isUnconfigurable", "originalConfigurableFlag", "rewriteDescriptor", "_tryDefineProperty", "defineProperties", "props", "_b", "getOwnPropertySymbols", "sym", "propertiesObject", "isFrozen", "swallowError", "<PERSON><PERSON><PERSON><PERSON>", "log", "eventTargetLegacyPatch", "WTF_ISSUE_555", "NO_EVENT_TARGET", "split", "EVENT_TARGET", "isWtf", "WTF_ISSUE_555_ARRAY", "isDisableIECheck", "isEnableCrossContextCheck", "FUNCTION_WRAPPER", "BROWSER_TOOLS", "pointerEventsMap", "targets", "checkIEAndCrossContext", "testString", "apiTypes", "pointerEventName", "WS", "WebSocket", "EventTarget", "x", "y", "socket", "proxySocket", "proxySocketProto", "onmessageDesc", "propName", "propertySymbol", "globalWebSocket", "propertyDescriptorLegacyPatch", "canPatchViaPropertyDescriptor", "supportsWebSocket", "patchViaCapturingAllTheEvents", "HTMLElement", "Element", "div", "document", "createElement", "onclick", "XMLHttpRequest", "ON_READY_STATE_CHANGE", "XMLHttpRequestPrototype", "xhrDesc", "req", "onreadystatechange", "SYMBOL_FAKE_ONREADYSTATECHANGE_1", "detectFunc", "globalEventHandlersEventNames", "documentEventNames", "windowEventNames", "htmlElementEventNames", "ieElementEventNames", "webglEventNames", "formEventNames", "detailEventNames", "unboundKey", "_loop_5", "property", "onproperty", "elt", "bound", "parentElement", "registerElementPatch", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "options_1", "delay", "callback_1", "timer", "handle", "ref", "unref", "bind", "id", "patchCustomElements", "customElements", "eventTargetPatch", "patchEvent", "legacyPatch", "clear", "blockingMethods", "name_2", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTarget_1", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "aborted", "loadTasks", "oriInvoke_1", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "eventTasks", "PromiseRejectionEvent", "evt"], "sources": ["E:/test_stream/frontend/rtsp-webrtc-client/node_modules/zone.js/dist/zone.js"], "sourcesContent": ["'use strict';\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * @license Angular v15.1.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n(function (factory) {\n    typeof define === 'function' && define.amd ? define(factory) :\n        factory();\n})((function () {\n    'use strict';\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    ((function (global) {\n        var performance = global['performance'];\n        function mark(name) {\n            performance && performance['mark'] && performance['mark'](name);\n        }\n        function performanceMeasure(name, label) {\n            performance && performance['measure'] && performance['measure'](name, label);\n        }\n        mark('Zone');\n        // Initialize before it's accessed below.\n        // __Zone_symbol_prefix global can be used to override the default zone\n        // symbol prefix with a custom one if needed.\n        var symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n        function __symbol__(name) {\n            return symbolPrefix + name;\n        }\n        var checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n        if (global['Zone']) {\n            // if global['Zone'] already exists (maybe zone.js was already loaded or\n            // some other lib also registered a global object named Zone), we may need\n            // to throw an error, but sometimes user may not want this error.\n            // For example,\n            // we have two web pages, page1 includes zone.js, page2 doesn't.\n            // and the 1st time user load page1 and page2, everything work fine,\n            // but when user load page2 again, error occurs because global['Zone'] already exists.\n            // so we add a flag to let user choose whether to throw this error or not.\n            // By default, if existing Zone is from zone.js, we will not throw the error.\n            if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n                throw new Error('Zone already loaded.');\n            }\n            else {\n                return global['Zone'];\n            }\n        }\n        var Zone = /** @class */ (function () {\n            function Zone(parent, zoneSpec) {\n                this._parent = parent;\n                this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n                this._properties = zoneSpec && zoneSpec.properties || {};\n                this._zoneDelegate =\n                    new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n            }\n            Zone.assertZonePatched = function () {\n                if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                    throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                        'has been overwritten.\\n' +\n                        'Most likely cause is that a Promise polyfill has been loaded ' +\n                        'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                        'If you must load one, do so before loading zone.js.)');\n                }\n            };\n            Object.defineProperty(Zone, \"root\", {\n                get: function () {\n                    var zone = Zone.current;\n                    while (zone.parent) {\n                        zone = zone.parent;\n                    }\n                    return zone;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(Zone, \"current\", {\n                get: function () {\n                    return _currentZoneFrame.zone;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(Zone, \"currentTask\", {\n                get: function () {\n                    return _currentTask;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            // tslint:disable-next-line:require-internal-with-underscore\n            Zone.__load_patch = function (name, fn, ignoreDuplicate) {\n                if (ignoreDuplicate === void 0) { ignoreDuplicate = false; }\n                if (patches.hasOwnProperty(name)) {\n                    // `checkDuplicate` option is defined from global variable\n                    // so it works for all modules.\n                    // `ignoreDuplicate` can work for the specified module\n                    if (!ignoreDuplicate && checkDuplicate) {\n                        throw Error('Already loaded patch: ' + name);\n                    }\n                }\n                else if (!global['__Zone_disable_' + name]) {\n                    var perfName = 'Zone:' + name;\n                    mark(perfName);\n                    patches[name] = fn(global, Zone, _api);\n                    performanceMeasure(perfName, perfName);\n                }\n            };\n            Object.defineProperty(Zone.prototype, \"parent\", {\n                get: function () {\n                    return this._parent;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(Zone.prototype, \"name\", {\n                get: function () {\n                    return this._name;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Zone.prototype.get = function (key) {\n                var zone = this.getZoneWith(key);\n                if (zone)\n                    return zone._properties[key];\n            };\n            Zone.prototype.getZoneWith = function (key) {\n                var current = this;\n                while (current) {\n                    if (current._properties.hasOwnProperty(key)) {\n                        return current;\n                    }\n                    current = current._parent;\n                }\n                return null;\n            };\n            Zone.prototype.fork = function (zoneSpec) {\n                if (!zoneSpec)\n                    throw new Error('ZoneSpec required!');\n                return this._zoneDelegate.fork(this, zoneSpec);\n            };\n            Zone.prototype.wrap = function (callback, source) {\n                if (typeof callback !== 'function') {\n                    throw new Error('Expecting function got: ' + callback);\n                }\n                var _callback = this._zoneDelegate.intercept(this, callback, source);\n                var zone = this;\n                return function () {\n                    return zone.runGuarded(_callback, this, arguments, source);\n                };\n            };\n            Zone.prototype.run = function (callback, applyThis, applyArgs, source) {\n                _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                finally {\n                    _currentZoneFrame = _currentZoneFrame.parent;\n                }\n            };\n            Zone.prototype.runGuarded = function (callback, applyThis, applyArgs, source) {\n                if (applyThis === void 0) { applyThis = null; }\n                _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n                try {\n                    try {\n                        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                    }\n                    catch (error) {\n                        if (this._zoneDelegate.handleError(this, error)) {\n                            throw error;\n                        }\n                    }\n                }\n                finally {\n                    _currentZoneFrame = _currentZoneFrame.parent;\n                }\n            };\n            Zone.prototype.runTask = function (task, applyThis, applyArgs) {\n                if (task.zone != this) {\n                    throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                        (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n                }\n                // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n                // will run in notScheduled(canceled) state, we should not try to\n                // run such kind of task but just return\n                if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                    return;\n                }\n                var reEntryGuard = task.state != running;\n                reEntryGuard && task._transitionTo(running, scheduled);\n                task.runCount++;\n                var previousTask = _currentTask;\n                _currentTask = task;\n                _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n                try {\n                    if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                        task.cancelFn = undefined;\n                    }\n                    try {\n                        return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                    }\n                    catch (error) {\n                        if (this._zoneDelegate.handleError(this, error)) {\n                            throw error;\n                        }\n                    }\n                }\n                finally {\n                    // if the task's state is notScheduled or unknown, then it has already been cancelled\n                    // we should not reset the state to scheduled\n                    if (task.state !== notScheduled && task.state !== unknown) {\n                        if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                            reEntryGuard && task._transitionTo(scheduled, running);\n                        }\n                        else {\n                            task.runCount = 0;\n                            this._updateTaskCount(task, -1);\n                            reEntryGuard &&\n                                task._transitionTo(notScheduled, running, notScheduled);\n                        }\n                    }\n                    _currentZoneFrame = _currentZoneFrame.parent;\n                    _currentTask = previousTask;\n                }\n            };\n            Zone.prototype.scheduleTask = function (task) {\n                if (task.zone && task.zone !== this) {\n                    // check if the task was rescheduled, the newZone\n                    // should not be the children of the original zone\n                    var newZone = this;\n                    while (newZone) {\n                        if (newZone === task.zone) {\n                            throw Error(\"can not reschedule task to \".concat(this.name, \" which is descendants of the original zone \").concat(task.zone.name));\n                        }\n                        newZone = newZone.parent;\n                    }\n                }\n                task._transitionTo(scheduling, notScheduled);\n                var zoneDelegates = [];\n                task._zoneDelegates = zoneDelegates;\n                task._zone = this;\n                try {\n                    task = this._zoneDelegate.scheduleTask(this, task);\n                }\n                catch (err) {\n                    // should set task's state to unknown when scheduleTask throw error\n                    // because the err may from reschedule, so the fromState maybe notScheduled\n                    task._transitionTo(unknown, scheduling, notScheduled);\n                    // TODO: @JiaLiPassion, should we check the result from handleError?\n                    this._zoneDelegate.handleError(this, err);\n                    throw err;\n                }\n                if (task._zoneDelegates === zoneDelegates) {\n                    // we have to check because internally the delegate can reschedule the task.\n                    this._updateTaskCount(task, 1);\n                }\n                if (task.state == scheduling) {\n                    task._transitionTo(scheduled, scheduling);\n                }\n                return task;\n            };\n            Zone.prototype.scheduleMicroTask = function (source, callback, data, customSchedule) {\n                return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n            };\n            Zone.prototype.scheduleMacroTask = function (source, callback, data, customSchedule, customCancel) {\n                return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n            };\n            Zone.prototype.scheduleEventTask = function (source, callback, data, customSchedule, customCancel) {\n                return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n            };\n            Zone.prototype.cancelTask = function (task) {\n                if (task.zone != this)\n                    throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                        (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n                if (task.state !== scheduled && task.state !== running) {\n                    return;\n                }\n                task._transitionTo(canceling, scheduled, running);\n                try {\n                    this._zoneDelegate.cancelTask(this, task);\n                }\n                catch (err) {\n                    // if error occurs when cancelTask, transit the state to unknown\n                    task._transitionTo(unknown, canceling);\n                    this._zoneDelegate.handleError(this, err);\n                    throw err;\n                }\n                this._updateTaskCount(task, -1);\n                task._transitionTo(notScheduled, canceling);\n                task.runCount = 0;\n                return task;\n            };\n            Zone.prototype._updateTaskCount = function (task, count) {\n                var zoneDelegates = task._zoneDelegates;\n                if (count == -1) {\n                    task._zoneDelegates = null;\n                }\n                for (var i = 0; i < zoneDelegates.length; i++) {\n                    zoneDelegates[i]._updateTaskCount(task.type, count);\n                }\n            };\n            return Zone;\n        }());\n        // tslint:disable-next-line:require-internal-with-underscore\n        Zone.__symbol__ = __symbol__;\n        var DELEGATE_ZS = {\n            name: '',\n            onHasTask: function (delegate, _, target, hasTaskState) { return delegate.hasTask(target, hasTaskState); },\n            onScheduleTask: function (delegate, _, target, task) { return delegate.scheduleTask(target, task); },\n            onInvokeTask: function (delegate, _, target, task, applyThis, applyArgs) { return delegate.invokeTask(target, task, applyThis, applyArgs); },\n            onCancelTask: function (delegate, _, target, task) { return delegate.cancelTask(target, task); }\n        };\n        var _ZoneDelegate = /** @class */ (function () {\n            function _ZoneDelegate(zone, parentDelegate, zoneSpec) {\n                this._taskCounts = { 'microTask': 0, 'macroTask': 0, 'eventTask': 0 };\n                this.zone = zone;\n                this._parentDelegate = parentDelegate;\n                this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n                this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n                this._forkCurrZone =\n                    zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n                this._interceptZS =\n                    zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n                this._interceptDlgt =\n                    zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n                this._interceptCurrZone =\n                    zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n                this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n                this._invokeDlgt =\n                    zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n                this._invokeCurrZone =\n                    zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n                this._handleErrorZS =\n                    zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n                this._handleErrorDlgt =\n                    zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n                this._handleErrorCurrZone =\n                    zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n                this._scheduleTaskZS =\n                    zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n                this._scheduleTaskDlgt = zoneSpec &&\n                    (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n                this._scheduleTaskCurrZone =\n                    zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n                this._invokeTaskZS =\n                    zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n                this._invokeTaskDlgt =\n                    zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n                this._invokeTaskCurrZone =\n                    zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n                this._cancelTaskZS =\n                    zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n                this._cancelTaskDlgt =\n                    zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n                this._cancelTaskCurrZone =\n                    zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n                this._hasTaskZS = null;\n                this._hasTaskDlgt = null;\n                this._hasTaskDlgtOwner = null;\n                this._hasTaskCurrZone = null;\n                var zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n                var parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n                if (zoneSpecHasTask || parentHasTask) {\n                    // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                    // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                    this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                    this._hasTaskDlgt = parentDelegate;\n                    this._hasTaskDlgtOwner = this;\n                    this._hasTaskCurrZone = zone;\n                    if (!zoneSpec.onScheduleTask) {\n                        this._scheduleTaskZS = DELEGATE_ZS;\n                        this._scheduleTaskDlgt = parentDelegate;\n                        this._scheduleTaskCurrZone = this.zone;\n                    }\n                    if (!zoneSpec.onInvokeTask) {\n                        this._invokeTaskZS = DELEGATE_ZS;\n                        this._invokeTaskDlgt = parentDelegate;\n                        this._invokeTaskCurrZone = this.zone;\n                    }\n                    if (!zoneSpec.onCancelTask) {\n                        this._cancelTaskZS = DELEGATE_ZS;\n                        this._cancelTaskDlgt = parentDelegate;\n                        this._cancelTaskCurrZone = this.zone;\n                    }\n                }\n            }\n            _ZoneDelegate.prototype.fork = function (targetZone, zoneSpec) {\n                return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) :\n                    new Zone(targetZone, zoneSpec);\n            };\n            _ZoneDelegate.prototype.intercept = function (targetZone, callback, source) {\n                return this._interceptZS ?\n                    this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) :\n                    callback;\n            };\n            _ZoneDelegate.prototype.invoke = function (targetZone, callback, applyThis, applyArgs, source) {\n                return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) :\n                    callback.apply(applyThis, applyArgs);\n            };\n            _ZoneDelegate.prototype.handleError = function (targetZone, error) {\n                return this._handleErrorZS ?\n                    this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) :\n                    true;\n            };\n            _ZoneDelegate.prototype.scheduleTask = function (targetZone, task) {\n                var returnTask = task;\n                if (this._scheduleTaskZS) {\n                    if (this._hasTaskZS) {\n                        returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                    }\n                    // clang-format off\n                    returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                    // clang-format on\n                    if (!returnTask)\n                        returnTask = task;\n                }\n                else {\n                    if (task.scheduleFn) {\n                        task.scheduleFn(task);\n                    }\n                    else if (task.type == microTask) {\n                        scheduleMicroTask(task);\n                    }\n                    else {\n                        throw new Error('Task is missing scheduleFn.');\n                    }\n                }\n                return returnTask;\n            };\n            _ZoneDelegate.prototype.invokeTask = function (targetZone, task, applyThis, applyArgs) {\n                return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) :\n                    task.callback.apply(applyThis, applyArgs);\n            };\n            _ZoneDelegate.prototype.cancelTask = function (targetZone, task) {\n                var value;\n                if (this._cancelTaskZS) {\n                    value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n                }\n                else {\n                    if (!task.cancelFn) {\n                        throw Error('Task is not cancelable');\n                    }\n                    value = task.cancelFn(task);\n                }\n                return value;\n            };\n            _ZoneDelegate.prototype.hasTask = function (targetZone, isEmpty) {\n                // hasTask should not throw error so other ZoneDelegate\n                // can still trigger hasTask callback\n                try {\n                    this._hasTaskZS &&\n                        this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n                }\n                catch (err) {\n                    this.handleError(targetZone, err);\n                }\n            };\n            // tslint:disable-next-line:require-internal-with-underscore\n            _ZoneDelegate.prototype._updateTaskCount = function (type, count) {\n                var counts = this._taskCounts;\n                var prev = counts[type];\n                var next = counts[type] = prev + count;\n                if (next < 0) {\n                    throw new Error('More tasks executed then were scheduled.');\n                }\n                if (prev == 0 || next == 0) {\n                    var isEmpty = {\n                        microTask: counts['microTask'] > 0,\n                        macroTask: counts['macroTask'] > 0,\n                        eventTask: counts['eventTask'] > 0,\n                        change: type\n                    };\n                    this.hasTask(this.zone, isEmpty);\n                }\n            };\n            return _ZoneDelegate;\n        }());\n        var ZoneTask = /** @class */ (function () {\n            function ZoneTask(type, source, callback, options, scheduleFn, cancelFn) {\n                // tslint:disable-next-line:require-internal-with-underscore\n                this._zone = null;\n                this.runCount = 0;\n                // tslint:disable-next-line:require-internal-with-underscore\n                this._zoneDelegates = null;\n                // tslint:disable-next-line:require-internal-with-underscore\n                this._state = 'notScheduled';\n                this.type = type;\n                this.source = source;\n                this.data = options;\n                this.scheduleFn = scheduleFn;\n                this.cancelFn = cancelFn;\n                if (!callback) {\n                    throw new Error('callback is not defined');\n                }\n                this.callback = callback;\n                var self = this;\n                // TODO: @JiaLiPassion options should have interface\n                if (type === eventTask && options && options.useG) {\n                    this.invoke = ZoneTask.invokeTask;\n                }\n                else {\n                    this.invoke = function () {\n                        return ZoneTask.invokeTask.call(global, self, this, arguments);\n                    };\n                }\n            }\n            ZoneTask.invokeTask = function (task, target, args) {\n                if (!task) {\n                    task = this;\n                }\n                _numberOfNestedTaskFrames++;\n                try {\n                    task.runCount++;\n                    return task.zone.runTask(task, target, args);\n                }\n                finally {\n                    if (_numberOfNestedTaskFrames == 1) {\n                        drainMicroTaskQueue();\n                    }\n                    _numberOfNestedTaskFrames--;\n                }\n            };\n            Object.defineProperty(ZoneTask.prototype, \"zone\", {\n                get: function () {\n                    return this._zone;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(ZoneTask.prototype, \"state\", {\n                get: function () {\n                    return this._state;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            ZoneTask.prototype.cancelScheduleRequest = function () {\n                this._transitionTo(notScheduled, scheduling);\n            };\n            // tslint:disable-next-line:require-internal-with-underscore\n            ZoneTask.prototype._transitionTo = function (toState, fromState1, fromState2) {\n                if (this._state === fromState1 || this._state === fromState2) {\n                    this._state = toState;\n                    if (toState == notScheduled) {\n                        this._zoneDelegates = null;\n                    }\n                }\n                else {\n                    throw new Error(\"\".concat(this.type, \" '\").concat(this.source, \"': can not transition to '\").concat(toState, \"', expecting state '\").concat(fromState1, \"'\").concat(fromState2 ? ' or \\'' + fromState2 + '\\'' : '', \", was '\").concat(this._state, \"'.\"));\n                }\n            };\n            ZoneTask.prototype.toString = function () {\n                if (this.data && typeof this.data.handleId !== 'undefined') {\n                    return this.data.handleId.toString();\n                }\n                else {\n                    return Object.prototype.toString.call(this);\n                }\n            };\n            // add toJSON method to prevent cyclic error when\n            // call JSON.stringify(zoneTask)\n            ZoneTask.prototype.toJSON = function () {\n                return {\n                    type: this.type,\n                    state: this.state,\n                    source: this.source,\n                    zone: this.zone.name,\n                    runCount: this.runCount\n                };\n            };\n            return ZoneTask;\n        }());\n        //////////////////////////////////////////////////////\n        //////////////////////////////////////////////////////\n        ///  MICROTASK QUEUE\n        //////////////////////////////////////////////////////\n        //////////////////////////////////////////////////////\n        var symbolSetTimeout = __symbol__('setTimeout');\n        var symbolPromise = __symbol__('Promise');\n        var symbolThen = __symbol__('then');\n        var _microTaskQueue = [];\n        var _isDrainingMicrotaskQueue = false;\n        var nativeMicroTaskQueuePromise;\n        function nativeScheduleMicroTask(func) {\n            if (!nativeMicroTaskQueuePromise) {\n                if (global[symbolPromise]) {\n                    nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n                }\n            }\n            if (nativeMicroTaskQueuePromise) {\n                var nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n                if (!nativeThen) {\n                    // native Promise is not patchable, we need to use `then` directly\n                    // issue 1078\n                    nativeThen = nativeMicroTaskQueuePromise['then'];\n                }\n                nativeThen.call(nativeMicroTaskQueuePromise, func);\n            }\n            else {\n                global[symbolSetTimeout](func, 0);\n            }\n        }\n        function scheduleMicroTask(task) {\n            // if we are not running in any task, and there has not been anything scheduled\n            // we must bootstrap the initial task creation by manually scheduling the drain\n            if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n                // We are not running in Task, so we need to kickstart the microtask queue.\n                nativeScheduleMicroTask(drainMicroTaskQueue);\n            }\n            task && _microTaskQueue.push(task);\n        }\n        function drainMicroTaskQueue() {\n            if (!_isDrainingMicrotaskQueue) {\n                _isDrainingMicrotaskQueue = true;\n                while (_microTaskQueue.length) {\n                    var queue = _microTaskQueue;\n                    _microTaskQueue = [];\n                    for (var i = 0; i < queue.length; i++) {\n                        var task = queue[i];\n                        try {\n                            task.zone.runTask(task, null, null);\n                        }\n                        catch (error) {\n                            _api.onUnhandledError(error);\n                        }\n                    }\n                }\n                _api.microtaskDrainDone();\n                _isDrainingMicrotaskQueue = false;\n            }\n        }\n        //////////////////////////////////////////////////////\n        //////////////////////////////////////////////////////\n        ///  BOOTSTRAP\n        //////////////////////////////////////////////////////\n        //////////////////////////////////////////////////////\n        var NO_ZONE = { name: 'NO ZONE' };\n        var notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n        var microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n        var patches = {};\n        var _api = {\n            symbol: __symbol__,\n            currentZoneFrame: function () { return _currentZoneFrame; },\n            onUnhandledError: noop,\n            microtaskDrainDone: noop,\n            scheduleMicroTask: scheduleMicroTask,\n            showUncaughtError: function () { return !Zone[__symbol__('ignoreConsoleErrorUncaughtError')]; },\n            patchEventTarget: function () { return []; },\n            patchOnProperties: noop,\n            patchMethod: function () { return noop; },\n            bindArguments: function () { return []; },\n            patchThen: function () { return noop; },\n            patchMacroTask: function () { return noop; },\n            patchEventPrototype: function () { return noop; },\n            isIEOrEdge: function () { return false; },\n            getGlobalObjects: function () { return undefined; },\n            ObjectDefineProperty: function () { return noop; },\n            ObjectGetOwnPropertyDescriptor: function () { return undefined; },\n            ObjectCreate: function () { return undefined; },\n            ArraySlice: function () { return []; },\n            patchClass: function () { return noop; },\n            wrapWithCurrentZone: function () { return noop; },\n            filterProperties: function () { return []; },\n            attachOriginToPatched: function () { return noop; },\n            _redefineProperty: function () { return noop; },\n            patchCallbacks: function () { return noop; },\n            nativeScheduleMicroTask: nativeScheduleMicroTask\n        };\n        var _currentZoneFrame = { parent: null, zone: new Zone(null, null) };\n        var _currentTask = null;\n        var _numberOfNestedTaskFrames = 0;\n        function noop() { }\n        performanceMeasure('Zone', 'Zone');\n        return global['Zone'] = Zone;\n    }))(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * Suppress closure compiler errors about unknown 'Zone' variable\n     * @fileoverview\n     * @suppress {undefinedVars,globalThis,missingRequire}\n     */\n    /// <reference types=\"node\"/>\n    // issue #989, to reduce bundle size, use short name\n    /** Object.getOwnPropertyDescriptor */\n    var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    /** Object.defineProperty */\n    var ObjectDefineProperty = Object.defineProperty;\n    /** Object.getPrototypeOf */\n    var ObjectGetPrototypeOf = Object.getPrototypeOf;\n    /** Object.create */\n    var ObjectCreate = Object.create;\n    /** Array.prototype.slice */\n    var ArraySlice = Array.prototype.slice;\n    /** addEventListener string const */\n    var ADD_EVENT_LISTENER_STR = 'addEventListener';\n    /** removeEventListener string const */\n    var REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n    /** zoneSymbol addEventListener */\n    var ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n    /** zoneSymbol removeEventListener */\n    var ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n    /** true string const */\n    var TRUE_STR = 'true';\n    /** false string const */\n    var FALSE_STR = 'false';\n    /** Zone symbol prefix string const. */\n    var ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\n    function wrapWithCurrentZone(callback, source) {\n        return Zone.current.wrap(callback, source);\n    }\n    function scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n        return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n    }\n    var zoneSymbol$1 = Zone.__symbol__;\n    var isWindowExists = typeof window !== 'undefined';\n    var internalWindow = isWindowExists ? window : undefined;\n    var _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\n    var REMOVE_ATTRIBUTE = 'removeAttribute';\n    function bindArguments(args, source) {\n        for (var i = args.length - 1; i >= 0; i--) {\n            if (typeof args[i] === 'function') {\n                args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n            }\n        }\n        return args;\n    }\n    function patchPrototype(prototype, fnNames) {\n        var source = prototype.constructor['name'];\n        var _loop_1 = function (i) {\n            var name_1 = fnNames[i];\n            var delegate = prototype[name_1];\n            if (delegate) {\n                var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name_1);\n                if (!isPropertyWritable(prototypeDesc)) {\n                    return \"continue\";\n                }\n                prototype[name_1] = (function (delegate) {\n                    var patched = function () {\n                        return delegate.apply(this, bindArguments(arguments, source + '.' + name_1));\n                    };\n                    attachOriginToPatched(patched, delegate);\n                    return patched;\n                })(delegate);\n            }\n        };\n        for (var i = 0; i < fnNames.length; i++) {\n            _loop_1(i);\n        }\n    }\n    function isPropertyWritable(propertyDesc) {\n        if (!propertyDesc) {\n            return true;\n        }\n        if (propertyDesc.writable === false) {\n            return false;\n        }\n        return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n    }\n    var isWebWorker = (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope);\n    // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n    // this code.\n    var isNode = (!('nw' in _global) && typeof _global.process !== 'undefined' &&\n        {}.toString.call(_global.process) === '[object process]');\n    var isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n    // we are in electron of nw, so we are both browser and nodejs\n    // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n    // this code.\n    var isMix = typeof _global.process !== 'undefined' &&\n        {}.toString.call(_global.process) === '[object process]' && !isWebWorker &&\n        !!(isWindowExists && internalWindow['HTMLElement']);\n    var zoneSymbolEventNames$1 = {};\n    var wrapFn = function (event) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        var eventNameSymbol = zoneSymbolEventNames$1[event.type];\n        if (!eventNameSymbol) {\n            eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol$1('ON_PROPERTY' + event.type);\n        }\n        var target = this || event.target || _global;\n        var listener = target[eventNameSymbol];\n        var result;\n        if (isBrowser && target === internalWindow && event.type === 'error') {\n            // window.onerror have different signature\n            // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n            // and onerror callback will prevent default when callback return true\n            var errorEvent = event;\n            result = listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n            if (result === true) {\n                event.preventDefault();\n            }\n        }\n        else {\n            result = listener && listener.apply(this, arguments);\n            if (result != undefined && !result) {\n                event.preventDefault();\n            }\n        }\n        return result;\n    };\n    function patchProperty(obj, prop, prototype) {\n        var desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n        if (!desc && prototype) {\n            // when patch window object, use prototype to check prop exist or not\n            var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n            if (prototypeDesc) {\n                desc = { enumerable: true, configurable: true };\n            }\n        }\n        // if the descriptor not exists or is not configurable\n        // just return\n        if (!desc || !desc.configurable) {\n            return;\n        }\n        var onPropPatchedSymbol = zoneSymbol$1('on' + prop + 'patched');\n        if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n            return;\n        }\n        // A property descriptor cannot have getter/setter and be writable\n        // deleting the writable and value properties avoids this error:\n        //\n        // TypeError: property descriptors must not specify a value or be writable when a\n        // getter or setter has been specified\n        delete desc.writable;\n        delete desc.value;\n        var originalDescGet = desc.get;\n        var originalDescSet = desc.set;\n        // slice(2) cuz 'onclick' -> 'click', etc\n        var eventName = prop.slice(2);\n        var eventNameSymbol = zoneSymbolEventNames$1[eventName];\n        if (!eventNameSymbol) {\n            eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol$1('ON_PROPERTY' + eventName);\n        }\n        desc.set = function (newValue) {\n            // in some of windows's onproperty callback, this is undefined\n            // so we need to check it\n            var target = this;\n            if (!target && obj === _global) {\n                target = _global;\n            }\n            if (!target) {\n                return;\n            }\n            var previousValue = target[eventNameSymbol];\n            if (typeof previousValue === 'function') {\n                target.removeEventListener(eventName, wrapFn);\n            }\n            // issue #978, when onload handler was added before loading zone.js\n            // we should remove it with originalDescSet\n            originalDescSet && originalDescSet.call(target, null);\n            target[eventNameSymbol] = newValue;\n            if (typeof newValue === 'function') {\n                target.addEventListener(eventName, wrapFn, false);\n            }\n        };\n        // The getter would return undefined for unassigned properties but the default value of an\n        // unassigned property is null\n        desc.get = function () {\n            // in some of windows's onproperty callback, this is undefined\n            // so we need to check it\n            var target = this;\n            if (!target && obj === _global) {\n                target = _global;\n            }\n            if (!target) {\n                return null;\n            }\n            var listener = target[eventNameSymbol];\n            if (listener) {\n                return listener;\n            }\n            else if (originalDescGet) {\n                // result will be null when use inline event attribute,\n                // such as <button onclick=\"func();\">OK</button>\n                // because the onclick function is internal raw uncompiled handler\n                // the onclick will be evaluated when first time event was triggered or\n                // the property is accessed, https://github.com/angular/zone.js/issues/525\n                // so we should use original native get to retrieve the handler\n                var value = originalDescGet.call(this);\n                if (value) {\n                    desc.set.call(this, value);\n                    if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                        target.removeAttribute(prop);\n                    }\n                    return value;\n                }\n            }\n            return null;\n        };\n        ObjectDefineProperty(obj, prop, desc);\n        obj[onPropPatchedSymbol] = true;\n    }\n    function patchOnProperties(obj, properties, prototype) {\n        if (properties) {\n            for (var i = 0; i < properties.length; i++) {\n                patchProperty(obj, 'on' + properties[i], prototype);\n            }\n        }\n        else {\n            var onProperties = [];\n            for (var prop in obj) {\n                if (prop.slice(0, 2) == 'on') {\n                    onProperties.push(prop);\n                }\n            }\n            for (var j = 0; j < onProperties.length; j++) {\n                patchProperty(obj, onProperties[j], prototype);\n            }\n        }\n    }\n    var originalInstanceKey = zoneSymbol$1('originalInstance');\n    // wrap some native API on `window`\n    function patchClass(className) {\n        var OriginalClass = _global[className];\n        if (!OriginalClass)\n            return;\n        // keep original class in global\n        _global[zoneSymbol$1(className)] = OriginalClass;\n        _global[className] = function () {\n            var a = bindArguments(arguments, className);\n            switch (a.length) {\n                case 0:\n                    this[originalInstanceKey] = new OriginalClass();\n                    break;\n                case 1:\n                    this[originalInstanceKey] = new OriginalClass(a[0]);\n                    break;\n                case 2:\n                    this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                    break;\n                case 3:\n                    this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                    break;\n                case 4:\n                    this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                    break;\n                default:\n                    throw new Error('Arg list too long.');\n            }\n        };\n        // attach original delegate to patched function\n        attachOriginToPatched(_global[className], OriginalClass);\n        var instance = new OriginalClass(function () { });\n        var prop;\n        for (prop in instance) {\n            // https://bugs.webkit.org/show_bug.cgi?id=44721\n            if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n                continue;\n            (function (prop) {\n                if (typeof instance[prop] === 'function') {\n                    _global[className].prototype[prop] = function () {\n                        return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                    };\n                }\n                else {\n                    ObjectDefineProperty(_global[className].prototype, prop, {\n                        set: function (fn) {\n                            if (typeof fn === 'function') {\n                                this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                                // keep callback in wrapped function so we can\n                                // use it in Function.prototype.toString to return\n                                // the native one.\n                                attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                            }\n                            else {\n                                this[originalInstanceKey][prop] = fn;\n                            }\n                        },\n                        get: function () {\n                            return this[originalInstanceKey][prop];\n                        }\n                    });\n                }\n            }(prop));\n        }\n        for (prop in OriginalClass) {\n            if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n                _global[className][prop] = OriginalClass[prop];\n            }\n        }\n    }\n    function patchMethod(target, name, patchFn) {\n        var proto = target;\n        while (proto && !proto.hasOwnProperty(name)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && target[name]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = target;\n        }\n        var delegateName = zoneSymbol$1(name);\n        var delegate = null;\n        if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n            delegate = proto[delegateName] = proto[name];\n            // check whether proto[name] is writable\n            // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n            var desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n            if (isPropertyWritable(desc)) {\n                var patchDelegate_1 = patchFn(delegate, delegateName, name);\n                proto[name] = function () {\n                    return patchDelegate_1(this, arguments);\n                };\n                attachOriginToPatched(proto[name], delegate);\n            }\n        }\n        return delegate;\n    }\n    // TODO: @JiaLiPassion, support cancel task later if necessary\n    function patchMacroTask(obj, funcName, metaCreator) {\n        var setNative = null;\n        function scheduleTask(task) {\n            var data = task.data;\n            data.args[data.cbIdx] = function () {\n                task.invoke.apply(this, arguments);\n            };\n            setNative.apply(data.target, data.args);\n            return task;\n        }\n        setNative = patchMethod(obj, funcName, function (delegate) { return function (self, args) {\n            var meta = metaCreator(self, args);\n            if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n                return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n            }\n            else {\n                // cause an error by calling it directly.\n                return delegate.apply(self, args);\n            }\n        }; });\n    }\n    function attachOriginToPatched(patched, original) {\n        patched[zoneSymbol$1('OriginalDelegate')] = original;\n    }\n    var isDetectedIEOrEdge = false;\n    var ieOrEdge = false;\n    function isIE() {\n        try {\n            var ua = internalWindow.navigator.userAgent;\n            if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n                return true;\n            }\n        }\n        catch (error) {\n        }\n        return false;\n    }\n    function isIEOrEdge() {\n        if (isDetectedIEOrEdge) {\n            return ieOrEdge;\n        }\n        isDetectedIEOrEdge = true;\n        try {\n            var ua = internalWindow.navigator.userAgent;\n            if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n                ieOrEdge = true;\n            }\n        }\n        catch (error) {\n        }\n        return ieOrEdge;\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    Zone.__load_patch('ZoneAwarePromise', function (global, Zone, api) {\n        var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        var ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                var className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        var __symbol__ = api.symbol;\n        var _uncaughtPromiseErrors = [];\n        var isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n        var symbolPromise = __symbol__('Promise');\n        var symbolThen = __symbol__('then');\n        var creationTrace = '__creationTrace__';\n        api.onUnhandledError = function (e) {\n            if (api.showUncaughtError()) {\n                var rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = function () {\n            var _loop_2 = function () {\n                var uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(function () {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            };\n            while (_uncaughtPromiseErrors.length) {\n                _loop_2();\n            }\n        };\n        var UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                var handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) {\n            }\n        }\n        function isThenable(value) {\n            return value && value.then;\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        var symbolState = __symbol__('state');\n        var symbolValue = __symbol__('value');\n        var symbolFinally = __symbol__('finally');\n        var symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        var symbolParentPromiseState = __symbol__('parentPromiseState');\n        var source = 'Promise.then';\n        var UNRESOLVED = null;\n        var RESOLVED = true;\n        var REJECTED = false;\n        var REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return function (v) {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        var once = function () {\n            var wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        var TYPE_ERROR = 'Promise resolved with itself';\n        var CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            var onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                var then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(function () {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED && value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(function () {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    var queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        var trace = Zone.currentTask && Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, { configurable: true, enumerable: false, writable: true, value: trace });\n                        }\n                    }\n                    for (var i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        var uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' + readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        var REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    var handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) {\n                }\n                promise[symbolState] = REJECTED;\n                for (var i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            var promiseState = promise[symbolState];\n            var delegate = promiseState ?\n                (typeof onFulfilled === 'function') ? onFulfilled : forwardResolution :\n                (typeof onRejected === 'function') ? onRejected :\n                    forwardRejection;\n            zone.scheduleMicroTask(source, function () {\n                try {\n                    var parentPromiseValue = promise[symbolValue];\n                    var isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    var value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ?\n                        [] :\n                        [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        var ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        var noop = function () { };\n        var AggregateError = global.AggregateError;\n        var ZoneAwarePromise = /** @class */ (function () {\n            function ZoneAwarePromise(executor) {\n                var promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    var onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            ZoneAwarePromise.toString = function () {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            };\n            ZoneAwarePromise.resolve = function (value) {\n                return resolvePromise(new this(null), RESOLVED, value);\n            };\n            ZoneAwarePromise.reject = function (error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            };\n            ZoneAwarePromise.any = function (values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                var promises = [];\n                var count = 0;\n                try {\n                    for (var _i = 0, values_1 = values; _i < values_1.length; _i++) {\n                        var v = values_1[_i];\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                var finished = false;\n                var errors = [];\n                return new ZoneAwarePromise(function (resolve, reject) {\n                    for (var i = 0; i < promises.length; i++) {\n                        promises[i].then(function (v) {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, function (err) {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            };\n            ;\n            ZoneAwarePromise.race = function (values) {\n                var resolve;\n                var reject;\n                var promise = new this(function (res, rej) {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (var _i = 0, values_2 = values; _i < values_2.length; _i++) {\n                    var value = values_2[_i];\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            };\n            ZoneAwarePromise.all = function (values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            };\n            ZoneAwarePromise.allSettled = function (values) {\n                var P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: function (value) { return ({ status: 'fulfilled', value: value }); },\n                    errorCallback: function (err) { return ({ status: 'rejected', reason: err }); }\n                });\n            };\n            ZoneAwarePromise.allWithCallback = function (values, callback) {\n                var resolve;\n                var reject;\n                var promise = new this(function (res, rej) {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                var unresolvedCount = 2;\n                var valueIndex = 0;\n                var resolvedValues = [];\n                var _loop_3 = function (value) {\n                    if (!isThenable(value)) {\n                        value = this_1.resolve(value);\n                    }\n                    var curValueIndex = valueIndex;\n                    try {\n                        value.then(function (value) {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, function (err) {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                };\n                var this_1 = this;\n                for (var _i = 0, values_3 = values; _i < values_3.length; _i++) {\n                    var value = values_3[_i];\n                    _loop_3(value);\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            };\n            Object.defineProperty(ZoneAwarePromise.prototype, Symbol.toStringTag, {\n                get: function () {\n                    return 'Promise';\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(ZoneAwarePromise.prototype, Symbol.species, {\n                get: function () {\n                    return ZoneAwarePromise;\n                },\n                enumerable: false,\n                configurable: true\n            });\n            ZoneAwarePromise.prototype.then = function (onFulfilled, onRejected) {\n                var _a;\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and invokes\n                // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n                // properties of undefined (reading 'Symbol(Symbol.species)')`.\n                var C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                var chainPromise = new C(noop);\n                var zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            };\n            ZoneAwarePromise.prototype.catch = function (onRejected) {\n                return this.then(null, onRejected);\n            };\n            ZoneAwarePromise.prototype.finally = function (onFinally) {\n                var _a;\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                var C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                var chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                var zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            };\n            return ZoneAwarePromise;\n        }());\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        var NativePromise = global[symbolPromise] = global['Promise'];\n        global['Promise'] = ZoneAwarePromise;\n        var symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            var proto = Ctor.prototype;\n            var prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            var originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                var _this = this;\n                var wrapped = new ZoneAwarePromise(function (resolve, reject) {\n                    originalThen.call(_this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                var resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                var ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', function (delegate) { return zoneify(delegate); });\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', function (global) {\n        // patch Func.prototype.toString to let them look like native\n        var originalFunctionToString = Function.prototype.toString;\n        var ORIGINAL_DELEGATE_SYMBOL = zoneSymbol$1('OriginalDelegate');\n        var PROMISE_SYMBOL = zoneSymbol$1('Promise');\n        var ERROR_SYMBOL = zoneSymbol$1('Error');\n        var newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                var originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    var nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    var nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        var originalObjectToString = Object.prototype.toString;\n        var PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var passiveSupported = false;\n    if (typeof window !== 'undefined') {\n        try {\n            var options = Object.defineProperty({}, 'passive', {\n                get: function () {\n                    passiveSupported = true;\n                }\n            });\n            // Note: We pass the `options` object as the event handler too. This is not compatible with the\n            // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n            // without an actual handler.\n            window.addEventListener('test', options, options);\n            window.removeEventListener('test', options, options);\n        }\n        catch (err) {\n            passiveSupported = false;\n        }\n    }\n    // an identifier to tell ZoneTask do not create a new invoke closure\n    var OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n        useG: true\n    };\n    var zoneSymbolEventNames = {};\n    var globalSources = {};\n    var EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\n    var IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol$1('propagationStopped');\n    function prepareEventNames(eventName, eventNameToString) {\n        var falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n        var trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n        var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    function patchEventTarget(_global, api, apis, patchOptions) {\n        var ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n        var REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n        var LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n        var REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n        var zoneSymbolAddEventListener = zoneSymbol$1(ADD_EVENT_LISTENER);\n        var ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n        var PREPEND_EVENT_LISTENER = 'prependListener';\n        var PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n        var invokeTask = function (task, target, event) {\n            // for better performance, check isRemoved which is set\n            // by removeEventListener\n            if (task.isRemoved) {\n                return;\n            }\n            var delegate = task.callback;\n            if (typeof delegate === 'object' && delegate.handleEvent) {\n                // create the bind version of handleEvent when invoke\n                task.callback = function (event) { return delegate.handleEvent(event); };\n                task.originalDelegate = delegate;\n            }\n            // invoke static task.invoke\n            // need to try/catch error here, otherwise, the error in one event listener\n            // will break the executions of the other event listeners. Also error will\n            // not remove the event listener when `once` options is true.\n            var error;\n            try {\n                task.invoke(task, target, [event]);\n            }\n            catch (err) {\n                error = err;\n            }\n            var options = task.options;\n            if (options && typeof options === 'object' && options.once) {\n                // if options.once is true, after invoke once remove listener here\n                // only browser need to do this, nodejs eventEmitter will cal removeListener\n                // inside EventEmitter.once\n                var delegate_1 = task.originalDelegate ? task.originalDelegate : task.callback;\n                target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate_1, options);\n            }\n            return error;\n        };\n        function globalCallback(context, event, isCapture) {\n            // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n            // event will be undefined, so we need to use window.event\n            event = event || _global.event;\n            if (!event) {\n                return;\n            }\n            // event.target is needed for Samsung TV and SourceBuffer\n            // || global is needed https://github.com/angular/zone.js/issues/190\n            var target = context || event.target || _global;\n            var tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n            if (tasks) {\n                var errors = [];\n                // invoke all tasks which attached to current target with given event.type and capture = false\n                // for performance concern, if task.length === 1, just invoke\n                if (tasks.length === 1) {\n                    var err = invokeTask(tasks[0], target, event);\n                    err && errors.push(err);\n                }\n                else {\n                    // https://github.com/angular/zone.js/issues/836\n                    // copy the tasks array before invoke, to avoid\n                    // the callback will remove itself or other listener\n                    var copyTasks = tasks.slice();\n                    for (var i = 0; i < copyTasks.length; i++) {\n                        if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                            break;\n                        }\n                        var err = invokeTask(copyTasks[i], target, event);\n                        err && errors.push(err);\n                    }\n                }\n                // Since there is only one error, we don't need to schedule microTask\n                // to throw the error.\n                if (errors.length === 1) {\n                    throw errors[0];\n                }\n                else {\n                    var _loop_4 = function (i) {\n                        var err = errors[i];\n                        api.nativeScheduleMicroTask(function () {\n                            throw err;\n                        });\n                    };\n                    for (var i = 0; i < errors.length; i++) {\n                        _loop_4(i);\n                    }\n                }\n            }\n        }\n        // global shared zoneAwareCallback to handle all event callback with capture = false\n        var globalZoneAwareCallback = function (event) {\n            return globalCallback(this, event, false);\n        };\n        // global shared zoneAwareCallback to handle all event callback with capture = true\n        var globalZoneAwareCaptureCallback = function (event) {\n            return globalCallback(this, event, true);\n        };\n        function patchEventTargetMethods(obj, patchOptions) {\n            if (!obj) {\n                return false;\n            }\n            var useGlobalCallback = true;\n            if (patchOptions && patchOptions.useG !== undefined) {\n                useGlobalCallback = patchOptions.useG;\n            }\n            var validateHandler = patchOptions && patchOptions.vh;\n            var checkDuplicate = true;\n            if (patchOptions && patchOptions.chkDup !== undefined) {\n                checkDuplicate = patchOptions.chkDup;\n            }\n            var returnTarget = false;\n            if (patchOptions && patchOptions.rt !== undefined) {\n                returnTarget = patchOptions.rt;\n            }\n            var proto = obj;\n            while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n                proto = ObjectGetPrototypeOf(proto);\n            }\n            if (!proto && obj[ADD_EVENT_LISTENER]) {\n                // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n                proto = obj;\n            }\n            if (!proto) {\n                return false;\n            }\n            if (proto[zoneSymbolAddEventListener]) {\n                return false;\n            }\n            var eventNameToString = patchOptions && patchOptions.eventNameToString;\n            // a shared global taskData to pass data for scheduleEventTask\n            // so we do not need to create a new object just for pass some data\n            var taskData = {};\n            var nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n            var nativeRemoveEventListener = proto[zoneSymbol$1(REMOVE_EVENT_LISTENER)] =\n                proto[REMOVE_EVENT_LISTENER];\n            var nativeListeners = proto[zoneSymbol$1(LISTENERS_EVENT_LISTENER)] =\n                proto[LISTENERS_EVENT_LISTENER];\n            var nativeRemoveAllListeners = proto[zoneSymbol$1(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n                proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n            var nativePrependEventListener;\n            if (patchOptions && patchOptions.prepend) {\n                nativePrependEventListener = proto[zoneSymbol$1(patchOptions.prepend)] =\n                    proto[patchOptions.prepend];\n            }\n            /**\n             * This util function will build an option object with passive option\n             * to handle all possible input from the user.\n             */\n            function buildEventListenerOptions(options, passive) {\n                if (!passiveSupported && typeof options === 'object' && options) {\n                    // doesn't support passive but user want to pass an object as options.\n                    // this will not work on some old browser, so we just pass a boolean\n                    // as useCapture parameter\n                    return !!options.capture;\n                }\n                if (!passiveSupported || !passive) {\n                    return options;\n                }\n                if (typeof options === 'boolean') {\n                    return { capture: options, passive: true };\n                }\n                if (!options) {\n                    return { passive: true };\n                }\n                if (typeof options === 'object' && options.passive !== false) {\n                    return Object.assign(Object.assign({}, options), { passive: true });\n                }\n                return options;\n            }\n            var customScheduleGlobal = function (task) {\n                // if there is already a task for the eventName + capture,\n                // just return, because we use the shared globalZoneAwareCallback here.\n                if (taskData.isExisting) {\n                    return;\n                }\n                return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n            };\n            var customCancelGlobal = function (task) {\n                // if task is not marked as isRemoved, this call is directly\n                // from Zone.prototype.cancelTask, we should remove the task\n                // from tasksList of target first\n                if (!task.isRemoved) {\n                    var symbolEventNames = zoneSymbolEventNames[task.eventName];\n                    var symbolEventName = void 0;\n                    if (symbolEventNames) {\n                        symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                    }\n                    var existingTasks = symbolEventName && task.target[symbolEventName];\n                    if (existingTasks) {\n                        for (var i = 0; i < existingTasks.length; i++) {\n                            var existingTask = existingTasks[i];\n                            if (existingTask === task) {\n                                existingTasks.splice(i, 1);\n                                // set isRemoved to data for faster invokeTask check\n                                task.isRemoved = true;\n                                if (existingTasks.length === 0) {\n                                    // all tasks for the eventName + capture have gone,\n                                    // remove globalZoneAwareCallback and remove the task cache from target\n                                    task.allRemoved = true;\n                                    task.target[symbolEventName] = null;\n                                }\n                                break;\n                            }\n                        }\n                    }\n                }\n                // if all tasks for the eventName + capture have gone,\n                // we will really remove the global event callback,\n                // if not, return\n                if (!task.allRemoved) {\n                    return;\n                }\n                return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n            };\n            var customScheduleNonGlobal = function (task) {\n                return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n            };\n            var customSchedulePrepend = function (task) {\n                return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n            };\n            var customCancelNonGlobal = function (task) {\n                return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n            };\n            var customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n            var customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n            var compareTaskCallbackVsDelegate = function (task, delegate) {\n                var typeOfDelegate = typeof delegate;\n                return (typeOfDelegate === 'function' && task.callback === delegate) ||\n                    (typeOfDelegate === 'object' && task.originalDelegate === delegate);\n            };\n            var compare = (patchOptions && patchOptions.diff) ? patchOptions.diff : compareTaskCallbackVsDelegate;\n            var unpatchedEvents = Zone[zoneSymbol$1('UNPATCHED_EVENTS')];\n            var passiveEvents = _global[zoneSymbol$1('PASSIVE_EVENTS')];\n            var makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget, prepend) {\n                if (returnTarget === void 0) { returnTarget = false; }\n                if (prepend === void 0) { prepend = false; }\n                return function () {\n                    var target = this || _global;\n                    var eventName = arguments[0];\n                    if (patchOptions && patchOptions.transferEventName) {\n                        eventName = patchOptions.transferEventName(eventName);\n                    }\n                    var delegate = arguments[1];\n                    if (!delegate) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    if (isNode && eventName === 'uncaughtException') {\n                        // don't patch uncaughtException of nodejs to prevent endless loop\n                        return nativeListener.apply(this, arguments);\n                    }\n                    // don't create the bind delegate function for handleEvent\n                    // case here to improve addEventListener performance\n                    // we will create the bind delegate when invoke\n                    var isHandleEvent = false;\n                    if (typeof delegate !== 'function') {\n                        if (!delegate.handleEvent) {\n                            return nativeListener.apply(this, arguments);\n                        }\n                        isHandleEvent = true;\n                    }\n                    if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                        return;\n                    }\n                    var passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                    var options = buildEventListenerOptions(arguments[2], passive);\n                    if (unpatchedEvents) {\n                        // check unpatched list\n                        for (var i = 0; i < unpatchedEvents.length; i++) {\n                            if (eventName === unpatchedEvents[i]) {\n                                if (passive) {\n                                    return nativeListener.call(target, eventName, delegate, options);\n                                }\n                                else {\n                                    return nativeListener.apply(this, arguments);\n                                }\n                            }\n                        }\n                    }\n                    var capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                    var once = options && typeof options === 'object' ? options.once : false;\n                    var zone = Zone.current;\n                    var symbolEventNames = zoneSymbolEventNames[eventName];\n                    if (!symbolEventNames) {\n                        prepareEventNames(eventName, eventNameToString);\n                        symbolEventNames = zoneSymbolEventNames[eventName];\n                    }\n                    var symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                    var existingTasks = target[symbolEventName];\n                    var isExisting = false;\n                    if (existingTasks) {\n                        // already have task registered\n                        isExisting = true;\n                        if (checkDuplicate) {\n                            for (var i = 0; i < existingTasks.length; i++) {\n                                if (compare(existingTasks[i], delegate)) {\n                                    // same callback, same capture, same event name, just return\n                                    return;\n                                }\n                            }\n                        }\n                    }\n                    else {\n                        existingTasks = target[symbolEventName] = [];\n                    }\n                    var source;\n                    var constructorName = target.constructor['name'];\n                    var targetSource = globalSources[constructorName];\n                    if (targetSource) {\n                        source = targetSource[eventName];\n                    }\n                    if (!source) {\n                        source = constructorName + addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                    }\n                    // do not create a new object as task.data to pass those things\n                    // just use the global shared one\n                    taskData.options = options;\n                    if (once) {\n                        // if addEventListener with once options, we don't pass it to\n                        // native addEventListener, instead we keep the once setting\n                        // and handle ourselves.\n                        taskData.options.once = false;\n                    }\n                    taskData.target = target;\n                    taskData.capture = capture;\n                    taskData.eventName = eventName;\n                    taskData.isExisting = isExisting;\n                    var data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                    // keep taskData into data to allow onScheduleEventTask to access the task information\n                    if (data) {\n                        data.taskData = taskData;\n                    }\n                    var task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                    // should clear taskData.target to avoid memory leak\n                    // issue, https://github.com/angular/angular/issues/20442\n                    taskData.target = null;\n                    // need to clear up taskData because it is a global object\n                    if (data) {\n                        data.taskData = null;\n                    }\n                    // have to save those information to task in case\n                    // application may call task.zone.cancelTask() directly\n                    if (once) {\n                        options.once = true;\n                    }\n                    if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                        // if not support passive, and we pass an option object\n                        // to addEventListener, we should save the options to task\n                        task.options = options;\n                    }\n                    task.target = target;\n                    task.capture = capture;\n                    task.eventName = eventName;\n                    if (isHandleEvent) {\n                        // save original delegate for compare to check duplicate\n                        task.originalDelegate = delegate;\n                    }\n                    if (!prepend) {\n                        existingTasks.push(task);\n                    }\n                    else {\n                        existingTasks.unshift(task);\n                    }\n                    if (returnTarget) {\n                        return target;\n                    }\n                };\n            };\n            proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n            if (nativePrependEventListener) {\n                proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n            }\n            proto[REMOVE_EVENT_LISTENER] = function () {\n                var target = this || _global;\n                var eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                var options = arguments[2];\n                var capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                var delegate = arguments[1];\n                if (!delegate) {\n                    return nativeRemoveEventListener.apply(this, arguments);\n                }\n                if (validateHandler &&\n                    !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                    return;\n                }\n                var symbolEventNames = zoneSymbolEventNames[eventName];\n                var symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                }\n                var existingTasks = symbolEventName && target[symbolEventName];\n                if (existingTasks) {\n                    for (var i = 0; i < existingTasks.length; i++) {\n                        var existingTask = existingTasks[i];\n                        if (compare(existingTask, delegate)) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            existingTask.isRemoved = true;\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                existingTask.allRemoved = true;\n                                target[symbolEventName] = null;\n                                // in the target, we have an event listener which is added by on_property\n                                // such as target.onclick = function() {}, so we need to clear this internal\n                                // property too if all delegates all removed\n                                if (typeof eventName === 'string') {\n                                    var onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                    target[onPropertySymbol] = null;\n                                }\n                            }\n                            existingTask.zone.cancelTask(existingTask);\n                            if (returnTarget) {\n                                return target;\n                            }\n                            return;\n                        }\n                    }\n                }\n                // issue 930, didn't find the event name or callback\n                // from zone kept existingTasks, the callback maybe\n                // added outside of zone, we need to call native removeEventListener\n                // to try to remove it.\n                return nativeRemoveEventListener.apply(this, arguments);\n            };\n            proto[LISTENERS_EVENT_LISTENER] = function () {\n                var target = this || _global;\n                var eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                var listeners = [];\n                var tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n                for (var i = 0; i < tasks.length; i++) {\n                    var task = tasks[i];\n                    var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                    listeners.push(delegate);\n                }\n                return listeners;\n            };\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n                var target = this || _global;\n                var eventName = arguments[0];\n                if (!eventName) {\n                    var keys = Object.keys(target);\n                    for (var i = 0; i < keys.length; i++) {\n                        var prop = keys[i];\n                        var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                        var evtName = match && match[1];\n                        // in nodejs EventEmitter, removeListener event is\n                        // used for monitoring the removeListener call,\n                        // so just keep removeListener eventListener until\n                        // all other eventListeners are removed\n                        if (evtName && evtName !== 'removeListener') {\n                            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                        }\n                    }\n                    // remove removeListener listener finally\n                    this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n                }\n                else {\n                    if (patchOptions && patchOptions.transferEventName) {\n                        eventName = patchOptions.transferEventName(eventName);\n                    }\n                    var symbolEventNames = zoneSymbolEventNames[eventName];\n                    if (symbolEventNames) {\n                        var symbolEventName = symbolEventNames[FALSE_STR];\n                        var symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                        var tasks = target[symbolEventName];\n                        var captureTasks = target[symbolCaptureEventName];\n                        if (tasks) {\n                            var removeTasks = tasks.slice();\n                            for (var i = 0; i < removeTasks.length; i++) {\n                                var task = removeTasks[i];\n                                var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                                this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                            }\n                        }\n                        if (captureTasks) {\n                            var removeTasks = captureTasks.slice();\n                            for (var i = 0; i < removeTasks.length; i++) {\n                                var task = removeTasks[i];\n                                var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                                this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                            }\n                        }\n                    }\n                }\n                if (returnTarget) {\n                    return this;\n                }\n            };\n            // for native toString patch\n            attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n            attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n            if (nativeRemoveAllListeners) {\n                attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n            }\n            if (nativeListeners) {\n                attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n            }\n            return true;\n        }\n        var results = [];\n        for (var i = 0; i < apis.length; i++) {\n            results[i] = patchEventTargetMethods(apis[i], patchOptions);\n        }\n        return results;\n    }\n    function findEventTasks(target, eventName) {\n        if (!eventName) {\n            var foundTasks = [];\n            for (var prop in target) {\n                var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                var evtName = match && match[1];\n                if (evtName && (!eventName || evtName === eventName)) {\n                    var tasks = target[prop];\n                    if (tasks) {\n                        for (var i = 0; i < tasks.length; i++) {\n                            foundTasks.push(tasks[i]);\n                        }\n                    }\n                }\n            }\n            return foundTasks;\n        }\n        var symbolEventName = zoneSymbolEventNames[eventName];\n        if (!symbolEventName) {\n            prepareEventNames(eventName);\n            symbolEventName = zoneSymbolEventNames[eventName];\n        }\n        var captureFalseTasks = target[symbolEventName[FALSE_STR]];\n        var captureTrueTasks = target[symbolEventName[TRUE_STR]];\n        if (!captureFalseTasks) {\n            return captureTrueTasks ? captureTrueTasks.slice() : [];\n        }\n        else {\n            return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) :\n                captureFalseTasks.slice();\n        }\n    }\n    function patchEventPrototype(global, api) {\n        var Event = global['Event'];\n        if (Event && Event.prototype) {\n            api.patchMethod(Event.prototype, 'stopImmediatePropagation', function (delegate) { return function (self, args) {\n                self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n                // we need to call the native stopImmediatePropagation\n                // in case in some hybrid application, some part of\n                // application will be controlled by zone, some are not\n                delegate && delegate.apply(self, args);\n            }; });\n        }\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function patchCallbacks(api, target, targetName, method, callbacks) {\n        var symbol = Zone.__symbol__(method);\n        if (target[symbol]) {\n            return;\n        }\n        var nativeDelegate = target[symbol] = target[method];\n        target[method] = function (name, opts, options) {\n            if (opts && opts.prototype) {\n                callbacks.forEach(function (callback) {\n                    var source = \"\".concat(targetName, \".\").concat(method, \"::\") + callback;\n                    var prototype = opts.prototype;\n                    // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                    // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                    // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                    // make those properties non-writable. This means that patching callback will throw an error\n                    // `cannot assign to read-only property`. See this code as an example:\n                    // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                    // We don't want to stop the application rendering if we couldn't patch some\n                    // callback, e.g. `attributeChangedCallback`.\n                    try {\n                        if (prototype.hasOwnProperty(callback)) {\n                            var descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                            if (descriptor && descriptor.value) {\n                                descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                                api._redefineProperty(opts.prototype, callback, descriptor);\n                            }\n                            else if (prototype[callback]) {\n                                prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                            }\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    catch (_a) {\n                        // Note: we leave the catch block empty since there's no way to handle the error related\n                        // to non-writable property.\n                    }\n                });\n            }\n            return nativeDelegate.call(target, name, opts, options);\n        };\n        api.attachOriginToPatched(target[method], nativeDelegate);\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function filterProperties(target, onProperties, ignoreProperties) {\n        if (!ignoreProperties || ignoreProperties.length === 0) {\n            return onProperties;\n        }\n        var tip = ignoreProperties.filter(function (ip) { return ip.target === target; });\n        if (!tip || tip.length === 0) {\n            return onProperties;\n        }\n        var targetIgnoreProperties = tip[0].ignoreProperties;\n        return onProperties.filter(function (op) { return targetIgnoreProperties.indexOf(op) === -1; });\n    }\n    function patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n        // check whether target is available, sometimes target will be undefined\n        // because different browser or some 3rd party plugin.\n        if (!target) {\n            return;\n        }\n        var filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n        patchOnProperties(target, filteredProperties, prototype);\n    }\n    /**\n     * Get all event name properties which the event name startsWith `on`\n     * from the target object itself, inherited properties are not considered.\n     */\n    function getOnEventNames(target) {\n        return Object.getOwnPropertyNames(target)\n            .filter(function (name) { return name.startsWith('on') && name.length > 2; })\n            .map(function (name) { return name.substring(2); });\n    }\n    function propertyDescriptorPatch(api, _global) {\n        if (isNode && !isMix) {\n            return;\n        }\n        if (Zone[api.symbol('patchEvents')]) {\n            // events are already been patched by legacy patch.\n            return;\n        }\n        var ignoreProperties = _global['__Zone_ignore_on_properties'];\n        // for browsers that we can patch the descriptor:  Chrome & Firefox\n        var patchTargets = [];\n        if (isBrowser) {\n            var internalWindow_1 = window;\n            patchTargets = patchTargets.concat([\n                'Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement',\n                'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker'\n            ]);\n            var ignoreErrorProperties = isIE() ? [{ target: internalWindow_1, ignoreProperties: ['error'] }] : [];\n            // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n            // so we need to pass WindowPrototype to check onProp exist or not\n            patchFilteredProperties(internalWindow_1, getOnEventNames(internalWindow_1), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow_1));\n        }\n        patchTargets = patchTargets.concat([\n            'XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest',\n            'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket'\n        ]);\n        for (var i = 0; i < patchTargets.length; i++) {\n            var target = _global[patchTargets[i]];\n            target && target.prototype &&\n                patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n        }\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    Zone.__load_patch('util', function (global, Zone, api) {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        var eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n        // define which events will not be patched by `Zone.js`.\n        // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n        // the name consistent with angular repo.\n        // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n        // backwards compatibility.\n        var SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        var SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = function () { return ({\n            globalSources: globalSources,\n            zoneSymbolEventNames: zoneSymbolEventNames,\n            eventNames: eventNames,\n            isBrowser: isBrowser,\n            isMix: isMix,\n            isNode: isNode,\n            TRUE_STR: TRUE_STR,\n            FALSE_STR: FALSE_STR,\n            ZONE_SYMBOL_PREFIX: ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR: ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR: REMOVE_EVENT_LISTENER_STR\n        }); };\n    });\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /*\n     * This is necessary for Chrome and Chrome mobile, to enable\n     * things like redefining `createdCallback` on an element.\n     */\n    var zoneSymbol;\n    var _defineProperty;\n    var _getOwnPropertyDescriptor;\n    var _create;\n    var unconfigurablesKey;\n    function propertyPatch() {\n        zoneSymbol = Zone.__symbol__;\n        _defineProperty = Object[zoneSymbol('defineProperty')] = Object.defineProperty;\n        _getOwnPropertyDescriptor = Object[zoneSymbol('getOwnPropertyDescriptor')] =\n            Object.getOwnPropertyDescriptor;\n        _create = Object.create;\n        unconfigurablesKey = zoneSymbol('unconfigurables');\n        Object.defineProperty = function (obj, prop, desc) {\n            if (isUnconfigurable(obj, prop)) {\n                throw new TypeError('Cannot assign to read only property \\'' + prop + '\\' of ' + obj);\n            }\n            var originalConfigurableFlag = desc.configurable;\n            if (prop !== 'prototype') {\n                desc = rewriteDescriptor(obj, prop, desc);\n            }\n            return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n        };\n        Object.defineProperties = function (obj, props) {\n            Object.keys(props).forEach(function (prop) {\n                Object.defineProperty(obj, prop, props[prop]);\n            });\n            for (var _i = 0, _b = Object.getOwnPropertySymbols(props); _i < _b.length; _i++) {\n                var sym = _b[_i];\n                var desc = Object.getOwnPropertyDescriptor(props, sym);\n                // Since `Object.getOwnPropertySymbols` returns *all* symbols,\n                // including non-enumerable ones, retrieve property descriptor and check\n                // enumerability there. Proceed with the rewrite only when a property is\n                // enumerable to make the logic consistent with the way regular\n                // properties are retrieved (via `Object.keys`, which respects\n                // `enumerable: false` flag). More information:\n                // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Enumerability_and_ownership_of_properties#retrieval\n                if (desc === null || desc === void 0 ? void 0 : desc.enumerable) {\n                    Object.defineProperty(obj, sym, props[sym]);\n                }\n            }\n            return obj;\n        };\n        Object.create = function (proto, propertiesObject) {\n            if (typeof propertiesObject === 'object' && !Object.isFrozen(propertiesObject)) {\n                Object.keys(propertiesObject).forEach(function (prop) {\n                    propertiesObject[prop] = rewriteDescriptor(proto, prop, propertiesObject[prop]);\n                });\n            }\n            return _create(proto, propertiesObject);\n        };\n        Object.getOwnPropertyDescriptor = function (obj, prop) {\n            var desc = _getOwnPropertyDescriptor(obj, prop);\n            if (desc && isUnconfigurable(obj, prop)) {\n                desc.configurable = false;\n            }\n            return desc;\n        };\n    }\n    function _redefineProperty(obj, prop, desc) {\n        var originalConfigurableFlag = desc.configurable;\n        desc = rewriteDescriptor(obj, prop, desc);\n        return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n    }\n    function isUnconfigurable(obj, prop) {\n        return obj && obj[unconfigurablesKey] && obj[unconfigurablesKey][prop];\n    }\n    function rewriteDescriptor(obj, prop, desc) {\n        // issue-927, if the desc is frozen, don't try to change the desc\n        if (!Object.isFrozen(desc)) {\n            desc.configurable = true;\n        }\n        if (!desc.configurable) {\n            // issue-927, if the obj is frozen, don't try to set the desc to obj\n            if (!obj[unconfigurablesKey] && !Object.isFrozen(obj)) {\n                _defineProperty(obj, unconfigurablesKey, { writable: true, value: {} });\n            }\n            if (obj[unconfigurablesKey]) {\n                obj[unconfigurablesKey][prop] = true;\n            }\n        }\n        return desc;\n    }\n    function _tryDefineProperty(obj, prop, desc, originalConfigurableFlag) {\n        try {\n            return _defineProperty(obj, prop, desc);\n        }\n        catch (error) {\n            if (desc.configurable) {\n                // In case of errors, when the configurable flag was likely set by rewriteDescriptor(),\n                // let's retry with the original flag value\n                if (typeof originalConfigurableFlag == 'undefined') {\n                    delete desc.configurable;\n                }\n                else {\n                    desc.configurable = originalConfigurableFlag;\n                }\n                try {\n                    return _defineProperty(obj, prop, desc);\n                }\n                catch (error) {\n                    var swallowError = false;\n                    if (prop === 'createdCallback' || prop === 'attachedCallback' ||\n                        prop === 'detachedCallback' || prop === 'attributeChangedCallback') {\n                        // We only swallow the error in registerElement patch\n                        // this is the work around since some applications\n                        // fail if we throw the error\n                        swallowError = true;\n                    }\n                    if (!swallowError) {\n                        throw error;\n                    }\n                    // TODO: @JiaLiPassion, Some application such as `registerElement` patch\n                    // still need to swallow the error, in the future after these applications\n                    // are updated, the following logic can be removed.\n                    var descJson = null;\n                    try {\n                        descJson = JSON.stringify(desc);\n                    }\n                    catch (error) {\n                        descJson = desc.toString();\n                    }\n                    console.log(\"Attempting to configure '\".concat(prop, \"' with descriptor '\").concat(descJson, \"' on object '\").concat(obj, \"' and got error, giving up: \").concat(error));\n                }\n            }\n            else {\n                throw error;\n            }\n        }\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function eventTargetLegacyPatch(_global, api) {\n        var _b = api.getGlobalObjects(), eventNames = _b.eventNames, globalSources = _b.globalSources, zoneSymbolEventNames = _b.zoneSymbolEventNames, TRUE_STR = _b.TRUE_STR, FALSE_STR = _b.FALSE_STR, ZONE_SYMBOL_PREFIX = _b.ZONE_SYMBOL_PREFIX;\n        var WTF_ISSUE_555 = 'Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video';\n        var NO_EVENT_TARGET = 'ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket'\n            .split(',');\n        var EVENT_TARGET = 'EventTarget';\n        var apis = [];\n        var isWtf = _global['wtf'];\n        var WTF_ISSUE_555_ARRAY = WTF_ISSUE_555.split(',');\n        if (isWtf) {\n            // Workaround for: https://github.com/google/tracing-framework/issues/555\n            apis = WTF_ISSUE_555_ARRAY.map(function (v) { return 'HTML' + v + 'Element'; }).concat(NO_EVENT_TARGET);\n        }\n        else if (_global[EVENT_TARGET]) {\n            apis.push(EVENT_TARGET);\n        }\n        else {\n            // Note: EventTarget is not available in all browsers,\n            // if it's not available, we instead patch the APIs in the IDL that inherit from EventTarget\n            apis = NO_EVENT_TARGET;\n        }\n        var isDisableIECheck = _global['__Zone_disable_IE_check'] || false;\n        var isEnableCrossContextCheck = _global['__Zone_enable_cross_context_check'] || false;\n        var ieOrEdge = api.isIEOrEdge();\n        var ADD_EVENT_LISTENER_SOURCE = '.addEventListener:';\n        var FUNCTION_WRAPPER = '[object FunctionWrapper]';\n        var BROWSER_TOOLS = 'function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }';\n        var pointerEventsMap = {\n            'MSPointerCancel': 'pointercancel',\n            'MSPointerDown': 'pointerdown',\n            'MSPointerEnter': 'pointerenter',\n            'MSPointerHover': 'pointerhover',\n            'MSPointerLeave': 'pointerleave',\n            'MSPointerMove': 'pointermove',\n            'MSPointerOut': 'pointerout',\n            'MSPointerOver': 'pointerover',\n            'MSPointerUp': 'pointerup'\n        };\n        //  predefine all __zone_symbol__ + eventName + true/false string\n        for (var i = 0; i < eventNames.length; i++) {\n            var eventName = eventNames[i];\n            var falseEventName = eventName + FALSE_STR;\n            var trueEventName = eventName + TRUE_STR;\n            var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n            var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n            zoneSymbolEventNames[eventName] = {};\n            zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n            zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n        }\n        //  predefine all task.source string\n        for (var i = 0; i < WTF_ISSUE_555_ARRAY.length; i++) {\n            var target = WTF_ISSUE_555_ARRAY[i];\n            var targets = globalSources[target] = {};\n            for (var j = 0; j < eventNames.length; j++) {\n                var eventName = eventNames[j];\n                targets[eventName] = target + ADD_EVENT_LISTENER_SOURCE + eventName;\n            }\n        }\n        var checkIEAndCrossContext = function (nativeDelegate, delegate, target, args) {\n            if (!isDisableIECheck && ieOrEdge) {\n                if (isEnableCrossContextCheck) {\n                    try {\n                        var testString = delegate.toString();\n                        if ((testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS)) {\n                            nativeDelegate.apply(target, args);\n                            return false;\n                        }\n                    }\n                    catch (error) {\n                        nativeDelegate.apply(target, args);\n                        return false;\n                    }\n                }\n                else {\n                    var testString = delegate.toString();\n                    if ((testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS)) {\n                        nativeDelegate.apply(target, args);\n                        return false;\n                    }\n                }\n            }\n            else if (isEnableCrossContextCheck) {\n                try {\n                    delegate.toString();\n                }\n                catch (error) {\n                    nativeDelegate.apply(target, args);\n                    return false;\n                }\n            }\n            return true;\n        };\n        var apiTypes = [];\n        for (var i = 0; i < apis.length; i++) {\n            var type = _global[apis[i]];\n            apiTypes.push(type && type.prototype);\n        }\n        // vh is validateHandler to check event handler\n        // is valid or not(for security check)\n        api.patchEventTarget(_global, api, apiTypes, {\n            vh: checkIEAndCrossContext,\n            transferEventName: function (eventName) {\n                var pointerEventName = pointerEventsMap[eventName];\n                return pointerEventName || eventName;\n            }\n        });\n        Zone[api.symbol('patchEventTarget')] = !!_global[EVENT_TARGET];\n        return true;\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    // we have to patch the instance since the proto is non-configurable\n    function apply(api, _global) {\n        var _b = api.getGlobalObjects(), ADD_EVENT_LISTENER_STR = _b.ADD_EVENT_LISTENER_STR, REMOVE_EVENT_LISTENER_STR = _b.REMOVE_EVENT_LISTENER_STR;\n        var WS = _global.WebSocket;\n        // On Safari window.EventTarget doesn't exist so need to patch WS add/removeEventListener\n        // On older Chrome, no need since EventTarget was already patched\n        if (!_global.EventTarget) {\n            api.patchEventTarget(_global, api, [WS.prototype]);\n        }\n        _global.WebSocket = function (x, y) {\n            var socket = arguments.length > 1 ? new WS(x, y) : new WS(x);\n            var proxySocket;\n            var proxySocketProto;\n            // Safari 7.0 has non-configurable own 'onmessage' and friends properties on the socket instance\n            var onmessageDesc = api.ObjectGetOwnPropertyDescriptor(socket, 'onmessage');\n            if (onmessageDesc && onmessageDesc.configurable === false) {\n                proxySocket = api.ObjectCreate(socket);\n                // socket have own property descriptor 'onopen', 'onmessage', 'onclose', 'onerror'\n                // but proxySocket not, so we will keep socket as prototype and pass it to\n                // patchOnProperties method\n                proxySocketProto = socket;\n                [ADD_EVENT_LISTENER_STR, REMOVE_EVENT_LISTENER_STR, 'send', 'close'].forEach(function (propName) {\n                    proxySocket[propName] = function () {\n                        var args = api.ArraySlice.call(arguments);\n                        if (propName === ADD_EVENT_LISTENER_STR || propName === REMOVE_EVENT_LISTENER_STR) {\n                            var eventName = args.length > 0 ? args[0] : undefined;\n                            if (eventName) {\n                                var propertySymbol = Zone.__symbol__('ON_PROPERTY' + eventName);\n                                socket[propertySymbol] = proxySocket[propertySymbol];\n                            }\n                        }\n                        return socket[propName].apply(socket, args);\n                    };\n                });\n            }\n            else {\n                // we can patch the real socket\n                proxySocket = socket;\n            }\n            api.patchOnProperties(proxySocket, ['close', 'error', 'message', 'open'], proxySocketProto);\n            return proxySocket;\n        };\n        var globalWebSocket = _global['WebSocket'];\n        for (var prop in WS) {\n            globalWebSocket[prop] = WS[prop];\n        }\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function propertyDescriptorLegacyPatch(api, _global) {\n        var _b = api.getGlobalObjects(), isNode = _b.isNode, isMix = _b.isMix;\n        if (isNode && !isMix) {\n            return;\n        }\n        if (!canPatchViaPropertyDescriptor(api, _global)) {\n            var supportsWebSocket = typeof WebSocket !== 'undefined';\n            // Safari, Android browsers (Jelly Bean)\n            patchViaCapturingAllTheEvents(api);\n            api.patchClass('XMLHttpRequest');\n            if (supportsWebSocket) {\n                apply(api, _global);\n            }\n            Zone[api.symbol('patchEvents')] = true;\n        }\n    }\n    function canPatchViaPropertyDescriptor(api, _global) {\n        var _b = api.getGlobalObjects(), isBrowser = _b.isBrowser, isMix = _b.isMix;\n        if ((isBrowser || isMix) &&\n            !api.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype, 'onclick') &&\n            typeof Element !== 'undefined') {\n            // WebKit https://bugs.webkit.org/show_bug.cgi?id=134364\n            // IDL interface attributes are not configurable\n            var desc = api.ObjectGetOwnPropertyDescriptor(Element.prototype, 'onclick');\n            if (desc && !desc.configurable)\n                return false;\n            // try to use onclick to detect whether we can patch via propertyDescriptor\n            // because XMLHttpRequest is not available in service worker\n            if (desc) {\n                api.ObjectDefineProperty(Element.prototype, 'onclick', {\n                    enumerable: true,\n                    configurable: true,\n                    get: function () {\n                        return true;\n                    }\n                });\n                var div = document.createElement('div');\n                var result = !!div.onclick;\n                api.ObjectDefineProperty(Element.prototype, 'onclick', desc);\n                return result;\n            }\n        }\n        var XMLHttpRequest = _global['XMLHttpRequest'];\n        if (!XMLHttpRequest) {\n            // XMLHttpRequest is not available in service worker\n            return false;\n        }\n        var ON_READY_STATE_CHANGE = 'onreadystatechange';\n        var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n        var xhrDesc = api.ObjectGetOwnPropertyDescriptor(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE);\n        // add enumerable and configurable here because in opera\n        // by default XMLHttpRequest.prototype.onreadystatechange is undefined\n        // without adding enumerable and configurable will cause onreadystatechange\n        // non-configurable\n        // and if XMLHttpRequest.prototype.onreadystatechange is undefined,\n        // we should set a real desc instead a fake one\n        if (xhrDesc) {\n            api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n                enumerable: true,\n                configurable: true,\n                get: function () {\n                    return true;\n                }\n            });\n            var req = new XMLHttpRequest();\n            var result = !!req.onreadystatechange;\n            // restore original desc\n            api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, xhrDesc || {});\n            return result;\n        }\n        else {\n            var SYMBOL_FAKE_ONREADYSTATECHANGE_1 = api.symbol('fake');\n            api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n                enumerable: true,\n                configurable: true,\n                get: function () {\n                    return this[SYMBOL_FAKE_ONREADYSTATECHANGE_1];\n                },\n                set: function (value) {\n                    this[SYMBOL_FAKE_ONREADYSTATECHANGE_1] = value;\n                }\n            });\n            var req = new XMLHttpRequest();\n            var detectFunc = function () { };\n            req.onreadystatechange = detectFunc;\n            var result = req[SYMBOL_FAKE_ONREADYSTATECHANGE_1] === detectFunc;\n            req.onreadystatechange = null;\n            return result;\n        }\n    }\n    var globalEventHandlersEventNames = [\n        'abort',\n        'animationcancel',\n        'animationend',\n        'animationiteration',\n        'auxclick',\n        'beforeinput',\n        'blur',\n        'cancel',\n        'canplay',\n        'canplaythrough',\n        'change',\n        'compositionstart',\n        'compositionupdate',\n        'compositionend',\n        'cuechange',\n        'click',\n        'close',\n        'contextmenu',\n        'curechange',\n        'dblclick',\n        'drag',\n        'dragend',\n        'dragenter',\n        'dragexit',\n        'dragleave',\n        'dragover',\n        'drop',\n        'durationchange',\n        'emptied',\n        'ended',\n        'error',\n        'focus',\n        'focusin',\n        'focusout',\n        'gotpointercapture',\n        'input',\n        'invalid',\n        'keydown',\n        'keypress',\n        'keyup',\n        'load',\n        'loadstart',\n        'loadeddata',\n        'loadedmetadata',\n        'lostpointercapture',\n        'mousedown',\n        'mouseenter',\n        'mouseleave',\n        'mousemove',\n        'mouseout',\n        'mouseover',\n        'mouseup',\n        'mousewheel',\n        'orientationchange',\n        'pause',\n        'play',\n        'playing',\n        'pointercancel',\n        'pointerdown',\n        'pointerenter',\n        'pointerleave',\n        'pointerlockchange',\n        'mozpointerlockchange',\n        'webkitpointerlockerchange',\n        'pointerlockerror',\n        'mozpointerlockerror',\n        'webkitpointerlockerror',\n        'pointermove',\n        'pointout',\n        'pointerover',\n        'pointerup',\n        'progress',\n        'ratechange',\n        'reset',\n        'resize',\n        'scroll',\n        'seeked',\n        'seeking',\n        'select',\n        'selectionchange',\n        'selectstart',\n        'show',\n        'sort',\n        'stalled',\n        'submit',\n        'suspend',\n        'timeupdate',\n        'volumechange',\n        'touchcancel',\n        'touchmove',\n        'touchstart',\n        'touchend',\n        'transitioncancel',\n        'transitionend',\n        'waiting',\n        'wheel'\n    ];\n    var documentEventNames = [\n        'afterscriptexecute', 'beforescriptexecute', 'DOMContentLoaded', 'freeze', 'fullscreenchange',\n        'mozfullscreenchange', 'webkitfullscreenchange', 'msfullscreenchange', 'fullscreenerror',\n        'mozfullscreenerror', 'webkitfullscreenerror', 'msfullscreenerror', 'readystatechange',\n        'visibilitychange', 'resume'\n    ];\n    var windowEventNames = [\n        'absolutedeviceorientation',\n        'afterinput',\n        'afterprint',\n        'appinstalled',\n        'beforeinstallprompt',\n        'beforeprint',\n        'beforeunload',\n        'devicelight',\n        'devicemotion',\n        'deviceorientation',\n        'deviceorientationabsolute',\n        'deviceproximity',\n        'hashchange',\n        'languagechange',\n        'message',\n        'mozbeforepaint',\n        'offline',\n        'online',\n        'paint',\n        'pageshow',\n        'pagehide',\n        'popstate',\n        'rejectionhandled',\n        'storage',\n        'unhandledrejection',\n        'unload',\n        'userproximity',\n        'vrdisplayconnected',\n        'vrdisplaydisconnected',\n        'vrdisplaypresentchange'\n    ];\n    var htmlElementEventNames = [\n        'beforecopy', 'beforecut', 'beforepaste', 'copy', 'cut', 'paste', 'dragstart', 'loadend',\n        'animationstart', 'search', 'transitionrun', 'transitionstart', 'webkitanimationend',\n        'webkitanimationiteration', 'webkitanimationstart', 'webkittransitionend'\n    ];\n    var ieElementEventNames = [\n        'activate',\n        'afterupdate',\n        'ariarequest',\n        'beforeactivate',\n        'beforedeactivate',\n        'beforeeditfocus',\n        'beforeupdate',\n        'cellchange',\n        'controlselect',\n        'dataavailable',\n        'datasetchanged',\n        'datasetcomplete',\n        'errorupdate',\n        'filterchange',\n        'layoutcomplete',\n        'losecapture',\n        'move',\n        'moveend',\n        'movestart',\n        'propertychange',\n        'resizeend',\n        'resizestart',\n        'rowenter',\n        'rowexit',\n        'rowsdelete',\n        'rowsinserted',\n        'command',\n        'compassneedscalibration',\n        'deactivate',\n        'help',\n        'mscontentzoom',\n        'msmanipulationstatechanged',\n        'msgesturechange',\n        'msgesturedoubletap',\n        'msgestureend',\n        'msgesturehold',\n        'msgesturestart',\n        'msgesturetap',\n        'msgotpointercapture',\n        'msinertiastart',\n        'mslostpointercapture',\n        'mspointercancel',\n        'mspointerdown',\n        'mspointerenter',\n        'mspointerhover',\n        'mspointerleave',\n        'mspointermove',\n        'mspointerout',\n        'mspointerover',\n        'mspointerup',\n        'pointerout',\n        'mssitemodejumplistitemremoved',\n        'msthumbnailclick',\n        'stop',\n        'storagecommit'\n    ];\n    var webglEventNames = ['webglcontextrestored', 'webglcontextlost', 'webglcontextcreationerror'];\n    var formEventNames = ['autocomplete', 'autocompleteerror'];\n    var detailEventNames = ['toggle'];\n    var eventNames = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], globalEventHandlersEventNames, true), webglEventNames, true), formEventNames, true), detailEventNames, true), documentEventNames, true), windowEventNames, true), htmlElementEventNames, true), ieElementEventNames, true);\n    // Whenever any eventListener fires, we check the eventListener target and all parents\n    // for `onwhatever` properties and replace them with zone-bound functions\n    // - Chrome (for now)\n    function patchViaCapturingAllTheEvents(api) {\n        var unboundKey = api.symbol('unbound');\n        var _loop_5 = function (i) {\n            var property = eventNames[i];\n            var onproperty = 'on' + property;\n            self.addEventListener(property, function (event) {\n                var elt = event.target, bound, source;\n                if (elt) {\n                    source = elt.constructor['name'] + '.' + onproperty;\n                }\n                else {\n                    source = 'unknown.' + onproperty;\n                }\n                while (elt) {\n                    if (elt[onproperty] && !elt[onproperty][unboundKey]) {\n                        bound = api.wrapWithCurrentZone(elt[onproperty], source);\n                        bound[unboundKey] = elt[onproperty];\n                        elt[onproperty] = bound;\n                    }\n                    elt = elt.parentElement;\n                }\n            }, true);\n        };\n        for (var i = 0; i < eventNames.length; i++) {\n            _loop_5(i);\n        }\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function registerElementPatch(_global, api) {\n        var _b = api.getGlobalObjects(), isBrowser = _b.isBrowser, isMix = _b.isMix;\n        if ((!isBrowser && !isMix) || !('registerElement' in _global.document)) {\n            return;\n        }\n        var callbacks = ['createdCallback', 'attachedCallback', 'detachedCallback', 'attributeChangedCallback'];\n        api.patchCallbacks(api, document, 'Document', 'registerElement', callbacks);\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    (function (_global) {\n        var symbolPrefix = _global['__Zone_symbol_prefix'] || '__zone_symbol__';\n        function __symbol__(name) {\n            return symbolPrefix + name;\n        }\n        _global[__symbol__('legacyPatch')] = function () {\n            var Zone = _global['Zone'];\n            Zone.__load_patch('defineProperty', function (global, Zone, api) {\n                api._redefineProperty = _redefineProperty;\n                propertyPatch();\n            });\n            Zone.__load_patch('registerElement', function (global, Zone, api) {\n                registerElementPatch(global, api);\n            });\n            Zone.__load_patch('EventTargetLegacy', function (global, Zone, api) {\n                eventTargetLegacyPatch(global, api);\n                propertyDescriptorLegacyPatch(api, global);\n            });\n        };\n    })(typeof window !== 'undefined' ?\n        window :\n        typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {});\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var taskSymbol = zoneSymbol$1('zoneTask');\n    function patchTimer(window, setName, cancelName, nameSuffix) {\n        var setNative = null;\n        var clearNative = null;\n        setName += nameSuffix;\n        cancelName += nameSuffix;\n        var tasksByHandleId = {};\n        function scheduleTask(task) {\n            var data = task.data;\n            data.args[0] = function () {\n                return task.invoke.apply(this, arguments);\n            };\n            data.handleId = setNative.apply(window, data.args);\n            return task;\n        }\n        function clearTask(task) {\n            return clearNative.call(window, task.data.handleId);\n        }\n        setNative =\n            patchMethod(window, setName, function (delegate) { return function (self, args) {\n                if (typeof args[0] === 'function') {\n                    var options_1 = {\n                        isPeriodic: nameSuffix === 'Interval',\n                        delay: (nameSuffix === 'Timeout' || nameSuffix === 'Interval') ? args[1] || 0 :\n                            undefined,\n                        args: args\n                    };\n                    var callback_1 = args[0];\n                    args[0] = function timer() {\n                        try {\n                            return callback_1.apply(this, arguments);\n                        }\n                        finally {\n                            // issue-934, task will be cancelled\n                            // even it is a periodic task such as\n                            // setInterval\n                            // https://github.com/angular/angular/issues/40387\n                            // Cleanup tasksByHandleId should be handled before scheduleTask\n                            // Since some zoneSpec may intercept and doesn't trigger\n                            // scheduleFn(scheduleTask) provided here.\n                            if (!(options_1.isPeriodic)) {\n                                if (typeof options_1.handleId === 'number') {\n                                    // in non-nodejs env, we remove timerId\n                                    // from local cache\n                                    delete tasksByHandleId[options_1.handleId];\n                                }\n                                else if (options_1.handleId) {\n                                    // Node returns complex objects as handleIds\n                                    // we remove task reference from timer object\n                                    options_1.handleId[taskSymbol] = null;\n                                }\n                            }\n                        }\n                    };\n                    var task = scheduleMacroTaskWithCurrentZone(setName, args[0], options_1, scheduleTask, clearTask);\n                    if (!task) {\n                        return task;\n                    }\n                    // Node.js must additionally support the ref and unref functions.\n                    var handle = task.data.handleId;\n                    if (typeof handle === 'number') {\n                        // for non nodejs env, we save handleId: task\n                        // mapping in local cache for clearTimeout\n                        tasksByHandleId[handle] = task;\n                    }\n                    else if (handle) {\n                        // for nodejs env, we save task\n                        // reference in timerId Object for clearTimeout\n                        handle[taskSymbol] = task;\n                    }\n                    // check whether handle is null, because some polyfill or browser\n                    // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n                    if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' &&\n                        typeof handle.unref === 'function') {\n                        task.ref = handle.ref.bind(handle);\n                        task.unref = handle.unref.bind(handle);\n                    }\n                    if (typeof handle === 'number' || handle) {\n                        return handle;\n                    }\n                    return task;\n                }\n                else {\n                    // cause an error by calling it directly.\n                    return delegate.apply(window, args);\n                }\n            }; });\n        clearNative =\n            patchMethod(window, cancelName, function (delegate) { return function (self, args) {\n                var id = args[0];\n                var task;\n                if (typeof id === 'number') {\n                    // non nodejs env.\n                    task = tasksByHandleId[id];\n                }\n                else {\n                    // nodejs env.\n                    task = id && id[taskSymbol];\n                    // other environments.\n                    if (!task) {\n                        task = id;\n                    }\n                }\n                if (task && typeof task.type === 'string') {\n                    if (task.state !== 'notScheduled' &&\n                        (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n                        if (typeof id === 'number') {\n                            delete tasksByHandleId[id];\n                        }\n                        else if (id) {\n                            id[taskSymbol] = null;\n                        }\n                        // Do not cancel already canceled functions\n                        task.zone.cancelTask(task);\n                    }\n                }\n                else {\n                    // cause an error by calling it directly.\n                    delegate.apply(window, args);\n                }\n            }; });\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function patchCustomElements(_global, api) {\n        var _b = api.getGlobalObjects(), isBrowser = _b.isBrowser, isMix = _b.isMix;\n        if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n            return;\n        }\n        var callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n        api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function eventTargetPatch(_global, api) {\n        if (Zone[api.symbol('patchEventTarget')]) {\n            // EventTarget is already patched.\n            return;\n        }\n        var _b = api.getGlobalObjects(), eventNames = _b.eventNames, zoneSymbolEventNames = _b.zoneSymbolEventNames, TRUE_STR = _b.TRUE_STR, FALSE_STR = _b.FALSE_STR, ZONE_SYMBOL_PREFIX = _b.ZONE_SYMBOL_PREFIX;\n        //  predefine all __zone_symbol__ + eventName + true/false string\n        for (var i = 0; i < eventNames.length; i++) {\n            var eventName = eventNames[i];\n            var falseEventName = eventName + FALSE_STR;\n            var trueEventName = eventName + TRUE_STR;\n            var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n            var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n            zoneSymbolEventNames[eventName] = {};\n            zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n            zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n        }\n        var EVENT_TARGET = _global['EventTarget'];\n        if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n            return;\n        }\n        api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n        return true;\n    }\n    function patchEvent(global, api) {\n        api.patchEventPrototype(global, api);\n    }\n    /**\n     * @license\n     * Copyright Google LLC All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    Zone.__load_patch('legacy', function (global) {\n        var legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('queueMicrotask', function (global, Zone, api) {\n        api.patchMethod(global, 'queueMicrotask', function (delegate) {\n            return function (self, args) {\n                Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n            };\n        });\n    });\n    Zone.__load_patch('timers', function (global) {\n        var set = 'set';\n        var clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', function (global) {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', function (global, Zone) {\n        var blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (var i = 0; i < blockingMethods.length; i++) {\n            var name_2 = blockingMethods[i];\n            patchMethod(global, name_2, function (delegate, symbol, name) {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', function (global, Zone, api) {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        var XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', function (global, Zone, api) {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', function (global, Zone, api) {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', function (global, Zone, api) {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', function (global, Zone, api) {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', function (global, Zone, api) {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', function (global, Zone) {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        var XHR_TASK = zoneSymbol$1('xhrTask');\n        var XHR_SYNC = zoneSymbol$1('xhrSync');\n        var XHR_LISTENER = zoneSymbol$1('xhrListener');\n        var XHR_SCHEDULED = zoneSymbol$1('xhrScheduled');\n        var XHR_URL = zoneSymbol$1('xhrURL');\n        var XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol$1('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            var XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            var oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            var oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                var XMLHttpRequestEventTarget_1 = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget_1) {\n                    var XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget_1.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            var READY_STATE_CHANGE = 'readystatechange';\n            var SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                var data = task.data;\n                var target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                var listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                var newListener = target[XHR_LISTENER] = function () {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            var loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                var oriInvoke_1 = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    var loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (var i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke_1.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                };\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                var storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                var data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            var openNative = patchMethod(XMLHttpRequestPrototype, 'open', function () { return function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            }; });\n            var XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            var fetchTaskAborting = zoneSymbol$1('fetchTaskAborting');\n            var fetchTaskScheduling = zoneSymbol$1('fetchTaskScheduling');\n            var sendNative = patchMethod(XMLHttpRequestPrototype, 'send', function () { return function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    var options = { target: self, url: self[XHR_URL], isPeriodic: false, args: args, aborted: false };\n                    var task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            }; });\n            var abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', function () { return function (self, args) {\n                var task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            }; });\n        }\n    });\n    Zone.__load_patch('geolocation', function (global) {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', function (global, Zone) {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                var eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach(function (eventTask) {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    var PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        var evt = new PromiseRejectionEvent(evtName, { promise: e.promise, reason: e.rejection });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol$1('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol$1('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n}));\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGL,IAAI,CAACG,MAAM,EAAEG,EAAE,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACjF,IAAIE,EAAE,IAAI,EAAEF,CAAC,IAAIJ,IAAI,CAAC,EAAE;MACpB,IAAI,CAACM,EAAE,EAAEA,EAAE,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACV,IAAI,EAAE,CAAC,EAAEI,CAAC,CAAC;MACpDE,EAAE,CAACF,CAAC,CAAC,GAAGJ,IAAI,CAACI,CAAC,CAAC;IACnB;EACJ;EACA,OAAOL,EAAE,CAACY,MAAM,CAACL,EAAE,IAAIC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;AAC5D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUY,OAAO,EAAE;EAChB,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACD,OAAO,CAAC,GACxDA,OAAO,EAAE;AACjB,CAAC,EAAG,YAAY;EACZ,YAAY;;EACZ;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,CAAE,UAAUG,MAAM,EAAE;IAChB,IAAIC,WAAW,GAAGD,MAAM,CAAC,aAAa,CAAC;IACvC,SAASE,IAAI,CAACC,IAAI,EAAE;MAChBF,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACE,IAAI,CAAC;IACnE;IACA,SAASC,kBAAkB,CAACD,IAAI,EAAEE,KAAK,EAAE;MACrCJ,WAAW,IAAIA,WAAW,CAAC,SAAS,CAAC,IAAIA,WAAW,CAAC,SAAS,CAAC,CAACE,IAAI,EAAEE,KAAK,CAAC;IAChF;IACAH,IAAI,CAAC,MAAM,CAAC;IACZ;IACA;IACA;IACA,IAAII,YAAY,GAAGN,MAAM,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;IACtE,SAASO,UAAU,CAACJ,IAAI,EAAE;MACtB,OAAOG,YAAY,GAAGH,IAAI;IAC9B;IACA,IAAIK,cAAc,GAAGR,MAAM,CAACO,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;IAC3E,IAAIP,MAAM,CAAC,MAAM,CAAC,EAAE;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIQ,cAAc,IAAI,OAAOR,MAAM,CAAC,MAAM,CAAC,CAACO,UAAU,KAAK,UAAU,EAAE;QACnE,MAAM,IAAIE,KAAK,CAAC,sBAAsB,CAAC;MAC3C,CAAC,MACI;QACD,OAAOT,MAAM,CAAC,MAAM,CAAC;MACzB;IACJ;IACA,IAAIU,IAAI,GAAG,aAAe,YAAY;MAClC,SAASA,IAAI,CAACC,MAAM,EAAEC,QAAQ,EAAE;QAC5B,IAAI,CAACC,OAAO,GAAGF,MAAM;QACrB,IAAI,CAACG,KAAK,GAAGF,QAAQ,GAAGA,QAAQ,CAACT,IAAI,IAAI,SAAS,GAAG,QAAQ;QAC7D,IAAI,CAACY,WAAW,GAAGH,QAAQ,IAAIA,QAAQ,CAACI,UAAU,IAAI,CAAC,CAAC;QACxD,IAAI,CAACC,aAAa,GACd,IAAIC,aAAa,CAAC,IAAI,EAAE,IAAI,CAACL,OAAO,IAAI,IAAI,CAACA,OAAO,CAACI,aAAa,EAAEL,QAAQ,CAAC;MACrF;MACAF,IAAI,CAACS,iBAAiB,GAAG,YAAY;QACjC,IAAInB,MAAM,CAAC,SAAS,CAAC,KAAKoB,OAAO,CAAC,kBAAkB,CAAC,EAAE;UACnD,MAAM,IAAIX,KAAK,CAAC,uEAAuE,GACnF,yBAAyB,GACzB,+DAA+D,GAC/D,kFAAkF,GAClF,sDAAsD,CAAC;QAC/D;MACJ,CAAC;MACDY,MAAM,CAACC,cAAc,CAACZ,IAAI,EAAE,MAAM,EAAE;QAChCa,GAAG,EAAE,YAAY;UACb,IAAIC,IAAI,GAAGd,IAAI,CAACe,OAAO;UACvB,OAAOD,IAAI,CAACb,MAAM,EAAE;YAChBa,IAAI,GAAGA,IAAI,CAACb,MAAM;UACtB;UACA,OAAOa,IAAI;QACf,CAAC;QACDE,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFN,MAAM,CAACC,cAAc,CAACZ,IAAI,EAAE,SAAS,EAAE;QACnCa,GAAG,EAAE,YAAY;UACb,OAAOK,iBAAiB,CAACJ,IAAI;QACjC,CAAC;QACDE,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFN,MAAM,CAACC,cAAc,CAACZ,IAAI,EAAE,aAAa,EAAE;QACvCa,GAAG,EAAE,YAAY;UACb,OAAOM,YAAY;QACvB,CAAC;QACDH,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACF;MACAjB,IAAI,CAACoB,YAAY,GAAG,UAAU3B,IAAI,EAAE4B,EAAE,EAAEC,eAAe,EAAE;QACrD,IAAIA,eAAe,KAAK,KAAK,CAAC,EAAE;UAAEA,eAAe,GAAG,KAAK;QAAE;QAC3D,IAAIZ,OAAO,CAACa,cAAc,CAAC9B,IAAI,CAAC,EAAE;UAC9B;UACA;UACA;UACA,IAAI,CAAC6B,eAAe,IAAIxB,cAAc,EAAE;YACpC,MAAMC,KAAK,CAAC,wBAAwB,GAAGN,IAAI,CAAC;UAChD;QACJ,CAAC,MACI,IAAI,CAACH,MAAM,CAAC,iBAAiB,GAAGG,IAAI,CAAC,EAAE;UACxC,IAAI+B,QAAQ,GAAG,OAAO,GAAG/B,IAAI;UAC7BD,IAAI,CAACgC,QAAQ,CAAC;UACdd,OAAO,CAACjB,IAAI,CAAC,GAAG4B,EAAE,CAAC/B,MAAM,EAAEU,IAAI,EAAEyB,IAAI,CAAC;UACtC/B,kBAAkB,CAAC8B,QAAQ,EAAEA,QAAQ,CAAC;QAC1C;MACJ,CAAC;MACDb,MAAM,CAACC,cAAc,CAACZ,IAAI,CAACjB,SAAS,EAAE,QAAQ,EAAE;QAC5C8B,GAAG,EAAE,YAAY;UACb,OAAO,IAAI,CAACV,OAAO;QACvB,CAAC;QACDa,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFN,MAAM,CAACC,cAAc,CAACZ,IAAI,CAACjB,SAAS,EAAE,MAAM,EAAE;QAC1C8B,GAAG,EAAE,YAAY;UACb,OAAO,IAAI,CAACT,KAAK;QACrB,CAAC;QACDY,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFjB,IAAI,CAACjB,SAAS,CAAC8B,GAAG,GAAG,UAAUa,GAAG,EAAE;QAChC,IAAIZ,IAAI,GAAG,IAAI,CAACa,WAAW,CAACD,GAAG,CAAC;QAChC,IAAIZ,IAAI,EACJ,OAAOA,IAAI,CAACT,WAAW,CAACqB,GAAG,CAAC;MACpC,CAAC;MACD1B,IAAI,CAACjB,SAAS,CAAC4C,WAAW,GAAG,UAAUD,GAAG,EAAE;QACxC,IAAIX,OAAO,GAAG,IAAI;QAClB,OAAOA,OAAO,EAAE;UACZ,IAAIA,OAAO,CAACV,WAAW,CAACkB,cAAc,CAACG,GAAG,CAAC,EAAE;YACzC,OAAOX,OAAO;UAClB;UACAA,OAAO,GAAGA,OAAO,CAACZ,OAAO;QAC7B;QACA,OAAO,IAAI;MACf,CAAC;MACDH,IAAI,CAACjB,SAAS,CAAC6C,IAAI,GAAG,UAAU1B,QAAQ,EAAE;QACtC,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIH,KAAK,CAAC,oBAAoB,CAAC;QACzC,OAAO,IAAI,CAACQ,aAAa,CAACqB,IAAI,CAAC,IAAI,EAAE1B,QAAQ,CAAC;MAClD,CAAC;MACDF,IAAI,CAACjB,SAAS,CAAC8C,IAAI,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAE;QAC9C,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;UAChC,MAAM,IAAI/B,KAAK,CAAC,0BAA0B,GAAG+B,QAAQ,CAAC;QAC1D;QACA,IAAIE,SAAS,GAAG,IAAI,CAACzB,aAAa,CAAC0B,SAAS,CAAC,IAAI,EAAEH,QAAQ,EAAEC,MAAM,CAAC;QACpE,IAAIjB,IAAI,GAAG,IAAI;QACf,OAAO,YAAY;UACf,OAAOA,IAAI,CAACoB,UAAU,CAACF,SAAS,EAAE,IAAI,EAAEvD,SAAS,EAAEsD,MAAM,CAAC;QAC9D,CAAC;MACL,CAAC;MACD/B,IAAI,CAACjB,SAAS,CAACoD,GAAG,GAAG,UAAUL,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,EAAE;QACnEb,iBAAiB,GAAG;UAAEjB,MAAM,EAAEiB,iBAAiB;UAAEJ,IAAI,EAAE;QAAK,CAAC;QAC7D,IAAI;UACA,OAAO,IAAI,CAACP,aAAa,CAAC+B,MAAM,CAAC,IAAI,EAAER,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,CAAC;QAClF,CAAC,SACO;UACJb,iBAAiB,GAAGA,iBAAiB,CAACjB,MAAM;QAChD;MACJ,CAAC;MACDD,IAAI,CAACjB,SAAS,CAACmD,UAAU,GAAG,UAAUJ,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,EAAE;QAC1E,IAAIK,SAAS,KAAK,KAAK,CAAC,EAAE;UAAEA,SAAS,GAAG,IAAI;QAAE;QAC9ClB,iBAAiB,GAAG;UAAEjB,MAAM,EAAEiB,iBAAiB;UAAEJ,IAAI,EAAE;QAAK,CAAC;QAC7D,IAAI;UACA,IAAI;YACA,OAAO,IAAI,CAACP,aAAa,CAAC+B,MAAM,CAAC,IAAI,EAAER,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,CAAC;UAClF,CAAC,CACD,OAAOQ,KAAK,EAAE;YACV,IAAI,IAAI,CAAChC,aAAa,CAACiC,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;cAC7C,MAAMA,KAAK;YACf;UACJ;QACJ,CAAC,SACO;UACJrB,iBAAiB,GAAGA,iBAAiB,CAACjB,MAAM;QAChD;MACJ,CAAC;MACDD,IAAI,CAACjB,SAAS,CAAC0D,OAAO,GAAG,UAAUC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;QAC3D,IAAIK,IAAI,CAAC5B,IAAI,IAAI,IAAI,EAAE;UACnB,MAAM,IAAIf,KAAK,CAAC,6DAA6D,GACzE,CAAC2C,IAAI,CAAC5B,IAAI,IAAI6B,OAAO,EAAElD,IAAI,GAAG,eAAe,GAAG,IAAI,CAACA,IAAI,GAAG,GAAG,CAAC;QACxE;QACA;QACA;QACA;QACA,IAAIiD,IAAI,CAACE,KAAK,KAAKC,YAAY,KAAKH,IAAI,CAACI,IAAI,KAAKC,SAAS,IAAIL,IAAI,CAACI,IAAI,KAAKE,SAAS,CAAC,EAAE;UACrF;QACJ;QACA,IAAIC,YAAY,GAAGP,IAAI,CAACE,KAAK,IAAIM,OAAO;QACxCD,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACD,OAAO,EAAEE,SAAS,CAAC;QACtDV,IAAI,CAACW,QAAQ,EAAE;QACf,IAAIC,YAAY,GAAGnC,YAAY;QAC/BA,YAAY,GAAGuB,IAAI;QACnBxB,iBAAiB,GAAG;UAAEjB,MAAM,EAAEiB,iBAAiB;UAAEJ,IAAI,EAAE;QAAK,CAAC;QAC7D,IAAI;UACA,IAAI4B,IAAI,CAACI,IAAI,IAAIE,SAAS,IAAIN,IAAI,CAACa,IAAI,IAAI,CAACb,IAAI,CAACa,IAAI,CAACC,UAAU,EAAE;YAC9Dd,IAAI,CAACe,QAAQ,GAAGC,SAAS;UAC7B;UACA,IAAI;YACA,OAAO,IAAI,CAACnD,aAAa,CAACoD,UAAU,CAAC,IAAI,EAAEjB,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;UAC1E,CAAC,CACD,OAAOE,KAAK,EAAE;YACV,IAAI,IAAI,CAAChC,aAAa,CAACiC,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;cAC7C,MAAMA,KAAK;YACf;UACJ;QACJ,CAAC,SACO;UACJ;UACA;UACA,IAAIG,IAAI,CAACE,KAAK,KAAKC,YAAY,IAAIH,IAAI,CAACE,KAAK,KAAKgB,OAAO,EAAE;YACvD,IAAIlB,IAAI,CAACI,IAAI,IAAIC,SAAS,IAAKL,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACC,UAAW,EAAE;cAC/DP,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEF,OAAO,CAAC;YAC1D,CAAC,MACI;cACDR,IAAI,CAACW,QAAQ,GAAG,CAAC;cACjB,IAAI,CAACQ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;cAC/BO,YAAY,IACRP,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEK,OAAO,EAAEL,YAAY,CAAC;YAC/D;UACJ;UACA3B,iBAAiB,GAAGA,iBAAiB,CAACjB,MAAM;UAC5CkB,YAAY,GAAGmC,YAAY;QAC/B;MACJ,CAAC;MACDtD,IAAI,CAACjB,SAAS,CAAC+E,YAAY,GAAG,UAAUpB,IAAI,EAAE;QAC1C,IAAIA,IAAI,CAAC5B,IAAI,IAAI4B,IAAI,CAAC5B,IAAI,KAAK,IAAI,EAAE;UACjC;UACA;UACA,IAAIiD,OAAO,GAAG,IAAI;UAClB,OAAOA,OAAO,EAAE;YACZ,IAAIA,OAAO,KAAKrB,IAAI,CAAC5B,IAAI,EAAE;cACvB,MAAMf,KAAK,CAAC,6BAA6B,CAACb,MAAM,CAAC,IAAI,CAACO,IAAI,EAAE,6CAA6C,CAAC,CAACP,MAAM,CAACwD,IAAI,CAAC5B,IAAI,CAACrB,IAAI,CAAC,CAAC;YACtI;YACAsE,OAAO,GAAGA,OAAO,CAAC9D,MAAM;UAC5B;QACJ;QACAyC,IAAI,CAACS,aAAa,CAACa,UAAU,EAAEnB,YAAY,CAAC;QAC5C,IAAIoB,aAAa,GAAG,EAAE;QACtBvB,IAAI,CAACwB,cAAc,GAAGD,aAAa;QACnCvB,IAAI,CAACyB,KAAK,GAAG,IAAI;QACjB,IAAI;UACAzB,IAAI,GAAG,IAAI,CAACnC,aAAa,CAACuD,YAAY,CAAC,IAAI,EAAEpB,IAAI,CAAC;QACtD,CAAC,CACD,OAAO0B,GAAG,EAAE;UACR;UACA;UACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEI,UAAU,EAAEnB,YAAY,CAAC;UACrD;UACA,IAAI,CAACtC,aAAa,CAACiC,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;UACzC,MAAMA,GAAG;QACb;QACA,IAAI1B,IAAI,CAACwB,cAAc,KAAKD,aAAa,EAAE;UACvC;UACA,IAAI,CAACJ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC;QAClC;QACA,IAAIA,IAAI,CAACE,KAAK,IAAIoB,UAAU,EAAE;UAC1BtB,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEY,UAAU,CAAC;QAC7C;QACA,OAAOtB,IAAI;MACf,CAAC;MACD1C,IAAI,CAACjB,SAAS,CAACsF,iBAAiB,GAAG,UAAUtC,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAE;QACjF,OAAO,IAAI,CAACR,YAAY,CAAC,IAAIS,QAAQ,CAACC,SAAS,EAAEzC,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEZ,SAAS,CAAC,CAAC;MACxG,CAAC;MACD1D,IAAI,CAACjB,SAAS,CAAC0F,iBAAiB,GAAG,UAAU1C,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;QAC/F,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACvB,SAAS,EAAEjB,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;MAC3G,CAAC;MACD1E,IAAI,CAACjB,SAAS,CAAC4F,iBAAiB,GAAG,UAAU5C,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;QAC/F,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACxB,SAAS,EAAEhB,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;MAC3G,CAAC;MACD1E,IAAI,CAACjB,SAAS,CAAC6F,UAAU,GAAG,UAAUlC,IAAI,EAAE;QACxC,IAAIA,IAAI,CAAC5B,IAAI,IAAI,IAAI,EACjB,MAAM,IAAIf,KAAK,CAAC,mEAAmE,GAC/E,CAAC2C,IAAI,CAAC5B,IAAI,IAAI6B,OAAO,EAAElD,IAAI,GAAG,eAAe,GAAG,IAAI,CAACA,IAAI,GAAG,GAAG,CAAC;QACxE,IAAIiD,IAAI,CAACE,KAAK,KAAKQ,SAAS,IAAIV,IAAI,CAACE,KAAK,KAAKM,OAAO,EAAE;UACpD;QACJ;QACAR,IAAI,CAACS,aAAa,CAAC0B,SAAS,EAAEzB,SAAS,EAAEF,OAAO,CAAC;QACjD,IAAI;UACA,IAAI,CAAC3C,aAAa,CAACqE,UAAU,CAAC,IAAI,EAAElC,IAAI,CAAC;QAC7C,CAAC,CACD,OAAO0B,GAAG,EAAE;UACR;UACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEiB,SAAS,CAAC;UACtC,IAAI,CAACtE,aAAa,CAACiC,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;UACzC,MAAMA,GAAG;QACb;QACA,IAAI,CAACP,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/BA,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEgC,SAAS,CAAC;QAC3CnC,IAAI,CAACW,QAAQ,GAAG,CAAC;QACjB,OAAOX,IAAI;MACf,CAAC;MACD1C,IAAI,CAACjB,SAAS,CAAC8E,gBAAgB,GAAG,UAAUnB,IAAI,EAAEoC,KAAK,EAAE;QACrD,IAAIb,aAAa,GAAGvB,IAAI,CAACwB,cAAc;QACvC,IAAIY,KAAK,IAAI,CAAC,CAAC,EAAE;UACbpC,IAAI,CAACwB,cAAc,GAAG,IAAI;QAC9B;QACA,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,aAAa,CAACvF,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC3CsF,aAAa,CAACtF,CAAC,CAAC,CAACkF,gBAAgB,CAACnB,IAAI,CAACI,IAAI,EAAEgC,KAAK,CAAC;QACvD;MACJ,CAAC;MACD,OAAO9E,IAAI;IACf,CAAC,EAAG;IACJ;IACAA,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAIkF,WAAW,GAAG;MACdtF,IAAI,EAAE,EAAE;MACRuF,SAAS,EAAE,UAAUC,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEC,YAAY,EAAE;QAAE,OAAOH,QAAQ,CAACI,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;MAAE,CAAC;MAC1GE,cAAc,EAAE,UAAUL,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEzC,IAAI,EAAE;QAAE,OAAOuC,QAAQ,CAACnB,YAAY,CAACqB,MAAM,EAAEzC,IAAI,CAAC;MAAE,CAAC;MACpG6C,YAAY,EAAE,UAAUN,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEzC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;QAAE,OAAO4C,QAAQ,CAACtB,UAAU,CAACwB,MAAM,EAAEzC,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;MAAE,CAAC;MAC5ImD,YAAY,EAAE,UAAUP,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEzC,IAAI,EAAE;QAAE,OAAOuC,QAAQ,CAACL,UAAU,CAACO,MAAM,EAAEzC,IAAI,CAAC;MAAE;IACnG,CAAC;IACD,IAAIlC,aAAa,GAAG,aAAe,YAAY;MAC3C,SAASA,aAAa,CAACM,IAAI,EAAE2E,cAAc,EAAEvF,QAAQ,EAAE;QACnD,IAAI,CAACwF,WAAW,GAAG;UAAE,WAAW,EAAE,CAAC;UAAE,WAAW,EAAE,CAAC;UAAE,WAAW,EAAE;QAAE,CAAC;QACrE,IAAI,CAAC5E,IAAI,GAAGA,IAAI;QAChB,IAAI,CAAC6E,eAAe,GAAGF,cAAc;QACrC,IAAI,CAACG,OAAO,GAAG1F,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC2F,MAAM,GAAG3F,QAAQ,GAAGuF,cAAc,CAACG,OAAO,CAAC;QAC5F,IAAI,CAACE,SAAS,GAAG5F,QAAQ,KAAKA,QAAQ,CAAC2F,MAAM,GAAGJ,cAAc,GAAGA,cAAc,CAACK,SAAS,CAAC;QAC1F,IAAI,CAACC,aAAa,GACd7F,QAAQ,KAAKA,QAAQ,CAAC2F,MAAM,GAAG,IAAI,CAAC/E,IAAI,GAAG2E,cAAc,CAACM,aAAa,CAAC;QAC5E,IAAI,CAACC,YAAY,GACb9F,QAAQ,KAAKA,QAAQ,CAAC+F,WAAW,GAAG/F,QAAQ,GAAGuF,cAAc,CAACO,YAAY,CAAC;QAC/E,IAAI,CAACE,cAAc,GACfhG,QAAQ,KAAKA,QAAQ,CAAC+F,WAAW,GAAGR,cAAc,GAAGA,cAAc,CAACS,cAAc,CAAC;QACvF,IAAI,CAACC,kBAAkB,GACnBjG,QAAQ,KAAKA,QAAQ,CAAC+F,WAAW,GAAG,IAAI,CAACnF,IAAI,GAAG2E,cAAc,CAACU,kBAAkB,CAAC;QACtF,IAAI,CAACC,SAAS,GAAGlG,QAAQ,KAAKA,QAAQ,CAACmG,QAAQ,GAAGnG,QAAQ,GAAGuF,cAAc,CAACW,SAAS,CAAC;QACtF,IAAI,CAACE,WAAW,GACZpG,QAAQ,KAAKA,QAAQ,CAACmG,QAAQ,GAAGZ,cAAc,GAAGA,cAAc,CAACa,WAAW,CAAC;QACjF,IAAI,CAACC,eAAe,GAChBrG,QAAQ,KAAKA,QAAQ,CAACmG,QAAQ,GAAG,IAAI,CAACvF,IAAI,GAAG2E,cAAc,CAACc,eAAe,CAAC;QAChF,IAAI,CAACC,cAAc,GACftG,QAAQ,KAAKA,QAAQ,CAACuG,aAAa,GAAGvG,QAAQ,GAAGuF,cAAc,CAACe,cAAc,CAAC;QACnF,IAAI,CAACE,gBAAgB,GACjBxG,QAAQ,KAAKA,QAAQ,CAACuG,aAAa,GAAGhB,cAAc,GAAGA,cAAc,CAACiB,gBAAgB,CAAC;QAC3F,IAAI,CAACC,oBAAoB,GACrBzG,QAAQ,KAAKA,QAAQ,CAACuG,aAAa,GAAG,IAAI,CAAC3F,IAAI,GAAG2E,cAAc,CAACkB,oBAAoB,CAAC;QAC1F,IAAI,CAACC,eAAe,GAChB1G,QAAQ,KAAKA,QAAQ,CAACoF,cAAc,GAAGpF,QAAQ,GAAGuF,cAAc,CAACmB,eAAe,CAAC;QACrF,IAAI,CAACC,iBAAiB,GAAG3G,QAAQ,KAC5BA,QAAQ,CAACoF,cAAc,GAAGG,cAAc,GAAGA,cAAc,CAACoB,iBAAiB,CAAC;QACjF,IAAI,CAACC,qBAAqB,GACtB5G,QAAQ,KAAKA,QAAQ,CAACoF,cAAc,GAAG,IAAI,CAACxE,IAAI,GAAG2E,cAAc,CAACqB,qBAAqB,CAAC;QAC5F,IAAI,CAACC,aAAa,GACd7G,QAAQ,KAAKA,QAAQ,CAACqF,YAAY,GAAGrF,QAAQ,GAAGuF,cAAc,CAACsB,aAAa,CAAC;QACjF,IAAI,CAACC,eAAe,GAChB9G,QAAQ,KAAKA,QAAQ,CAACqF,YAAY,GAAGE,cAAc,GAAGA,cAAc,CAACuB,eAAe,CAAC;QACzF,IAAI,CAACC,mBAAmB,GACpB/G,QAAQ,KAAKA,QAAQ,CAACqF,YAAY,GAAG,IAAI,CAACzE,IAAI,GAAG2E,cAAc,CAACwB,mBAAmB,CAAC;QACxF,IAAI,CAACC,aAAa,GACdhH,QAAQ,KAAKA,QAAQ,CAACsF,YAAY,GAAGtF,QAAQ,GAAGuF,cAAc,CAACyB,aAAa,CAAC;QACjF,IAAI,CAACC,eAAe,GAChBjH,QAAQ,KAAKA,QAAQ,CAACsF,YAAY,GAAGC,cAAc,GAAGA,cAAc,CAAC0B,eAAe,CAAC;QACzF,IAAI,CAACC,mBAAmB,GACpBlH,QAAQ,KAAKA,QAAQ,CAACsF,YAAY,GAAG,IAAI,CAAC1E,IAAI,GAAG2E,cAAc,CAAC2B,mBAAmB,CAAC;QACxF,IAAI,CAACC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,YAAY,GAAG,IAAI;QACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;QAC5B,IAAIC,eAAe,GAAGvH,QAAQ,IAAIA,QAAQ,CAAC8E,SAAS;QACpD,IAAI0C,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAU;QAC/D,IAAII,eAAe,IAAIC,aAAa,EAAE;UAClC;UACA;UACA,IAAI,CAACL,UAAU,GAAGI,eAAe,GAAGvH,QAAQ,GAAG6E,WAAW;UAC1D,IAAI,CAACuC,YAAY,GAAG7B,cAAc;UAClC,IAAI,CAAC8B,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACC,gBAAgB,GAAG1G,IAAI;UAC5B,IAAI,CAACZ,QAAQ,CAACoF,cAAc,EAAE;YAC1B,IAAI,CAACsB,eAAe,GAAG7B,WAAW;YAClC,IAAI,CAAC8B,iBAAiB,GAAGpB,cAAc;YACvC,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAAChG,IAAI;UAC1C;UACA,IAAI,CAACZ,QAAQ,CAACqF,YAAY,EAAE;YACxB,IAAI,CAACwB,aAAa,GAAGhC,WAAW;YAChC,IAAI,CAACiC,eAAe,GAAGvB,cAAc;YACrC,IAAI,CAACwB,mBAAmB,GAAG,IAAI,CAACnG,IAAI;UACxC;UACA,IAAI,CAACZ,QAAQ,CAACsF,YAAY,EAAE;YACxB,IAAI,CAAC0B,aAAa,GAAGnC,WAAW;YAChC,IAAI,CAACoC,eAAe,GAAG1B,cAAc;YACrC,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAACtG,IAAI;UACxC;QACJ;MACJ;MACAN,aAAa,CAACzB,SAAS,CAAC6C,IAAI,GAAG,UAAU+F,UAAU,EAAEzH,QAAQ,EAAE;QAC3D,OAAO,IAAI,CAAC0F,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAAChF,IAAI,EAAE6G,UAAU,EAAEzH,QAAQ,CAAC,GACtF,IAAIF,IAAI,CAAC2H,UAAU,EAAEzH,QAAQ,CAAC;MACtC,CAAC;MACDM,aAAa,CAACzB,SAAS,CAACkD,SAAS,GAAG,UAAU0F,UAAU,EAAE7F,QAAQ,EAAEC,MAAM,EAAE;QACxE,OAAO,IAAI,CAACiE,YAAY,GACpB,IAAI,CAACA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAEwB,UAAU,EAAE7F,QAAQ,EAAEC,MAAM,CAAC,GACzGD,QAAQ;MAChB,CAAC;MACDtB,aAAa,CAACzB,SAAS,CAACuD,MAAM,GAAG,UAAUqF,UAAU,EAAE7F,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,EAAE;QAC3F,OAAO,IAAI,CAACqE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAEoB,UAAU,EAAE7F,QAAQ,EAAEM,SAAS,EAAEC,SAAS,EAAEN,MAAM,CAAC,GACvID,QAAQ,CAAC8F,KAAK,CAACxF,SAAS,EAAEC,SAAS,CAAC;MAC5C,CAAC;MACD7B,aAAa,CAACzB,SAAS,CAACyD,WAAW,GAAG,UAAUmF,UAAU,EAAEpF,KAAK,EAAE;QAC/D,OAAO,IAAI,CAACiE,cAAc,GACtB,IAAI,CAACA,cAAc,CAACC,aAAa,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,oBAAoB,EAAEgB,UAAU,EAAEpF,KAAK,CAAC,GACtG,IAAI;MACZ,CAAC;MACD/B,aAAa,CAACzB,SAAS,CAAC+E,YAAY,GAAG,UAAU6D,UAAU,EAAEjF,IAAI,EAAE;QAC/D,IAAImF,UAAU,GAAGnF,IAAI;QACrB,IAAI,IAAI,CAACkE,eAAe,EAAE;UACtB,IAAI,IAAI,CAACS,UAAU,EAAE;YACjBQ,UAAU,CAAC3D,cAAc,CAAC4D,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAAC;UAC1D;UACA;UACAM,UAAU,GAAG,IAAI,CAACjB,eAAe,CAACtB,cAAc,CAAC,IAAI,CAACuB,iBAAiB,EAAE,IAAI,CAACC,qBAAqB,EAAEa,UAAU,EAAEjF,IAAI,CAAC;UACtH;UACA,IAAI,CAACmF,UAAU,EACXA,UAAU,GAAGnF,IAAI;QACzB,CAAC,MACI;UACD,IAAIA,IAAI,CAACqF,UAAU,EAAE;YACjBrF,IAAI,CAACqF,UAAU,CAACrF,IAAI,CAAC;UACzB,CAAC,MACI,IAAIA,IAAI,CAACI,IAAI,IAAI0B,SAAS,EAAE;YAC7BH,iBAAiB,CAAC3B,IAAI,CAAC;UAC3B,CAAC,MACI;YACD,MAAM,IAAI3C,KAAK,CAAC,6BAA6B,CAAC;UAClD;QACJ;QACA,OAAO8H,UAAU;MACrB,CAAC;MACDrH,aAAa,CAACzB,SAAS,CAAC4E,UAAU,GAAG,UAAUgE,UAAU,EAAEjF,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;QACnF,OAAO,IAAI,CAAC0E,aAAa,GAAG,IAAI,CAACA,aAAa,CAACxB,YAAY,CAAC,IAAI,CAACyB,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEU,UAAU,EAAEjF,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC,GAC/IK,IAAI,CAACZ,QAAQ,CAAC8F,KAAK,CAACxF,SAAS,EAAEC,SAAS,CAAC;MACjD,CAAC;MACD7B,aAAa,CAACzB,SAAS,CAAC6F,UAAU,GAAG,UAAU+C,UAAU,EAAEjF,IAAI,EAAE;QAC7D,IAAIsF,KAAK;QACT,IAAI,IAAI,CAACd,aAAa,EAAE;UACpBc,KAAK,GAAG,IAAI,CAACd,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC2B,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEO,UAAU,EAAEjF,IAAI,CAAC;QAC7G,CAAC,MACI;UACD,IAAI,CAACA,IAAI,CAACe,QAAQ,EAAE;YAChB,MAAM1D,KAAK,CAAC,wBAAwB,CAAC;UACzC;UACAiI,KAAK,GAAGtF,IAAI,CAACe,QAAQ,CAACf,IAAI,CAAC;QAC/B;QACA,OAAOsF,KAAK;MAChB,CAAC;MACDxH,aAAa,CAACzB,SAAS,CAACsG,OAAO,GAAG,UAAUsC,UAAU,EAAEM,OAAO,EAAE;QAC7D;QACA;QACA,IAAI;UACA,IAAI,CAACZ,UAAU,IACX,IAAI,CAACA,UAAU,CAACrC,SAAS,CAAC,IAAI,CAACsC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAEG,UAAU,EAAEM,OAAO,CAAC;QAChG,CAAC,CACD,OAAO7D,GAAG,EAAE;UACR,IAAI,CAAC5B,WAAW,CAACmF,UAAU,EAAEvD,GAAG,CAAC;QACrC;MACJ,CAAC;MACD;MACA5D,aAAa,CAACzB,SAAS,CAAC8E,gBAAgB,GAAG,UAAUf,IAAI,EAAEgC,KAAK,EAAE;QAC9D,IAAIoD,MAAM,GAAG,IAAI,CAACxC,WAAW;QAC7B,IAAIyC,IAAI,GAAGD,MAAM,CAACpF,IAAI,CAAC;QACvB,IAAIsF,IAAI,GAAGF,MAAM,CAACpF,IAAI,CAAC,GAAGqF,IAAI,GAAGrD,KAAK;QACtC,IAAIsD,IAAI,GAAG,CAAC,EAAE;UACV,MAAM,IAAIrI,KAAK,CAAC,0CAA0C,CAAC;QAC/D;QACA,IAAIoI,IAAI,IAAI,CAAC,IAAIC,IAAI,IAAI,CAAC,EAAE;UACxB,IAAIH,OAAO,GAAG;YACVzD,SAAS,EAAE0D,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;YAClClF,SAAS,EAAEkF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;YAClCnF,SAAS,EAAEmF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;YAClCG,MAAM,EAAEvF;UACZ,CAAC;UACD,IAAI,CAACuC,OAAO,CAAC,IAAI,CAACvE,IAAI,EAAEmH,OAAO,CAAC;QACpC;MACJ,CAAC;MACD,OAAOzH,aAAa;IACxB,CAAC,EAAG;IACJ,IAAI+D,QAAQ,GAAG,aAAe,YAAY;MACtC,SAASA,QAAQ,CAACzB,IAAI,EAAEf,MAAM,EAAED,QAAQ,EAAEwG,OAAO,EAAEP,UAAU,EAAEtE,QAAQ,EAAE;QACrE;QACA,IAAI,CAACU,KAAK,GAAG,IAAI;QACjB,IAAI,CAACd,QAAQ,GAAG,CAAC;QACjB;QACA,IAAI,CAACa,cAAc,GAAG,IAAI;QAC1B;QACA,IAAI,CAACqE,MAAM,GAAG,cAAc;QAC5B,IAAI,CAACzF,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACf,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACwB,IAAI,GAAG+E,OAAO;QACnB,IAAI,CAACP,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAACtE,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAAC3B,QAAQ,EAAE;UACX,MAAM,IAAI/B,KAAK,CAAC,yBAAyB,CAAC;QAC9C;QACA,IAAI,CAAC+B,QAAQ,GAAGA,QAAQ;QACxB,IAAI0G,IAAI,GAAG,IAAI;QACf;QACA,IAAI1F,IAAI,KAAKC,SAAS,IAAIuF,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;UAC/C,IAAI,CAACnG,MAAM,GAAGiC,QAAQ,CAACZ,UAAU;QACrC,CAAC,MACI;UACD,IAAI,CAACrB,MAAM,GAAG,YAAY;YACtB,OAAOiC,QAAQ,CAACZ,UAAU,CAAC1E,IAAI,CAACK,MAAM,EAAEkJ,IAAI,EAAE,IAAI,EAAE/J,SAAS,CAAC;UAClE,CAAC;QACL;MACJ;MACA8F,QAAQ,CAACZ,UAAU,GAAG,UAAUjB,IAAI,EAAEyC,MAAM,EAAEuD,IAAI,EAAE;QAChD,IAAI,CAAChG,IAAI,EAAE;UACPA,IAAI,GAAG,IAAI;QACf;QACAiG,yBAAyB,EAAE;QAC3B,IAAI;UACAjG,IAAI,CAACW,QAAQ,EAAE;UACf,OAAOX,IAAI,CAAC5B,IAAI,CAAC2B,OAAO,CAACC,IAAI,EAAEyC,MAAM,EAAEuD,IAAI,CAAC;QAChD,CAAC,SACO;UACJ,IAAIC,yBAAyB,IAAI,CAAC,EAAE;YAChCC,mBAAmB,EAAE;UACzB;UACAD,yBAAyB,EAAE;QAC/B;MACJ,CAAC;MACDhI,MAAM,CAACC,cAAc,CAAC2D,QAAQ,CAACxF,SAAS,EAAE,MAAM,EAAE;QAC9C8B,GAAG,EAAE,YAAY;UACb,OAAO,IAAI,CAACsD,KAAK;QACrB,CAAC;QACDnD,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFN,MAAM,CAACC,cAAc,CAAC2D,QAAQ,CAACxF,SAAS,EAAE,OAAO,EAAE;QAC/C8B,GAAG,EAAE,YAAY;UACb,OAAO,IAAI,CAAC0H,MAAM;QACtB,CAAC;QACDvH,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFsD,QAAQ,CAACxF,SAAS,CAAC8J,qBAAqB,GAAG,YAAY;QACnD,IAAI,CAAC1F,aAAa,CAACN,YAAY,EAAEmB,UAAU,CAAC;MAChD,CAAC;MACD;MACAO,QAAQ,CAACxF,SAAS,CAACoE,aAAa,GAAG,UAAU2F,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;QAC1E,IAAI,IAAI,CAACT,MAAM,KAAKQ,UAAU,IAAI,IAAI,CAACR,MAAM,KAAKS,UAAU,EAAE;UAC1D,IAAI,CAACT,MAAM,GAAGO,OAAO;UACrB,IAAIA,OAAO,IAAIjG,YAAY,EAAE;YACzB,IAAI,CAACqB,cAAc,GAAG,IAAI;UAC9B;QACJ,CAAC,MACI;UACD,MAAM,IAAInE,KAAK,CAAC,EAAE,CAACb,MAAM,CAAC,IAAI,CAAC4D,IAAI,EAAE,IAAI,CAAC,CAAC5D,MAAM,CAAC,IAAI,CAAC6C,MAAM,EAAE,4BAA4B,CAAC,CAAC7C,MAAM,CAAC4J,OAAO,EAAE,sBAAsB,CAAC,CAAC5J,MAAM,CAAC6J,UAAU,EAAE,GAAG,CAAC,CAAC7J,MAAM,CAAC8J,UAAU,GAAG,QAAQ,GAAGA,UAAU,GAAG,IAAI,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC9J,MAAM,CAAC,IAAI,CAACqJ,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7P;MACJ,CAAC;MACDhE,QAAQ,CAACxF,SAAS,CAACkK,QAAQ,GAAG,YAAY;QACtC,IAAI,IAAI,CAAC1F,IAAI,IAAI,OAAO,IAAI,CAACA,IAAI,CAAC2F,QAAQ,KAAK,WAAW,EAAE;UACxD,OAAO,IAAI,CAAC3F,IAAI,CAAC2F,QAAQ,CAACD,QAAQ,EAAE;QACxC,CAAC,MACI;UACD,OAAOtI,MAAM,CAAC5B,SAAS,CAACkK,QAAQ,CAAChK,IAAI,CAAC,IAAI,CAAC;QAC/C;MACJ,CAAC;MACD;MACA;MACAsF,QAAQ,CAACxF,SAAS,CAACoK,MAAM,GAAG,YAAY;QACpC,OAAO;UACHrG,IAAI,EAAE,IAAI,CAACA,IAAI;UACfF,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBb,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBjB,IAAI,EAAE,IAAI,CAACA,IAAI,CAACrB,IAAI;UACpB4D,QAAQ,EAAE,IAAI,CAACA;QACnB,CAAC;MACL,CAAC;MACD,OAAOkB,QAAQ;IACnB,CAAC,EAAG;IACJ;IACA;IACA;IACA;IACA;IACA,IAAI6E,gBAAgB,GAAGvJ,UAAU,CAAC,YAAY,CAAC;IAC/C,IAAIwJ,aAAa,GAAGxJ,UAAU,CAAC,SAAS,CAAC;IACzC,IAAIyJ,UAAU,GAAGzJ,UAAU,CAAC,MAAM,CAAC;IACnC,IAAI0J,eAAe,GAAG,EAAE;IACxB,IAAIC,yBAAyB,GAAG,KAAK;IACrC,IAAIC,2BAA2B;IAC/B,SAASC,uBAAuB,CAACC,IAAI,EAAE;MACnC,IAAI,CAACF,2BAA2B,EAAE;QAC9B,IAAInK,MAAM,CAAC+J,aAAa,CAAC,EAAE;UACvBI,2BAA2B,GAAGnK,MAAM,CAAC+J,aAAa,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;QAClE;MACJ;MACA,IAAIH,2BAA2B,EAAE;QAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAU,CAAC;QACxD,IAAI,CAACO,UAAU,EAAE;UACb;UACA;UACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAM,CAAC;QACpD;QACAI,UAAU,CAAC5K,IAAI,CAACwK,2BAA2B,EAAEE,IAAI,CAAC;MACtD,CAAC,MACI;QACDrK,MAAM,CAAC8J,gBAAgB,CAAC,CAACO,IAAI,EAAE,CAAC,CAAC;MACrC;IACJ;IACA,SAAStF,iBAAiB,CAAC3B,IAAI,EAAE;MAC7B;MACA;MACA,IAAIiG,yBAAyB,KAAK,CAAC,IAAIY,eAAe,CAAC7K,MAAM,KAAK,CAAC,EAAE;QACjE;QACAgL,uBAAuB,CAACd,mBAAmB,CAAC;MAChD;MACAlG,IAAI,IAAI6G,eAAe,CAACzB,IAAI,CAACpF,IAAI,CAAC;IACtC;IACA,SAASkG,mBAAmB,GAAG;MAC3B,IAAI,CAACY,yBAAyB,EAAE;QAC5BA,yBAAyB,GAAG,IAAI;QAChC,OAAOD,eAAe,CAAC7K,MAAM,EAAE;UAC3B,IAAIoL,KAAK,GAAGP,eAAe;UAC3BA,eAAe,GAAG,EAAE;UACpB,KAAK,IAAI5K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,KAAK,CAACpL,MAAM,EAAEC,CAAC,EAAE,EAAE;YACnC,IAAI+D,IAAI,GAAGoH,KAAK,CAACnL,CAAC,CAAC;YACnB,IAAI;cACA+D,IAAI,CAAC5B,IAAI,CAAC2B,OAAO,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACvC,CAAC,CACD,OAAOH,KAAK,EAAE;cACVd,IAAI,CAACsI,gBAAgB,CAACxH,KAAK,CAAC;YAChC;UACJ;QACJ;QACAd,IAAI,CAACuI,kBAAkB,EAAE;QACzBR,yBAAyB,GAAG,KAAK;MACrC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI7G,OAAO,GAAG;MAAElD,IAAI,EAAE;IAAU,CAAC;IACjC,IAAIoD,YAAY,GAAG,cAAc;MAAEmB,UAAU,GAAG,YAAY;MAAEZ,SAAS,GAAG,WAAW;MAAEF,OAAO,GAAG,SAAS;MAAE2B,SAAS,GAAG,WAAW;MAAEjB,OAAO,GAAG,SAAS;IACxJ,IAAIY,SAAS,GAAG,WAAW;MAAExB,SAAS,GAAG,WAAW;MAAED,SAAS,GAAG,WAAW;IAC7E,IAAIrC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIe,IAAI,GAAG;MACPwI,MAAM,EAAEpK,UAAU;MAClBqK,gBAAgB,EAAE,YAAY;QAAE,OAAOhJ,iBAAiB;MAAE,CAAC;MAC3D6I,gBAAgB,EAAEI,IAAI;MACtBH,kBAAkB,EAAEG,IAAI;MACxB9F,iBAAiB,EAAEA,iBAAiB;MACpC+F,iBAAiB,EAAE,YAAY;QAAE,OAAO,CAACpK,IAAI,CAACH,UAAU,CAAC,iCAAiC,CAAC,CAAC;MAAE,CAAC;MAC/FwK,gBAAgB,EAAE,YAAY;QAAE,OAAO,EAAE;MAAE,CAAC;MAC5CC,iBAAiB,EAAEH,IAAI;MACvBI,WAAW,EAAE,YAAY;QAAE,OAAOJ,IAAI;MAAE,CAAC;MACzCK,aAAa,EAAE,YAAY;QAAE,OAAO,EAAE;MAAE,CAAC;MACzCC,SAAS,EAAE,YAAY;QAAE,OAAON,IAAI;MAAE,CAAC;MACvCO,cAAc,EAAE,YAAY;QAAE,OAAOP,IAAI;MAAE,CAAC;MAC5CQ,mBAAmB,EAAE,YAAY;QAAE,OAAOR,IAAI;MAAE,CAAC;MACjDS,UAAU,EAAE,YAAY;QAAE,OAAO,KAAK;MAAE,CAAC;MACzCC,gBAAgB,EAAE,YAAY;QAAE,OAAOnH,SAAS;MAAE,CAAC;MACnDoH,oBAAoB,EAAE,YAAY;QAAE,OAAOX,IAAI;MAAE,CAAC;MAClDY,8BAA8B,EAAE,YAAY;QAAE,OAAOrH,SAAS;MAAE,CAAC;MACjEsH,YAAY,EAAE,YAAY;QAAE,OAAOtH,SAAS;MAAE,CAAC;MAC/CuH,UAAU,EAAE,YAAY;QAAE,OAAO,EAAE;MAAE,CAAC;MACtCC,UAAU,EAAE,YAAY;QAAE,OAAOf,IAAI;MAAE,CAAC;MACxCgB,mBAAmB,EAAE,YAAY;QAAE,OAAOhB,IAAI;MAAE,CAAC;MACjDiB,gBAAgB,EAAE,YAAY;QAAE,OAAO,EAAE;MAAE,CAAC;MAC5CC,qBAAqB,EAAE,YAAY;QAAE,OAAOlB,IAAI;MAAE,CAAC;MACnDmB,iBAAiB,EAAE,YAAY;QAAE,OAAOnB,IAAI;MAAE,CAAC;MAC/CoB,cAAc,EAAE,YAAY;QAAE,OAAOpB,IAAI;MAAE,CAAC;MAC5CT,uBAAuB,EAAEA;IAC7B,CAAC;IACD,IAAIxI,iBAAiB,GAAG;MAAEjB,MAAM,EAAE,IAAI;MAAEa,IAAI,EAAE,IAAId,IAAI,CAAC,IAAI,EAAE,IAAI;IAAE,CAAC;IACpE,IAAImB,YAAY,GAAG,IAAI;IACvB,IAAIwH,yBAAyB,GAAG,CAAC;IACjC,SAASwB,IAAI,GAAG,CAAE;IAClBzK,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;IAClC,OAAOJ,MAAM,CAAC,MAAM,CAAC,GAAGU,IAAI;EAChC,CAAC,EAAG,OAAOwL,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,OAAOhD,IAAI,KAAK,WAAW,IAAIA,IAAI,IAAIlJ,MAAM,CAAC;EAC7F;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA,IAAIyL,8BAA8B,GAAGpK,MAAM,CAAC8K,wBAAwB;EACpE;EACA,IAAIX,oBAAoB,GAAGnK,MAAM,CAACC,cAAc;EAChD;EACA,IAAI8K,oBAAoB,GAAG/K,MAAM,CAACgL,cAAc;EAChD;EACA,IAAIX,YAAY,GAAGrK,MAAM,CAACiL,MAAM;EAChC;EACA,IAAIX,UAAU,GAAGnM,KAAK,CAACC,SAAS,CAACC,KAAK;EACtC;EACA,IAAI6M,sBAAsB,GAAG,kBAAkB;EAC/C;EACA,IAAIC,yBAAyB,GAAG,qBAAqB;EACrD;EACA,IAAIC,8BAA8B,GAAG/L,IAAI,CAACH,UAAU,CAACgM,sBAAsB,CAAC;EAC5E;EACA,IAAIG,iCAAiC,GAAGhM,IAAI,CAACH,UAAU,CAACiM,yBAAyB,CAAC;EAClF;EACA,IAAIG,QAAQ,GAAG,MAAM;EACrB;EACA,IAAIC,SAAS,GAAG,OAAO;EACvB;EACA,IAAIC,kBAAkB,GAAGnM,IAAI,CAACH,UAAU,CAAC,EAAE,CAAC;EAC5C,SAASsL,mBAAmB,CAACrJ,QAAQ,EAAEC,MAAM,EAAE;IAC3C,OAAO/B,IAAI,CAACe,OAAO,CAACc,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;EAC9C;EACA,SAASqK,gCAAgC,CAACrK,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;IAC5F,OAAO1E,IAAI,CAACe,OAAO,CAAC0D,iBAAiB,CAAC1C,MAAM,EAAED,QAAQ,EAAEyB,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC;EAC/F;EACA,IAAI2H,YAAY,GAAGrM,IAAI,CAACH,UAAU;EAClC,IAAIyM,cAAc,GAAG,OAAOd,MAAM,KAAK,WAAW;EAClD,IAAIe,cAAc,GAAGD,cAAc,GAAGd,MAAM,GAAG9H,SAAS;EACxD,IAAI8I,OAAO,GAAGF,cAAc,IAAIC,cAAc,IAAI,OAAO/D,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAIlJ,MAAM;EAC5F,IAAImN,gBAAgB,GAAG,iBAAiB;EACxC,SAASjC,aAAa,CAAC9B,IAAI,EAAE3G,MAAM,EAAE;IACjC,KAAK,IAAIpD,CAAC,GAAG+J,IAAI,CAAChK,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvC,IAAI,OAAO+J,IAAI,CAAC/J,CAAC,CAAC,KAAK,UAAU,EAAE;QAC/B+J,IAAI,CAAC/J,CAAC,CAAC,GAAGwM,mBAAmB,CAACzC,IAAI,CAAC/J,CAAC,CAAC,EAAEoD,MAAM,GAAG,GAAG,GAAGpD,CAAC,CAAC;MAC5D;IACJ;IACA,OAAO+J,IAAI;EACf;EACA,SAASgE,cAAc,CAAC3N,SAAS,EAAE4N,OAAO,EAAE;IACxC,IAAI5K,MAAM,GAAGhD,SAAS,CAAC6N,WAAW,CAAC,MAAM,CAAC;IAC1C,IAAIC,OAAO,GAAG,UAAUlO,CAAC,EAAE;MACvB,IAAImO,MAAM,GAAGH,OAAO,CAAChO,CAAC,CAAC;MACvB,IAAIsG,QAAQ,GAAGlG,SAAS,CAAC+N,MAAM,CAAC;MAChC,IAAI7H,QAAQ,EAAE;QACV,IAAI8H,aAAa,GAAGhC,8BAA8B,CAAChM,SAAS,EAAE+N,MAAM,CAAC;QACrE,IAAI,CAACE,kBAAkB,CAACD,aAAa,CAAC,EAAE;UACpC,OAAO,UAAU;QACrB;QACAhO,SAAS,CAAC+N,MAAM,CAAC,GAAI,UAAU7H,QAAQ,EAAE;UACrC,IAAIgI,OAAO,GAAG,YAAY;YACtB,OAAOhI,QAAQ,CAAC2C,KAAK,CAAC,IAAI,EAAE4C,aAAa,CAAC/L,SAAS,EAAEsD,MAAM,GAAG,GAAG,GAAG+K,MAAM,CAAC,CAAC;UAChF,CAAC;UACDzB,qBAAqB,CAAC4B,OAAO,EAAEhI,QAAQ,CAAC;UACxC,OAAOgI,OAAO;QAClB,CAAC,CAAEhI,QAAQ,CAAC;MAChB;IACJ,CAAC;IACD,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgO,OAAO,CAACjO,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCkO,OAAO,CAAClO,CAAC,CAAC;IACd;EACJ;EACA,SAASqO,kBAAkB,CAACE,YAAY,EAAE;IACtC,IAAI,CAACA,YAAY,EAAE;MACf,OAAO,IAAI;IACf;IACA,IAAIA,YAAY,CAACC,QAAQ,KAAK,KAAK,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,OAAO,EAAE,OAAOD,YAAY,CAACrM,GAAG,KAAK,UAAU,IAAI,OAAOqM,YAAY,CAACE,GAAG,KAAK,WAAW,CAAC;EAC/F;EACA,IAAIC,WAAW,GAAI,OAAOC,iBAAiB,KAAK,WAAW,IAAI9E,IAAI,YAAY8E,iBAAkB;EACjG;EACA;EACA,IAAIC,MAAM,GAAI,EAAE,IAAI,IAAIf,OAAO,CAAC,IAAI,OAAOA,OAAO,CAACgB,OAAO,KAAK,WAAW,IACtE,CAAC,CAAC,CAACvE,QAAQ,CAAChK,IAAI,CAACuN,OAAO,CAACgB,OAAO,CAAC,KAAK,kBAAmB;EAC7D,IAAIC,SAAS,GAAG,CAACF,MAAM,IAAI,CAACF,WAAW,IAAI,CAAC,EAAEf,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;EAC9F;EACA;EACA;EACA,IAAImB,KAAK,GAAG,OAAOlB,OAAO,CAACgB,OAAO,KAAK,WAAW,IAC9C,CAAC,CAAC,CAACvE,QAAQ,CAAChK,IAAI,CAACuN,OAAO,CAACgB,OAAO,CAAC,KAAK,kBAAkB,IAAI,CAACH,WAAW,IACxE,CAAC,EAAEf,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;EACvD,IAAIoB,sBAAsB,GAAG,CAAC,CAAC;EAC/B,IAAIC,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC1B;IACA;IACAA,KAAK,GAAGA,KAAK,IAAIrB,OAAO,CAACqB,KAAK;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAIC,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAAC/K,IAAI,CAAC;IACxD,IAAI,CAACgL,eAAe,EAAE;MAClBA,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAAC/K,IAAI,CAAC,GAAGuJ,YAAY,CAAC,aAAa,GAAGwB,KAAK,CAAC/K,IAAI,CAAC;IACnG;IACA,IAAIqC,MAAM,GAAG,IAAI,IAAI0I,KAAK,CAAC1I,MAAM,IAAIqH,OAAO;IAC5C,IAAIuB,QAAQ,GAAG5I,MAAM,CAAC2I,eAAe,CAAC;IACtC,IAAIE,MAAM;IACV,IAAIP,SAAS,IAAItI,MAAM,KAAKoH,cAAc,IAAIsB,KAAK,CAAC/K,IAAI,KAAK,OAAO,EAAE;MAClE;MACA;MACA;MACA,IAAImL,UAAU,GAAGJ,KAAK;MACtBG,MAAM,GAAGD,QAAQ,IACbA,QAAQ,CAAC9O,IAAI,CAAC,IAAI,EAAEgP,UAAU,CAACC,OAAO,EAAED,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,EAAEH,UAAU,CAACI,KAAK,EAAEJ,UAAU,CAAC1L,KAAK,CAAC;MACvH,IAAIyL,MAAM,KAAK,IAAI,EAAE;QACjBH,KAAK,CAACS,cAAc,EAAE;MAC1B;IACJ,CAAC,MACI;MACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACnG,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;MACpD,IAAIuP,MAAM,IAAItK,SAAS,IAAI,CAACsK,MAAM,EAAE;QAChCH,KAAK,CAACS,cAAc,EAAE;MAC1B;IACJ;IACA,OAAON,MAAM;EACjB,CAAC;EACD,SAASO,aAAa,CAACC,GAAG,EAAEC,IAAI,EAAE1P,SAAS,EAAE;IACzC,IAAI2P,IAAI,GAAG3D,8BAA8B,CAACyD,GAAG,EAAEC,IAAI,CAAC;IACpD,IAAI,CAACC,IAAI,IAAI3P,SAAS,EAAE;MACpB;MACA,IAAIgO,aAAa,GAAGhC,8BAA8B,CAAChM,SAAS,EAAE0P,IAAI,CAAC;MACnE,IAAI1B,aAAa,EAAE;QACf2B,IAAI,GAAG;UAAE1N,UAAU,EAAE,IAAI;UAAEC,YAAY,EAAE;QAAK,CAAC;MACnD;IACJ;IACA;IACA;IACA,IAAI,CAACyN,IAAI,IAAI,CAACA,IAAI,CAACzN,YAAY,EAAE;MAC7B;IACJ;IACA,IAAI0N,mBAAmB,GAAGtC,YAAY,CAAC,IAAI,GAAGoC,IAAI,GAAG,SAAS,CAAC;IAC/D,IAAID,GAAG,CAACjN,cAAc,CAACoN,mBAAmB,CAAC,IAAIH,GAAG,CAACG,mBAAmB,CAAC,EAAE;MACrE;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,OAAOD,IAAI,CAACvB,QAAQ;IACpB,OAAOuB,IAAI,CAAC1G,KAAK;IACjB,IAAI4G,eAAe,GAAGF,IAAI,CAAC7N,GAAG;IAC9B,IAAIgO,eAAe,GAAGH,IAAI,CAACtB,GAAG;IAC9B;IACA,IAAI0B,SAAS,GAAGL,IAAI,CAACzP,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI8O,eAAe,GAAGH,sBAAsB,CAACmB,SAAS,CAAC;IACvD,IAAI,CAAChB,eAAe,EAAE;MAClBA,eAAe,GAAGH,sBAAsB,CAACmB,SAAS,CAAC,GAAGzC,YAAY,CAAC,aAAa,GAAGyC,SAAS,CAAC;IACjG;IACAJ,IAAI,CAACtB,GAAG,GAAG,UAAU2B,QAAQ,EAAE;MAC3B;MACA;MACA,IAAI5J,MAAM,GAAG,IAAI;MACjB,IAAI,CAACA,MAAM,IAAIqJ,GAAG,KAAKhC,OAAO,EAAE;QAC5BrH,MAAM,GAAGqH,OAAO;MACpB;MACA,IAAI,CAACrH,MAAM,EAAE;QACT;MACJ;MACA,IAAI6J,aAAa,GAAG7J,MAAM,CAAC2I,eAAe,CAAC;MAC3C,IAAI,OAAOkB,aAAa,KAAK,UAAU,EAAE;QACrC7J,MAAM,CAAC8J,mBAAmB,CAACH,SAAS,EAAElB,MAAM,CAAC;MACjD;MACA;MACA;MACAiB,eAAe,IAAIA,eAAe,CAAC5P,IAAI,CAACkG,MAAM,EAAE,IAAI,CAAC;MACrDA,MAAM,CAAC2I,eAAe,CAAC,GAAGiB,QAAQ;MAClC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;QAChC5J,MAAM,CAAC+J,gBAAgB,CAACJ,SAAS,EAAElB,MAAM,EAAE,KAAK,CAAC;MACrD;IACJ,CAAC;IACD;IACA;IACAc,IAAI,CAAC7N,GAAG,GAAG,YAAY;MACnB;MACA;MACA,IAAIsE,MAAM,GAAG,IAAI;MACjB,IAAI,CAACA,MAAM,IAAIqJ,GAAG,KAAKhC,OAAO,EAAE;QAC5BrH,MAAM,GAAGqH,OAAO;MACpB;MACA,IAAI,CAACrH,MAAM,EAAE;QACT,OAAO,IAAI;MACf;MACA,IAAI4I,QAAQ,GAAG5I,MAAM,CAAC2I,eAAe,CAAC;MACtC,IAAIC,QAAQ,EAAE;QACV,OAAOA,QAAQ;MACnB,CAAC,MACI,IAAIa,eAAe,EAAE;QACtB;QACA;QACA;QACA;QACA;QACA;QACA,IAAI5G,KAAK,GAAG4G,eAAe,CAAC3P,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI+I,KAAK,EAAE;UACP0G,IAAI,CAACtB,GAAG,CAACnO,IAAI,CAAC,IAAI,EAAE+I,KAAK,CAAC;UAC1B,IAAI,OAAO7C,MAAM,CAACsH,gBAAgB,CAAC,KAAK,UAAU,EAAE;YAChDtH,MAAM,CAACgK,eAAe,CAACV,IAAI,CAAC;UAChC;UACA,OAAOzG,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf,CAAC;IACD8C,oBAAoB,CAAC0D,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;IACrCF,GAAG,CAACG,mBAAmB,CAAC,GAAG,IAAI;EACnC;EACA,SAASrE,iBAAiB,CAACkE,GAAG,EAAElO,UAAU,EAAEvB,SAAS,EAAE;IACnD,IAAIuB,UAAU,EAAE;MACZ,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,UAAU,CAAC5B,MAAM,EAAEC,CAAC,EAAE,EAAE;QACxC4P,aAAa,CAACC,GAAG,EAAE,IAAI,GAAGlO,UAAU,CAAC3B,CAAC,CAAC,EAAEI,SAAS,CAAC;MACvD;IACJ,CAAC,MACI;MACD,IAAIqQ,YAAY,GAAG,EAAE;MACrB,KAAK,IAAIX,IAAI,IAAID,GAAG,EAAE;QAClB,IAAIC,IAAI,CAACzP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;UAC1BoQ,YAAY,CAACtH,IAAI,CAAC2G,IAAI,CAAC;QAC3B;MACJ;MACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAAC1Q,MAAM,EAAE2Q,CAAC,EAAE,EAAE;QAC1Cd,aAAa,CAACC,GAAG,EAAEY,YAAY,CAACC,CAAC,CAAC,EAAEtQ,SAAS,CAAC;MAClD;IACJ;EACJ;EACA,IAAIuQ,mBAAmB,GAAGjD,YAAY,CAAC,kBAAkB,CAAC;EAC1D;EACA,SAASnB,UAAU,CAACqE,SAAS,EAAE;IAC3B,IAAIC,aAAa,GAAGhD,OAAO,CAAC+C,SAAS,CAAC;IACtC,IAAI,CAACC,aAAa,EACd;IACJ;IACAhD,OAAO,CAACH,YAAY,CAACkD,SAAS,CAAC,CAAC,GAAGC,aAAa;IAChDhD,OAAO,CAAC+C,SAAS,CAAC,GAAG,YAAY;MAC7B,IAAIE,CAAC,GAAGjF,aAAa,CAAC/L,SAAS,EAAE8Q,SAAS,CAAC;MAC3C,QAAQE,CAAC,CAAC/Q,MAAM;QACZ,KAAK,CAAC;UACF,IAAI,CAAC4Q,mBAAmB,CAAC,GAAG,IAAIE,aAAa,EAAE;UAC/C;QACJ,KAAK,CAAC;UACF,IAAI,CAACF,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD;QACJ,KAAK,CAAC;UACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD;QACJ,KAAK,CAAC;UACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D;QACJ,KAAK,CAAC;UACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE;QACJ;UACI,MAAM,IAAI1P,KAAK,CAAC,oBAAoB,CAAC;MAAC;IAElD,CAAC;IACD;IACAsL,qBAAqB,CAACmB,OAAO,CAAC+C,SAAS,CAAC,EAAEC,aAAa,CAAC;IACxD,IAAIE,QAAQ,GAAG,IAAIF,aAAa,CAAC,YAAY,CAAE,CAAC,CAAC;IACjD,IAAIf,IAAI;IACR,KAAKA,IAAI,IAAIiB,QAAQ,EAAE;MACnB;MACA,IAAIH,SAAS,KAAK,gBAAgB,IAAId,IAAI,KAAK,cAAc,EACzD;MACH,WAAUA,IAAI,EAAE;QACb,IAAI,OAAOiB,QAAQ,CAACjB,IAAI,CAAC,KAAK,UAAU,EAAE;UACtCjC,OAAO,CAAC+C,SAAS,CAAC,CAACxQ,SAAS,CAAC0P,IAAI,CAAC,GAAG,YAAY;YAC7C,OAAO,IAAI,CAACa,mBAAmB,CAAC,CAACb,IAAI,CAAC,CAAC7G,KAAK,CAAC,IAAI,CAAC0H,mBAAmB,CAAC,EAAE7Q,SAAS,CAAC;UACtF,CAAC;QACL,CAAC,MACI;UACDqM,oBAAoB,CAAC0B,OAAO,CAAC+C,SAAS,CAAC,CAACxQ,SAAS,EAAE0P,IAAI,EAAE;YACrDrB,GAAG,EAAE,UAAU/L,EAAE,EAAE;cACf,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;gBAC1B,IAAI,CAACiO,mBAAmB,CAAC,CAACb,IAAI,CAAC,GAAGtD,mBAAmB,CAAC9J,EAAE,EAAEkO,SAAS,GAAG,GAAG,GAAGd,IAAI,CAAC;gBACjF;gBACA;gBACA;gBACApD,qBAAqB,CAAC,IAAI,CAACiE,mBAAmB,CAAC,CAACb,IAAI,CAAC,EAAEpN,EAAE,CAAC;cAC9D,CAAC,MACI;gBACD,IAAI,CAACiO,mBAAmB,CAAC,CAACb,IAAI,CAAC,GAAGpN,EAAE;cACxC;YACJ,CAAC;YACDR,GAAG,EAAE,YAAY;cACb,OAAO,IAAI,CAACyO,mBAAmB,CAAC,CAACb,IAAI,CAAC;YAC1C;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,EAACA,IAAI,CAAC;IACX;IACA,KAAKA,IAAI,IAAIe,aAAa,EAAE;MACxB,IAAIf,IAAI,KAAK,WAAW,IAAIe,aAAa,CAACjO,cAAc,CAACkN,IAAI,CAAC,EAAE;QAC5DjC,OAAO,CAAC+C,SAAS,CAAC,CAACd,IAAI,CAAC,GAAGe,aAAa,CAACf,IAAI,CAAC;MAClD;IACJ;EACJ;EACA,SAASlE,WAAW,CAACpF,MAAM,EAAE1F,IAAI,EAAEkQ,OAAO,EAAE;IACxC,IAAIC,KAAK,GAAGzK,MAAM;IAClB,OAAOyK,KAAK,IAAI,CAACA,KAAK,CAACrO,cAAc,CAAC9B,IAAI,CAAC,EAAE;MACzCmQ,KAAK,GAAGlE,oBAAoB,CAACkE,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,IAAIzK,MAAM,CAAC1F,IAAI,CAAC,EAAE;MACxB;MACAmQ,KAAK,GAAGzK,MAAM;IAClB;IACA,IAAI0K,YAAY,GAAGxD,YAAY,CAAC5M,IAAI,CAAC;IACrC,IAAIwF,QAAQ,GAAG,IAAI;IACnB,IAAI2K,KAAK,KAAK,EAAE3K,QAAQ,GAAG2K,KAAK,CAACC,YAAY,CAAC,CAAC,IAAI,CAACD,KAAK,CAACrO,cAAc,CAACsO,YAAY,CAAC,CAAC,EAAE;MACrF5K,QAAQ,GAAG2K,KAAK,CAACC,YAAY,CAAC,GAAGD,KAAK,CAACnQ,IAAI,CAAC;MAC5C;MACA;MACA,IAAIiP,IAAI,GAAGkB,KAAK,IAAI7E,8BAA8B,CAAC6E,KAAK,EAAEnQ,IAAI,CAAC;MAC/D,IAAIuN,kBAAkB,CAAC0B,IAAI,CAAC,EAAE;QAC1B,IAAIoB,eAAe,GAAGH,OAAO,CAAC1K,QAAQ,EAAE4K,YAAY,EAAEpQ,IAAI,CAAC;QAC3DmQ,KAAK,CAACnQ,IAAI,CAAC,GAAG,YAAY;UACtB,OAAOqQ,eAAe,CAAC,IAAI,EAAErR,SAAS,CAAC;QAC3C,CAAC;QACD4M,qBAAqB,CAACuE,KAAK,CAACnQ,IAAI,CAAC,EAAEwF,QAAQ,CAAC;MAChD;IACJ;IACA,OAAOA,QAAQ;EACnB;EACA;EACA,SAASyF,cAAc,CAAC8D,GAAG,EAAEuB,QAAQ,EAAEC,WAAW,EAAE;IAChD,IAAIC,SAAS,GAAG,IAAI;IACpB,SAASnM,YAAY,CAACpB,IAAI,EAAE;MACxB,IAAIa,IAAI,GAAGb,IAAI,CAACa,IAAI;MACpBA,IAAI,CAACmF,IAAI,CAACnF,IAAI,CAAC2M,KAAK,CAAC,GAAG,YAAY;QAChCxN,IAAI,CAACJ,MAAM,CAACsF,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;MACtC,CAAC;MACDwR,SAAS,CAACrI,KAAK,CAACrE,IAAI,CAAC4B,MAAM,EAAE5B,IAAI,CAACmF,IAAI,CAAC;MACvC,OAAOhG,IAAI;IACf;IACAuN,SAAS,GAAG1F,WAAW,CAACiE,GAAG,EAAEuB,QAAQ,EAAE,UAAU9K,QAAQ,EAAE;MAAE,OAAO,UAAUuD,IAAI,EAAEE,IAAI,EAAE;QACtF,IAAIyH,IAAI,GAAGH,WAAW,CAACxH,IAAI,EAAEE,IAAI,CAAC;QAClC,IAAIyH,IAAI,CAACD,KAAK,IAAI,CAAC,IAAI,OAAOxH,IAAI,CAACyH,IAAI,CAACD,KAAK,CAAC,KAAK,UAAU,EAAE;UAC3D,OAAO9D,gCAAgC,CAAC+D,IAAI,CAAC1Q,IAAI,EAAEiJ,IAAI,CAACyH,IAAI,CAACD,KAAK,CAAC,EAAEC,IAAI,EAAErM,YAAY,CAAC;QAC5F,CAAC,MACI;UACD;UACA,OAAOmB,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;QACrC;MACJ,CAAC;IAAE,CAAC,CAAC;EACT;EACA,SAAS2C,qBAAqB,CAAC4B,OAAO,EAAEmD,QAAQ,EAAE;IAC9CnD,OAAO,CAACZ,YAAY,CAAC,kBAAkB,CAAC,CAAC,GAAG+D,QAAQ;EACxD;EACA,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,QAAQ,GAAG,KAAK;EACpB,SAASC,IAAI,GAAG;IACZ,IAAI;MACA,IAAIC,EAAE,GAAGjE,cAAc,CAACkE,SAAS,CAACC,SAAS;MAC3C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7D,OAAO,IAAI;MACf;IACJ,CAAC,CACD,OAAOpO,KAAK,EAAE,CACd;IACA,OAAO,KAAK;EAChB;EACA,SAASqI,UAAU,GAAG;IAClB,IAAIyF,kBAAkB,EAAE;MACpB,OAAOC,QAAQ;IACnB;IACAD,kBAAkB,GAAG,IAAI;IACzB,IAAI;MACA,IAAIG,EAAE,GAAGjE,cAAc,CAACkE,SAAS,CAACC,SAAS;MAC3C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3FL,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CACD,OAAO/N,KAAK,EAAE,CACd;IACA,OAAO+N,QAAQ;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItQ,IAAI,CAACoB,YAAY,CAAC,kBAAkB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC/D,IAAI7F,8BAA8B,GAAGpK,MAAM,CAAC8K,wBAAwB;IACpE,IAAIX,oBAAoB,GAAGnK,MAAM,CAACC,cAAc;IAChD,SAASiQ,sBAAsB,CAACrC,GAAG,EAAE;MACjC,IAAIA,GAAG,IAAIA,GAAG,CAACvF,QAAQ,KAAKtI,MAAM,CAAC5B,SAAS,CAACkK,QAAQ,EAAE;QACnD,IAAIsG,SAAS,GAAGf,GAAG,CAAC5B,WAAW,IAAI4B,GAAG,CAAC5B,WAAW,CAACnN,IAAI;QACvD,OAAO,CAAC8P,SAAS,GAAGA,SAAS,GAAG,EAAE,IAAI,IAAI,GAAGuB,IAAI,CAACC,SAAS,CAACvC,GAAG,CAAC;MACpE;MACA,OAAOA,GAAG,GAAGA,GAAG,CAACvF,QAAQ,EAAE,GAAGtI,MAAM,CAAC5B,SAAS,CAACkK,QAAQ,CAAChK,IAAI,CAACuP,GAAG,CAAC;IACrE;IACA,IAAI3O,UAAU,GAAG+Q,GAAG,CAAC3G,MAAM;IAC3B,IAAI+G,sBAAsB,GAAG,EAAE;IAC/B,IAAIC,yCAAyC,GAAG3R,MAAM,CAACO,UAAU,CAAC,6CAA6C,CAAC,CAAC,KAAK,IAAI;IAC1H,IAAIwJ,aAAa,GAAGxJ,UAAU,CAAC,SAAS,CAAC;IACzC,IAAIyJ,UAAU,GAAGzJ,UAAU,CAAC,MAAM,CAAC;IACnC,IAAIqR,aAAa,GAAG,mBAAmB;IACvCN,GAAG,CAAC7G,gBAAgB,GAAG,UAAUoH,CAAC,EAAE;MAChC,IAAIP,GAAG,CAACxG,iBAAiB,EAAE,EAAE;QACzB,IAAIgH,SAAS,GAAGD,CAAC,IAAIA,CAAC,CAACC,SAAS;QAChC,IAAIA,SAAS,EAAE;UACXC,OAAO,CAAC9O,KAAK,CAAC,8BAA8B,EAAE6O,SAAS,YAAYrR,KAAK,GAAGqR,SAAS,CAAClD,OAAO,GAAGkD,SAAS,EAAE,SAAS,EAAED,CAAC,CAACrQ,IAAI,CAACrB,IAAI,EAAE,SAAS,EAAE0R,CAAC,CAACzO,IAAI,IAAIyO,CAAC,CAACzO,IAAI,CAACX,MAAM,EAAE,UAAU,EAAEqP,SAAS,EAAEA,SAAS,YAAYrR,KAAK,GAAGqR,SAAS,CAACE,KAAK,GAAG5N,SAAS,CAAC;QAC1P,CAAC,MACI;UACD2N,OAAO,CAAC9O,KAAK,CAAC4O,CAAC,CAAC;QACpB;MACJ;IACJ,CAAC;IACDP,GAAG,CAAC5G,kBAAkB,GAAG,YAAY;MACjC,IAAIuH,OAAO,GAAG,YAAY;QACtB,IAAIC,oBAAoB,GAAGR,sBAAsB,CAACS,KAAK,EAAE;QACzD,IAAI;UACAD,oBAAoB,CAAC1Q,IAAI,CAACoB,UAAU,CAAC,YAAY;YAC7C,IAAIsP,oBAAoB,CAACE,aAAa,EAAE;cACpC,MAAMF,oBAAoB,CAACJ,SAAS;YACxC;YACA,MAAMI,oBAAoB;UAC9B,CAAC,CAAC;QACN,CAAC,CACD,OAAOjP,KAAK,EAAE;UACVoP,wBAAwB,CAACpP,KAAK,CAAC;QACnC;MACJ,CAAC;MACD,OAAOyO,sBAAsB,CAACtS,MAAM,EAAE;QAClC6S,OAAO,EAAE;MACb;IACJ,CAAC;IACD,IAAIK,0CAA0C,GAAG/R,UAAU,CAAC,kCAAkC,CAAC;IAC/F,SAAS8R,wBAAwB,CAACR,CAAC,EAAE;MACjCP,GAAG,CAAC7G,gBAAgB,CAACoH,CAAC,CAAC;MACvB,IAAI;QACA,IAAIU,OAAO,GAAG7R,IAAI,CAAC4R,0CAA0C,CAAC;QAC9D,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;UAC/BA,OAAO,CAAC5S,IAAI,CAAC,IAAI,EAAEkS,CAAC,CAAC;QACzB;MACJ,CAAC,CACD,OAAO/M,GAAG,EAAE,CACZ;IACJ;IACA,SAAS0N,UAAU,CAAC9J,KAAK,EAAE;MACvB,OAAOA,KAAK,IAAIA,KAAK,CAAC+J,IAAI;IAC9B;IACA,SAASC,iBAAiB,CAAChK,KAAK,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,SAASiK,gBAAgB,CAACb,SAAS,EAAE;MACjC,OAAOc,gBAAgB,CAACC,MAAM,CAACf,SAAS,CAAC;IAC7C;IACA,IAAIgB,WAAW,GAAGvS,UAAU,CAAC,OAAO,CAAC;IACrC,IAAIwS,WAAW,GAAGxS,UAAU,CAAC,OAAO,CAAC;IACrC,IAAIyS,aAAa,GAAGzS,UAAU,CAAC,SAAS,CAAC;IACzC,IAAI0S,wBAAwB,GAAG1S,UAAU,CAAC,oBAAoB,CAAC;IAC/D,IAAI2S,wBAAwB,GAAG3S,UAAU,CAAC,oBAAoB,CAAC;IAC/D,IAAIkC,MAAM,GAAG,cAAc;IAC3B,IAAI0Q,UAAU,GAAG,IAAI;IACrB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,SAASC,YAAY,CAACC,OAAO,EAAElQ,KAAK,EAAE;MAClC,OAAO,UAAUmQ,CAAC,EAAE;QAChB,IAAI;UACAC,cAAc,CAACF,OAAO,EAAElQ,KAAK,EAAEmQ,CAAC,CAAC;QACrC,CAAC,CACD,OAAO3O,GAAG,EAAE;UACR4O,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE1O,GAAG,CAAC;QACvC;QACA;MACJ,CAAC;IACL;;IACA,IAAI6O,IAAI,GAAG,YAAY;MACnB,IAAIC,SAAS,GAAG,KAAK;MACrB,OAAO,SAASC,OAAO,CAACC,eAAe,EAAE;QACrC,OAAO,YAAY;UACf,IAAIF,SAAS,EAAE;YACX;UACJ;UACAA,SAAS,GAAG,IAAI;UAChBE,eAAe,CAACxL,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;QAC1C,CAAC;MACL,CAAC;IACL,CAAC;IACD,IAAI4U,UAAU,GAAG,8BAA8B;IAC/C,IAAIC,yBAAyB,GAAGzT,UAAU,CAAC,kBAAkB,CAAC;IAC9D;IACA,SAASmT,cAAc,CAACF,OAAO,EAAElQ,KAAK,EAAEoF,KAAK,EAAE;MAC3C,IAAIuL,WAAW,GAAGN,IAAI,EAAE;MACxB,IAAIH,OAAO,KAAK9K,KAAK,EAAE;QACnB,MAAM,IAAIwL,SAAS,CAACH,UAAU,CAAC;MACnC;MACA,IAAIP,OAAO,CAACV,WAAW,CAAC,KAAKK,UAAU,EAAE;QACrC;QACA,IAAIV,IAAI,GAAG,IAAI;QACf,IAAI;UACA,IAAI,OAAO/J,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;YAC1D+J,IAAI,GAAG/J,KAAK,IAAIA,KAAK,CAAC+J,IAAI;UAC9B;QACJ,CAAC,CACD,OAAO3N,GAAG,EAAE;UACRmP,WAAW,CAAC,YAAY;YACpBP,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE1O,GAAG,CAAC;UACvC,CAAC,CAAC,EAAE;UACJ,OAAO0O,OAAO;QAClB;QACA;QACA,IAAIlQ,KAAK,KAAK+P,QAAQ,IAAI3K,KAAK,YAAYkK,gBAAgB,IACvDlK,KAAK,CAACzG,cAAc,CAAC6Q,WAAW,CAAC,IAAIpK,KAAK,CAACzG,cAAc,CAAC8Q,WAAW,CAAC,IACtErK,KAAK,CAACoK,WAAW,CAAC,KAAKK,UAAU,EAAE;UACnCgB,oBAAoB,CAACzL,KAAK,CAAC;UAC3BgL,cAAc,CAACF,OAAO,EAAE9K,KAAK,CAACoK,WAAW,CAAC,EAAEpK,KAAK,CAACqK,WAAW,CAAC,CAAC;QACnE,CAAC,MACI,IAAIzP,KAAK,KAAK+P,QAAQ,IAAI,OAAOZ,IAAI,KAAK,UAAU,EAAE;UACvD,IAAI;YACAA,IAAI,CAAC9S,IAAI,CAAC+I,KAAK,EAAEuL,WAAW,CAACV,YAAY,CAACC,OAAO,EAAElQ,KAAK,CAAC,CAAC,EAAE2Q,WAAW,CAACV,YAAY,CAACC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;UAC1G,CAAC,CACD,OAAO1O,GAAG,EAAE;YACRmP,WAAW,CAAC,YAAY;cACpBP,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE1O,GAAG,CAAC;YACvC,CAAC,CAAC,EAAE;UACR;QACJ,CAAC,MACI;UACD0O,OAAO,CAACV,WAAW,CAAC,GAAGxP,KAAK;UAC5B,IAAIkH,KAAK,GAAGgJ,OAAO,CAACT,WAAW,CAAC;UAChCS,OAAO,CAACT,WAAW,CAAC,GAAGrK,KAAK;UAC5B,IAAI8K,OAAO,CAACR,aAAa,CAAC,KAAKA,aAAa,EAAE;YAC1C;YACA,IAAI1P,KAAK,KAAK8P,QAAQ,EAAE;cACpB;cACA;cACAI,OAAO,CAACV,WAAW,CAAC,GAAGU,OAAO,CAACN,wBAAwB,CAAC;cACxDM,OAAO,CAACT,WAAW,CAAC,GAAGS,OAAO,CAACP,wBAAwB,CAAC;YAC5D;UACJ;UACA;UACA;UACA,IAAI3P,KAAK,KAAK+P,QAAQ,IAAI3K,KAAK,YAAYjI,KAAK,EAAE;YAC9C;YACA,IAAI2T,KAAK,GAAG1T,IAAI,CAAC2T,WAAW,IAAI3T,IAAI,CAAC2T,WAAW,CAACpQ,IAAI,IACjDvD,IAAI,CAAC2T,WAAW,CAACpQ,IAAI,CAAC2N,aAAa,CAAC;YACxC,IAAIwC,KAAK,EAAE;cACP;cACA5I,oBAAoB,CAAC9C,KAAK,EAAEsL,yBAAyB,EAAE;gBAAErS,YAAY,EAAE,IAAI;gBAAED,UAAU,EAAE,KAAK;gBAAEmM,QAAQ,EAAE,IAAI;gBAAEnF,KAAK,EAAE0L;cAAM,CAAC,CAAC;YACnI;UACJ;UACA,KAAK,IAAI/U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,KAAK,CAACpL,MAAM,GAAG;YAC/BkV,uBAAuB,CAACd,OAAO,EAAEhJ,KAAK,CAACnL,CAAC,EAAE,CAAC,EAAEmL,KAAK,CAACnL,CAAC,EAAE,CAAC,EAAEmL,KAAK,CAACnL,CAAC,EAAE,CAAC,EAAEmL,KAAK,CAACnL,CAAC,EAAE,CAAC,CAAC;UACpF;UACA,IAAImL,KAAK,CAACpL,MAAM,IAAI,CAAC,IAAIkE,KAAK,IAAI+P,QAAQ,EAAE;YACxCG,OAAO,CAACV,WAAW,CAAC,GAAGQ,iBAAiB;YACxC,IAAIpB,oBAAoB,GAAGxJ,KAAK;YAChC,IAAI;cACA;cACA;cACA;cACA,MAAM,IAAIjI,KAAK,CAAC,yBAAyB,GAAG8Q,sBAAsB,CAAC7I,KAAK,CAAC,IACpEA,KAAK,IAAIA,KAAK,CAACsJ,KAAK,GAAG,IAAI,GAAGtJ,KAAK,CAACsJ,KAAK,GAAG,EAAE,CAAC,CAAC;YACzD,CAAC,CACD,OAAOlN,GAAG,EAAE;cACRoN,oBAAoB,GAAGpN,GAAG;YAC9B;YACA,IAAI6M,yCAAyC,EAAE;cAC3C;cACA;cACAO,oBAAoB,CAACE,aAAa,GAAG,IAAI;YAC7C;YACAF,oBAAoB,CAACJ,SAAS,GAAGpJ,KAAK;YACtCwJ,oBAAoB,CAACsB,OAAO,GAAGA,OAAO;YACtCtB,oBAAoB,CAAC1Q,IAAI,GAAGd,IAAI,CAACe,OAAO;YACxCyQ,oBAAoB,CAAC9O,IAAI,GAAG1C,IAAI,CAAC2T,WAAW;YAC5C3C,sBAAsB,CAAClJ,IAAI,CAAC0J,oBAAoB,CAAC;YACjDZ,GAAG,CAACvM,iBAAiB,EAAE,CAAC,CAAC;UAC7B;QACJ;MACJ;MACA;MACA,OAAOyO,OAAO;IAClB;IACA,IAAIe,yBAAyB,GAAGhU,UAAU,CAAC,yBAAyB,CAAC;IACrE,SAAS4T,oBAAoB,CAACX,OAAO,EAAE;MACnC,IAAIA,OAAO,CAACV,WAAW,CAAC,KAAKQ,iBAAiB,EAAE;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIf,OAAO,GAAG7R,IAAI,CAAC6T,yBAAyB,CAAC;UAC7C,IAAIhC,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;YAC1CA,OAAO,CAAC5S,IAAI,CAAC,IAAI,EAAE;cAAEmS,SAAS,EAAE0B,OAAO,CAACT,WAAW,CAAC;cAAES,OAAO,EAAEA;YAAQ,CAAC,CAAC;UAC7E;QACJ,CAAC,CACD,OAAO1O,GAAG,EAAE,CACZ;QACA0O,OAAO,CAACV,WAAW,CAAC,GAAGO,QAAQ;QAC/B,KAAK,IAAIhU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqS,sBAAsB,CAACtS,MAAM,EAAEC,CAAC,EAAE,EAAE;UACpD,IAAImU,OAAO,KAAK9B,sBAAsB,CAACrS,CAAC,CAAC,CAACmU,OAAO,EAAE;YAC/C9B,sBAAsB,CAAC8C,MAAM,CAACnV,CAAC,EAAE,CAAC,CAAC;UACvC;QACJ;MACJ;IACJ;IACA,SAASiV,uBAAuB,CAACd,OAAO,EAAEhS,IAAI,EAAEiT,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACnFR,oBAAoB,CAACX,OAAO,CAAC;MAC7B,IAAIoB,YAAY,GAAGpB,OAAO,CAACV,WAAW,CAAC;MACvC,IAAInN,QAAQ,GAAGiP,YAAY,GACtB,OAAOF,WAAW,KAAK,UAAU,GAAIA,WAAW,GAAGhC,iBAAiB,GACpE,OAAOiC,UAAU,KAAK,UAAU,GAAIA,UAAU,GAC3ChC,gBAAgB;MACxBnR,IAAI,CAACuD,iBAAiB,CAACtC,MAAM,EAAE,YAAY;QACvC,IAAI;UACA,IAAIoS,kBAAkB,GAAGrB,OAAO,CAACT,WAAW,CAAC;UAC7C,IAAI+B,gBAAgB,GAAG,CAAC,CAACL,YAAY,IAAIzB,aAAa,KAAKyB,YAAY,CAACzB,aAAa,CAAC;UACtF,IAAI8B,gBAAgB,EAAE;YAClB;YACAL,YAAY,CAACxB,wBAAwB,CAAC,GAAG4B,kBAAkB;YAC3DJ,YAAY,CAACvB,wBAAwB,CAAC,GAAG0B,YAAY;UACzD;UACA;UACA,IAAIlM,KAAK,GAAGlH,IAAI,CAACqB,GAAG,CAAC8C,QAAQ,EAAEvB,SAAS,EAAE0Q,gBAAgB,IAAInP,QAAQ,KAAKgN,gBAAgB,IAAIhN,QAAQ,KAAK+M,iBAAiB,GACzH,EAAE,GACF,CAACmC,kBAAkB,CAAC,CAAC;UACzBnB,cAAc,CAACe,YAAY,EAAE,IAAI,EAAE/L,KAAK,CAAC;QAC7C,CAAC,CACD,OAAOzF,KAAK,EAAE;UACV;UACAyQ,cAAc,CAACe,YAAY,EAAE,KAAK,EAAExR,KAAK,CAAC;QAC9C;MACJ,CAAC,EAAEwR,YAAY,CAAC;IACpB;IACA,IAAIM,4BAA4B,GAAG,+CAA+C;IAClF,IAAIlK,IAAI,GAAG,YAAY,CAAE,CAAC;IAC1B,IAAImK,cAAc,GAAGhV,MAAM,CAACgV,cAAc;IAC1C,IAAIpC,gBAAgB,GAAG,aAAe,YAAY;MAC9C,SAASA,gBAAgB,CAACqC,QAAQ,EAAE;QAChC,IAAIzB,OAAO,GAAG,IAAI;QAClB,IAAI,EAAEA,OAAO,YAAYZ,gBAAgB,CAAC,EAAE;UACxC,MAAM,IAAInS,KAAK,CAAC,gCAAgC,CAAC;QACrD;QACA+S,OAAO,CAACV,WAAW,CAAC,GAAGK,UAAU;QACjCK,OAAO,CAACT,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAI;UACA,IAAIkB,WAAW,GAAGN,IAAI,EAAE;UACxBsB,QAAQ,IACJA,QAAQ,CAAChB,WAAW,CAACV,YAAY,CAACC,OAAO,EAAEJ,QAAQ,CAAC,CAAC,EAAEa,WAAW,CAACV,YAAY,CAACC,OAAO,EAAEH,QAAQ,CAAC,CAAC,CAAC;QAC5G,CAAC,CACD,OAAOpQ,KAAK,EAAE;UACVyQ,cAAc,CAACF,OAAO,EAAE,KAAK,EAAEvQ,KAAK,CAAC;QACzC;MACJ;MACA2P,gBAAgB,CAACjJ,QAAQ,GAAG,YAAY;QACpC,OAAOoL,4BAA4B;MACvC,CAAC;MACDnC,gBAAgB,CAACtI,OAAO,GAAG,UAAU5B,KAAK,EAAE;QACxC,OAAOgL,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEN,QAAQ,EAAE1K,KAAK,CAAC;MAC1D,CAAC;MACDkK,gBAAgB,CAACC,MAAM,GAAG,UAAU5P,KAAK,EAAE;QACvC,OAAOyQ,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEL,QAAQ,EAAEpQ,KAAK,CAAC;MAC1D,CAAC;MACD2P,gBAAgB,CAACsC,GAAG,GAAG,UAAUC,MAAM,EAAE;QACrC,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;UAC1D,OAAOC,OAAO,CAACzC,MAAM,CAAC,IAAImC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIO,QAAQ,GAAG,EAAE;QACjB,IAAI/P,KAAK,GAAG,CAAC;QACb,IAAI;UACA,KAAK,IAAIgQ,EAAE,GAAG,CAAC,EAAEC,QAAQ,GAAGN,MAAM,EAAEK,EAAE,GAAGC,QAAQ,CAACrW,MAAM,EAAEoW,EAAE,EAAE,EAAE;YAC5D,IAAI/B,CAAC,GAAGgC,QAAQ,CAACD,EAAE,CAAC;YACpBhQ,KAAK,EAAE;YACP+P,QAAQ,CAAC/M,IAAI,CAACoK,gBAAgB,CAACtI,OAAO,CAACmJ,CAAC,CAAC,CAAC;UAC9C;QACJ,CAAC,CACD,OAAO3O,GAAG,EAAE;UACR,OAAOwQ,OAAO,CAACzC,MAAM,CAAC,IAAImC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIxP,KAAK,KAAK,CAAC,EAAE;UACb,OAAO8P,OAAO,CAACzC,MAAM,CAAC,IAAImC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIU,QAAQ,GAAG,KAAK;QACpB,IAAIC,MAAM,GAAG,EAAE;QACf,OAAO,IAAI/C,gBAAgB,CAAC,UAAUtI,OAAO,EAAEuI,MAAM,EAAE;UACnD,KAAK,IAAIxT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkW,QAAQ,CAACnW,MAAM,EAAEC,CAAC,EAAE,EAAE;YACtCkW,QAAQ,CAAClW,CAAC,CAAC,CAACoT,IAAI,CAAC,UAAUgB,CAAC,EAAE;cAC1B,IAAIiC,QAAQ,EAAE;gBACV;cACJ;cACAA,QAAQ,GAAG,IAAI;cACfpL,OAAO,CAACmJ,CAAC,CAAC;YACd,CAAC,EAAE,UAAU3O,GAAG,EAAE;cACd6Q,MAAM,CAACnN,IAAI,CAAC1D,GAAG,CAAC;cAChBU,KAAK,EAAE;cACP,IAAIA,KAAK,KAAK,CAAC,EAAE;gBACbkQ,QAAQ,GAAG,IAAI;gBACf7C,MAAM,CAAC,IAAImC,cAAc,CAACW,MAAM,EAAE,4BAA4B,CAAC,CAAC;cACpE;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN,CAAC;MACD;MACA/C,gBAAgB,CAACgD,IAAI,GAAG,UAAUT,MAAM,EAAE;QACtC,IAAI7K,OAAO;QACX,IAAIuI,MAAM;QACV,IAAIW,OAAO,GAAG,IAAI,IAAI,CAAC,UAAUqC,GAAG,EAAEC,GAAG,EAAE;UACvCxL,OAAO,GAAGuL,GAAG;UACbhD,MAAM,GAAGiD,GAAG;QAChB,CAAC,CAAC;QACF,SAASC,SAAS,CAACrN,KAAK,EAAE;UACtB4B,OAAO,CAAC5B,KAAK,CAAC;QAClB;QACA,SAASsN,QAAQ,CAAC/S,KAAK,EAAE;UACrB4P,MAAM,CAAC5P,KAAK,CAAC;QACjB;QACA,KAAK,IAAIuS,EAAE,GAAG,CAAC,EAAES,QAAQ,GAAGd,MAAM,EAAEK,EAAE,GAAGS,QAAQ,CAAC7W,MAAM,EAAEoW,EAAE,EAAE,EAAE;UAC5D,IAAI9M,KAAK,GAAGuN,QAAQ,CAACT,EAAE,CAAC;UACxB,IAAI,CAAChD,UAAU,CAAC9J,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC4B,OAAO,CAAC5B,KAAK,CAAC;UAC/B;UACAA,KAAK,CAAC+J,IAAI,CAACsD,SAAS,EAAEC,QAAQ,CAAC;QACnC;QACA,OAAOxC,OAAO;MAClB,CAAC;MACDZ,gBAAgB,CAACsD,GAAG,GAAG,UAAUf,MAAM,EAAE;QACrC,OAAOvC,gBAAgB,CAACuD,eAAe,CAAChB,MAAM,CAAC;MACnD,CAAC;MACDvC,gBAAgB,CAACwD,UAAU,GAAG,UAAUjB,MAAM,EAAE;QAC5C,IAAIkB,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC5W,SAAS,YAAYmT,gBAAgB,GAAG,IAAI,GAAGA,gBAAgB;QACpF,OAAOyD,CAAC,CAACF,eAAe,CAAChB,MAAM,EAAE;UAC7BmB,YAAY,EAAE,UAAU5N,KAAK,EAAE;YAAE,OAAQ;cAAE6N,MAAM,EAAE,WAAW;cAAE7N,KAAK,EAAEA;YAAM,CAAC;UAAG,CAAC;UAClF8N,aAAa,EAAE,UAAU1R,GAAG,EAAE;YAAE,OAAQ;cAAEyR,MAAM,EAAE,UAAU;cAAEE,MAAM,EAAE3R;YAAI,CAAC;UAAG;QAClF,CAAC,CAAC;MACN,CAAC;MACD8N,gBAAgB,CAACuD,eAAe,GAAG,UAAUhB,MAAM,EAAE3S,QAAQ,EAAE;QAC3D,IAAI8H,OAAO;QACX,IAAIuI,MAAM;QACV,IAAIW,OAAO,GAAG,IAAI,IAAI,CAAC,UAAUqC,GAAG,EAAEC,GAAG,EAAE;UACvCxL,OAAO,GAAGuL,GAAG;UACbhD,MAAM,GAAGiD,GAAG;QAChB,CAAC,CAAC;QACF;QACA,IAAIY,eAAe,GAAG,CAAC;QACvB,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,cAAc,GAAG,EAAE;QACvB,IAAIC,OAAO,GAAG,UAAUnO,KAAK,EAAE;UAC3B,IAAI,CAAC8J,UAAU,CAAC9J,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAGoO,MAAM,CAACxM,OAAO,CAAC5B,KAAK,CAAC;UACjC;UACA,IAAIqO,aAAa,GAAGJ,UAAU;UAC9B,IAAI;YACAjO,KAAK,CAAC+J,IAAI,CAAC,UAAU/J,KAAK,EAAE;cACxBkO,cAAc,CAACG,aAAa,CAAC,GAAGvU,QAAQ,GAAGA,QAAQ,CAAC8T,YAAY,CAAC5N,KAAK,CAAC,GAAGA,KAAK;cAC/EgO,eAAe,EAAE;cACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;gBACvBpM,OAAO,CAACsM,cAAc,CAAC;cAC3B;YACJ,CAAC,EAAE,UAAU9R,GAAG,EAAE;cACd,IAAI,CAACtC,QAAQ,EAAE;gBACXqQ,MAAM,CAAC/N,GAAG,CAAC;cACf,CAAC,MACI;gBACD8R,cAAc,CAACG,aAAa,CAAC,GAAGvU,QAAQ,CAACgU,aAAa,CAAC1R,GAAG,CAAC;gBAC3D4R,eAAe,EAAE;gBACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;kBACvBpM,OAAO,CAACsM,cAAc,CAAC;gBAC3B;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CACD,OAAOI,OAAO,EAAE;YACZnE,MAAM,CAACmE,OAAO,CAAC;UACnB;UACAN,eAAe,EAAE;UACjBC,UAAU,EAAE;QAChB,CAAC;QACD,IAAIG,MAAM,GAAG,IAAI;QACjB,KAAK,IAAItB,EAAE,GAAG,CAAC,EAAEyB,QAAQ,GAAG9B,MAAM,EAAEK,EAAE,GAAGyB,QAAQ,CAAC7X,MAAM,EAAEoW,EAAE,EAAE,EAAE;UAC5D,IAAI9M,KAAK,GAAGuO,QAAQ,CAACzB,EAAE,CAAC;UACxBqB,OAAO,CAACnO,KAAK,CAAC;QAClB;QACA;QACAgO,eAAe,IAAI,CAAC;QACpB,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvBpM,OAAO,CAACsM,cAAc,CAAC;QAC3B;QACA,OAAOpD,OAAO;MAClB,CAAC;MACDnS,MAAM,CAACC,cAAc,CAACsR,gBAAgB,CAACnT,SAAS,EAAE2V,MAAM,CAAC8B,WAAW,EAAE;QAClE3V,GAAG,EAAE,YAAY;UACb,OAAO,SAAS;QACpB,CAAC;QACDG,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFN,MAAM,CAACC,cAAc,CAACsR,gBAAgB,CAACnT,SAAS,EAAE2V,MAAM,CAAC+B,OAAO,EAAE;QAC9D5V,GAAG,EAAE,YAAY;UACb,OAAOqR,gBAAgB;QAC3B,CAAC;QACDlR,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAClB,CAAC,CAAC;MACFiR,gBAAgB,CAACnT,SAAS,CAACgT,IAAI,GAAG,UAAUiC,WAAW,EAAEC,UAAU,EAAE;QACjE,IAAIyC,EAAE;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIC,CAAC,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC9J,WAAW,MAAM,IAAI,IAAI8J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,MAAM,CAAC+B,OAAO,CAAC;QACvF,IAAI,CAACE,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG,IAAI,CAAC/J,WAAW,IAAIsF,gBAAgB;QAC5C;QACA,IAAI6B,YAAY,GAAG,IAAI4C,CAAC,CAACxM,IAAI,CAAC;QAC9B,IAAIrJ,IAAI,GAAGd,IAAI,CAACe,OAAO;QACvB,IAAI,IAAI,CAACqR,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAACvK,IAAI,CAAChH,IAAI,EAAEiT,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QACvE,CAAC,MACI;UACDL,uBAAuB,CAAC,IAAI,EAAE9S,IAAI,EAAEiT,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QAC9E;QACA,OAAOF,YAAY;MACvB,CAAC;MACD7B,gBAAgB,CAACnT,SAAS,CAAC6X,KAAK,GAAG,UAAU3C,UAAU,EAAE;QACrD,OAAO,IAAI,CAAClC,IAAI,CAAC,IAAI,EAAEkC,UAAU,CAAC;MACtC,CAAC;MACD/B,gBAAgB,CAACnT,SAAS,CAAC8X,OAAO,GAAG,UAAUC,SAAS,EAAE;QACtD,IAAIJ,EAAE;QACN;QACA,IAAIC,CAAC,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC9J,WAAW,MAAM,IAAI,IAAI8J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,MAAM,CAAC+B,OAAO,CAAC;QACvF,IAAI,CAACE,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAGzE,gBAAgB;QACxB;QACA,IAAI6B,YAAY,GAAG,IAAI4C,CAAC,CAACxM,IAAI,CAAC;QAC9B4J,YAAY,CAACzB,aAAa,CAAC,GAAGA,aAAa;QAC3C,IAAIxR,IAAI,GAAGd,IAAI,CAACe,OAAO;QACvB,IAAI,IAAI,CAACqR,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAACvK,IAAI,CAAChH,IAAI,EAAEiT,YAAY,EAAE+C,SAAS,EAAEA,SAAS,CAAC;QACpE,CAAC,MACI;UACDlD,uBAAuB,CAAC,IAAI,EAAE9S,IAAI,EAAEiT,YAAY,EAAE+C,SAAS,EAAEA,SAAS,CAAC;QAC3E;QACA,OAAO/C,YAAY;MACvB,CAAC;MACD,OAAO7B,gBAAgB;IAC3B,CAAC,EAAG;IACJ;IACA;IACAA,gBAAgB,CAAC,SAAS,CAAC,GAAGA,gBAAgB,CAACtI,OAAO;IACtDsI,gBAAgB,CAAC,QAAQ,CAAC,GAAGA,gBAAgB,CAACC,MAAM;IACpDD,gBAAgB,CAAC,MAAM,CAAC,GAAGA,gBAAgB,CAACgD,IAAI;IAChDhD,gBAAgB,CAAC,KAAK,CAAC,GAAGA,gBAAgB,CAACsD,GAAG;IAC9C,IAAIuB,aAAa,GAAGzX,MAAM,CAAC+J,aAAa,CAAC,GAAG/J,MAAM,CAAC,SAAS,CAAC;IAC7DA,MAAM,CAAC,SAAS,CAAC,GAAG4S,gBAAgB;IACpC,IAAI8E,iBAAiB,GAAGnX,UAAU,CAAC,aAAa,CAAC;IACjD,SAAS4K,SAAS,CAACwM,IAAI,EAAE;MACrB,IAAIrH,KAAK,GAAGqH,IAAI,CAAClY,SAAS;MAC1B,IAAI0P,IAAI,GAAG1D,8BAA8B,CAAC6E,KAAK,EAAE,MAAM,CAAC;MACxD,IAAInB,IAAI,KAAKA,IAAI,CAACtB,QAAQ,KAAK,KAAK,IAAI,CAACsB,IAAI,CAACxN,YAAY,CAAC,EAAE;QACzD;QACA;QACA;MACJ;MACA,IAAIiW,YAAY,GAAGtH,KAAK,CAACmC,IAAI;MAC7B;MACAnC,KAAK,CAACtG,UAAU,CAAC,GAAG4N,YAAY;MAChCD,IAAI,CAAClY,SAAS,CAACgT,IAAI,GAAG,UAAUsD,SAAS,EAAEC,QAAQ,EAAE;QACjD,IAAI6B,KAAK,GAAG,IAAI;QAChB,IAAIC,OAAO,GAAG,IAAIlF,gBAAgB,CAAC,UAAUtI,OAAO,EAAEuI,MAAM,EAAE;UAC1D+E,YAAY,CAACjY,IAAI,CAACkY,KAAK,EAAEvN,OAAO,EAAEuI,MAAM,CAAC;QAC7C,CAAC,CAAC;QACF,OAAOiF,OAAO,CAACrF,IAAI,CAACsD,SAAS,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MACD2B,IAAI,CAACD,iBAAiB,CAAC,GAAG,IAAI;IAClC;IACApG,GAAG,CAACnG,SAAS,GAAGA,SAAS;IACzB,SAAS4M,OAAO,CAAChW,EAAE,EAAE;MACjB,OAAO,UAAUmH,IAAI,EAAEE,IAAI,EAAE;QACzB,IAAI4O,aAAa,GAAGjW,EAAE,CAACuG,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;QACxC,IAAI4O,aAAa,YAAYpF,gBAAgB,EAAE;UAC3C,OAAOoF,aAAa;QACxB;QACA,IAAIC,IAAI,GAAGD,aAAa,CAAC1K,WAAW;QACpC,IAAI,CAAC2K,IAAI,CAACP,iBAAiB,CAAC,EAAE;UAC1BvM,SAAS,CAAC8M,IAAI,CAAC;QACnB;QACA,OAAOD,aAAa;MACxB,CAAC;IACL;IACA,IAAIP,aAAa,EAAE;MACftM,SAAS,CAACsM,aAAa,CAAC;MACxBxM,WAAW,CAACjL,MAAM,EAAE,OAAO,EAAE,UAAU2F,QAAQ,EAAE;QAAE,OAAOoS,OAAO,CAACpS,QAAQ,CAAC;MAAE,CAAC,CAAC;IACnF;IACA;IACA2P,OAAO,CAAC5U,IAAI,CAACH,UAAU,CAAC,uBAAuB,CAAC,CAAC,GAAGmR,sBAAsB;IAC1E,OAAOkB,gBAAgB;EAC3B,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACAlS,IAAI,CAACoB,YAAY,CAAC,UAAU,EAAE,UAAU9B,MAAM,EAAE;IAC5C;IACA,IAAIkY,wBAAwB,GAAGC,QAAQ,CAAC1Y,SAAS,CAACkK,QAAQ;IAC1D,IAAIyO,wBAAwB,GAAGrL,YAAY,CAAC,kBAAkB,CAAC;IAC/D,IAAIsL,cAAc,GAAGtL,YAAY,CAAC,SAAS,CAAC;IAC5C,IAAIuL,YAAY,GAAGvL,YAAY,CAAC,OAAO,CAAC;IACxC,IAAIwL,mBAAmB,GAAG,SAAS5O,QAAQ,GAAG;MAC1C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC5B,IAAI6O,gBAAgB,GAAG,IAAI,CAACJ,wBAAwB,CAAC;QACrD,IAAII,gBAAgB,EAAE;UAClB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;YACxC,OAAON,wBAAwB,CAACvY,IAAI,CAAC6Y,gBAAgB,CAAC;UAC1D,CAAC,MACI;YACD,OAAOnX,MAAM,CAAC5B,SAAS,CAACkK,QAAQ,CAAChK,IAAI,CAAC6Y,gBAAgB,CAAC;UAC3D;QACJ;QACA,IAAI,IAAI,KAAKlD,OAAO,EAAE;UAClB,IAAImD,aAAa,GAAGzY,MAAM,CAACqY,cAAc,CAAC;UAC1C,IAAII,aAAa,EAAE;YACf,OAAOP,wBAAwB,CAACvY,IAAI,CAAC8Y,aAAa,CAAC;UACvD;QACJ;QACA,IAAI,IAAI,KAAKhY,KAAK,EAAE;UAChB,IAAIiY,WAAW,GAAG1Y,MAAM,CAACsY,YAAY,CAAC;UACtC,IAAII,WAAW,EAAE;YACb,OAAOR,wBAAwB,CAACvY,IAAI,CAAC+Y,WAAW,CAAC;UACrD;QACJ;MACJ;MACA,OAAOR,wBAAwB,CAACvY,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC;IACD4Y,mBAAmB,CAACH,wBAAwB,CAAC,GAAGF,wBAAwB;IACxEC,QAAQ,CAAC1Y,SAAS,CAACkK,QAAQ,GAAG4O,mBAAmB;IACjD;IACA,IAAII,sBAAsB,GAAGtX,MAAM,CAAC5B,SAAS,CAACkK,QAAQ;IACtD,IAAIiP,wBAAwB,GAAG,kBAAkB;IACjDvX,MAAM,CAAC5B,SAAS,CAACkK,QAAQ,GAAG,YAAY;MACpC,IAAI,OAAO2L,OAAO,KAAK,UAAU,IAAI,IAAI,YAAYA,OAAO,EAAE;QAC1D,OAAOsD,wBAAwB;MACnC;MACA,OAAOD,sBAAsB,CAAChZ,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC;EACL,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIkZ,gBAAgB,GAAG,KAAK;EAC5B,IAAI,OAAO3M,MAAM,KAAK,WAAW,EAAE;IAC/B,IAAI;MACA,IAAIlD,OAAO,GAAG3H,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;QAC/CC,GAAG,EAAE,YAAY;UACbsX,gBAAgB,GAAG,IAAI;QAC3B;MACJ,CAAC,CAAC;MACF;MACA;MACA;MACA3M,MAAM,CAAC0D,gBAAgB,CAAC,MAAM,EAAE5G,OAAO,EAAEA,OAAO,CAAC;MACjDkD,MAAM,CAACyD,mBAAmB,CAAC,MAAM,EAAE3G,OAAO,EAAEA,OAAO,CAAC;IACxD,CAAC,CACD,OAAOlE,GAAG,EAAE;MACR+T,gBAAgB,GAAG,KAAK;IAC5B;EACJ;EACA;EACA,IAAIC,8BAA8B,GAAG;IACjC3P,IAAI,EAAE;EACV,CAAC;EACD,IAAI4P,oBAAoB,GAAG,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGrM,kBAAkB,GAAG,qBAAqB,CAAC;EACzF,IAAIsM,4BAA4B,GAAGpM,YAAY,CAAC,oBAAoB,CAAC;EACrE,SAASqM,iBAAiB,CAAC5J,SAAS,EAAE6J,iBAAiB,EAAE;IACrD,IAAIC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAAC7J,SAAS,CAAC,GAAGA,SAAS,IAAI5C,SAAS;IAC/F,IAAI2M,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAAC7J,SAAS,CAAC,GAAGA,SAAS,IAAI7C,QAAQ;IAC7F,IAAIhC,MAAM,GAAGkC,kBAAkB,GAAGyM,cAAc;IAChD,IAAIE,aAAa,GAAG3M,kBAAkB,GAAG0M,aAAa;IACtDR,oBAAoB,CAACvJ,SAAS,CAAC,GAAG,CAAC,CAAC;IACpCuJ,oBAAoB,CAACvJ,SAAS,CAAC,CAAC5C,SAAS,CAAC,GAAGjC,MAAM;IACnDoO,oBAAoB,CAACvJ,SAAS,CAAC,CAAC7C,QAAQ,CAAC,GAAG6M,aAAa;EAC7D;EACA,SAASzO,gBAAgB,CAACmC,OAAO,EAAEoE,GAAG,EAAEmI,IAAI,EAAEC,YAAY,EAAE;IACxD,IAAIC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAAG,IAAKrN,sBAAsB;IACrF,IAAIsN,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAAE,IAAKtN,yBAAyB;IAC1F,IAAIuN,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAAS,IAAK,gBAAgB;IAC3F,IAAIC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAAK,IAAK,oBAAoB;IACtG,IAAIC,0BAA0B,GAAGpN,YAAY,CAAC4M,kBAAkB,CAAC;IACjE,IAAIS,yBAAyB,GAAG,GAAG,GAAGT,kBAAkB,GAAG,GAAG;IAC9D,IAAIU,sBAAsB,GAAG,iBAAiB;IAC9C,IAAIC,6BAA6B,GAAG,GAAG,GAAGD,sBAAsB,GAAG,GAAG;IACtE,IAAIhW,UAAU,GAAG,UAAUjB,IAAI,EAAEyC,MAAM,EAAE0I,KAAK,EAAE;MAC5C;MACA;MACA,IAAInL,IAAI,CAACmX,SAAS,EAAE;QAChB;MACJ;MACA,IAAI5U,QAAQ,GAAGvC,IAAI,CAACZ,QAAQ;MAC5B,IAAI,OAAOmD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAAC6U,WAAW,EAAE;QACtD;QACApX,IAAI,CAACZ,QAAQ,GAAG,UAAU+L,KAAK,EAAE;UAAE,OAAO5I,QAAQ,CAAC6U,WAAW,CAACjM,KAAK,CAAC;QAAE,CAAC;QACxEnL,IAAI,CAACoV,gBAAgB,GAAG7S,QAAQ;MACpC;MACA;MACA;MACA;MACA;MACA,IAAI1C,KAAK;MACT,IAAI;QACAG,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAEyC,MAAM,EAAE,CAAC0I,KAAK,CAAC,CAAC;MACtC,CAAC,CACD,OAAOzJ,GAAG,EAAE;QACR7B,KAAK,GAAG6B,GAAG;MACf;MACA,IAAIkE,OAAO,GAAG5F,IAAI,CAAC4F,OAAO;MAC1B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC2K,IAAI,EAAE;QACxD;QACA;QACA;QACA,IAAI8G,UAAU,GAAGrX,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACZ,QAAQ;QAC9EqD,MAAM,CAACgU,qBAAqB,CAAC,CAACla,IAAI,CAACkG,MAAM,EAAE0I,KAAK,CAAC/K,IAAI,EAAEiX,UAAU,EAAEzR,OAAO,CAAC;MAC/E;MACA,OAAO/F,KAAK;IAChB,CAAC;IACD,SAASyX,cAAc,CAACC,OAAO,EAAEpM,KAAK,EAAEqM,SAAS,EAAE;MAC/C;MACA;MACArM,KAAK,GAAGA,KAAK,IAAIrB,OAAO,CAACqB,KAAK;MAC9B,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MACA;MACA;MACA,IAAI1I,MAAM,GAAG8U,OAAO,IAAIpM,KAAK,CAAC1I,MAAM,IAAIqH,OAAO;MAC/C,IAAI2N,KAAK,GAAGhV,MAAM,CAACkT,oBAAoB,CAACxK,KAAK,CAAC/K,IAAI,CAAC,CAACoX,SAAS,GAAGjO,QAAQ,GAAGC,SAAS,CAAC,CAAC;MACtF,IAAIiO,KAAK,EAAE;QACP,IAAIlF,MAAM,GAAG,EAAE;QACf;QACA;QACA,IAAIkF,KAAK,CAACzb,MAAM,KAAK,CAAC,EAAE;UACpB,IAAI0F,GAAG,GAAGT,UAAU,CAACwW,KAAK,CAAC,CAAC,CAAC,EAAEhV,MAAM,EAAE0I,KAAK,CAAC;UAC7CzJ,GAAG,IAAI6Q,MAAM,CAACnN,IAAI,CAAC1D,GAAG,CAAC;QAC3B,CAAC,MACI;UACD;UACA;UACA;UACA,IAAIgW,SAAS,GAAGD,KAAK,CAACnb,KAAK,EAAE;UAC7B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyb,SAAS,CAAC1b,MAAM,EAAEC,CAAC,EAAE,EAAE;YACvC,IAAIkP,KAAK,IAAIA,KAAK,CAAC4K,4BAA4B,CAAC,KAAK,IAAI,EAAE;cACvD;YACJ;YACA,IAAIrU,GAAG,GAAGT,UAAU,CAACyW,SAAS,CAACzb,CAAC,CAAC,EAAEwG,MAAM,EAAE0I,KAAK,CAAC;YACjDzJ,GAAG,IAAI6Q,MAAM,CAACnN,IAAI,CAAC1D,GAAG,CAAC;UAC3B;QACJ;QACA;QACA;QACA,IAAI6Q,MAAM,CAACvW,MAAM,KAAK,CAAC,EAAE;UACrB,MAAMuW,MAAM,CAAC,CAAC,CAAC;QACnB,CAAC,MACI;UACD,IAAIoF,OAAO,GAAG,UAAU1b,CAAC,EAAE;YACvB,IAAIyF,GAAG,GAAG6Q,MAAM,CAACtW,CAAC,CAAC;YACnBiS,GAAG,CAAClH,uBAAuB,CAAC,YAAY;cACpC,MAAMtF,GAAG;YACb,CAAC,CAAC;UACN,CAAC;UACD,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsW,MAAM,CAACvW,MAAM,EAAEC,CAAC,EAAE,EAAE;YACpC0b,OAAO,CAAC1b,CAAC,CAAC;UACd;QACJ;MACJ;IACJ;IACA;IACA,IAAI2b,uBAAuB,GAAG,UAAUzM,KAAK,EAAE;MAC3C,OAAOmM,cAAc,CAAC,IAAI,EAAEnM,KAAK,EAAE,KAAK,CAAC;IAC7C,CAAC;IACD;IACA,IAAI0M,8BAA8B,GAAG,UAAU1M,KAAK,EAAE;MAClD,OAAOmM,cAAc,CAAC,IAAI,EAAEnM,KAAK,EAAE,IAAI,CAAC;IAC5C,CAAC;IACD,SAAS2M,uBAAuB,CAAChM,GAAG,EAAEwK,YAAY,EAAE;MAChD,IAAI,CAACxK,GAAG,EAAE;QACN,OAAO,KAAK;MAChB;MACA,IAAIiM,iBAAiB,GAAG,IAAI;MAC5B,IAAIzB,YAAY,IAAIA,YAAY,CAACvQ,IAAI,KAAK/E,SAAS,EAAE;QACjD+W,iBAAiB,GAAGzB,YAAY,CAACvQ,IAAI;MACzC;MACA,IAAIiS,eAAe,GAAG1B,YAAY,IAAIA,YAAY,CAAC2B,EAAE;MACrD,IAAI7a,cAAc,GAAG,IAAI;MACzB,IAAIkZ,YAAY,IAAIA,YAAY,CAAC4B,MAAM,KAAKlX,SAAS,EAAE;QACnD5D,cAAc,GAAGkZ,YAAY,CAAC4B,MAAM;MACxC;MACA,IAAIC,YAAY,GAAG,KAAK;MACxB,IAAI7B,YAAY,IAAIA,YAAY,CAAC8B,EAAE,KAAKpX,SAAS,EAAE;QAC/CmX,YAAY,GAAG7B,YAAY,CAAC8B,EAAE;MAClC;MACA,IAAIlL,KAAK,GAAGpB,GAAG;MACf,OAAOoB,KAAK,IAAI,CAACA,KAAK,CAACrO,cAAc,CAAC0X,kBAAkB,CAAC,EAAE;QACvDrJ,KAAK,GAAGlE,oBAAoB,CAACkE,KAAK,CAAC;MACvC;MACA,IAAI,CAACA,KAAK,IAAIpB,GAAG,CAACyK,kBAAkB,CAAC,EAAE;QACnC;QACArJ,KAAK,GAAGpB,GAAG;MACf;MACA,IAAI,CAACoB,KAAK,EAAE;QACR,OAAO,KAAK;MAChB;MACA,IAAIA,KAAK,CAAC6J,0BAA0B,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA,IAAId,iBAAiB,GAAGK,YAAY,IAAIA,YAAY,CAACL,iBAAiB;MACtE;MACA;MACA,IAAIoC,QAAQ,GAAG,CAAC,CAAC;MACjB,IAAIC,sBAAsB,GAAGpL,KAAK,CAAC6J,0BAA0B,CAAC,GAAG7J,KAAK,CAACqJ,kBAAkB,CAAC;MAC1F,IAAIgC,yBAAyB,GAAGrL,KAAK,CAACvD,YAAY,CAAC8M,qBAAqB,CAAC,CAAC,GACtEvJ,KAAK,CAACuJ,qBAAqB,CAAC;MAChC,IAAI+B,eAAe,GAAGtL,KAAK,CAACvD,YAAY,CAACgN,wBAAwB,CAAC,CAAC,GAC/DzJ,KAAK,CAACyJ,wBAAwB,CAAC;MACnC,IAAI8B,wBAAwB,GAAGvL,KAAK,CAACvD,YAAY,CAACkN,mCAAmC,CAAC,CAAC,GACnF3J,KAAK,CAAC2J,mCAAmC,CAAC;MAC9C,IAAI6B,0BAA0B;MAC9B,IAAIpC,YAAY,IAAIA,YAAY,CAACqC,OAAO,EAAE;QACtCD,0BAA0B,GAAGxL,KAAK,CAACvD,YAAY,CAAC2M,YAAY,CAACqC,OAAO,CAAC,CAAC,GAClEzL,KAAK,CAACoJ,YAAY,CAACqC,OAAO,CAAC;MACnC;MACA;AACZ;AACA;AACA;MACY,SAASC,yBAAyB,CAAChT,OAAO,EAAEiT,OAAO,EAAE;QACjD,IAAI,CAACpD,gBAAgB,IAAI,OAAO7P,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;UAC7D;UACA;UACA;UACA,OAAO,CAAC,CAACA,OAAO,CAACkT,OAAO;QAC5B;QACA,IAAI,CAACrD,gBAAgB,IAAI,CAACoD,OAAO,EAAE;UAC/B,OAAOjT,OAAO;QAClB;QACA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;UAC9B,OAAO;YAAEkT,OAAO,EAAElT,OAAO;YAAEiT,OAAO,EAAE;UAAK,CAAC;QAC9C;QACA,IAAI,CAACjT,OAAO,EAAE;UACV,OAAO;YAAEiT,OAAO,EAAE;UAAK,CAAC;QAC5B;QACA,IAAI,OAAOjT,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACiT,OAAO,KAAK,KAAK,EAAE;UAC1D,OAAO5a,MAAM,CAAC8a,MAAM,CAAC9a,MAAM,CAAC8a,MAAM,CAAC,CAAC,CAAC,EAAEnT,OAAO,CAAC,EAAE;YAAEiT,OAAO,EAAE;UAAK,CAAC,CAAC;QACvE;QACA,OAAOjT,OAAO;MAClB;MACA,IAAIoT,oBAAoB,GAAG,UAAUhZ,IAAI,EAAE;QACvC;QACA;QACA,IAAIqY,QAAQ,CAACY,UAAU,EAAE;UACrB;QACJ;QACA,OAAOX,sBAAsB,CAAC/b,IAAI,CAAC8b,QAAQ,CAAC5V,MAAM,EAAE4V,QAAQ,CAACjM,SAAS,EAAEiM,QAAQ,CAACS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAES,QAAQ,CAACzS,OAAO,CAAC;MAC1K,CAAC;MACD,IAAIsT,kBAAkB,GAAG,UAAUlZ,IAAI,EAAE;QACrC;QACA;QACA;QACA,IAAI,CAACA,IAAI,CAACmX,SAAS,EAAE;UACjB,IAAIgC,gBAAgB,GAAGxD,oBAAoB,CAAC3V,IAAI,CAACoM,SAAS,CAAC;UAC3D,IAAIgN,eAAe,GAAG,KAAK,CAAC;UAC5B,IAAID,gBAAgB,EAAE;YAClBC,eAAe,GAAGD,gBAAgB,CAACnZ,IAAI,CAAC8Y,OAAO,GAAGvP,QAAQ,GAAGC,SAAS,CAAC;UAC3E;UACA,IAAI6P,aAAa,GAAGD,eAAe,IAAIpZ,IAAI,CAACyC,MAAM,CAAC2W,eAAe,CAAC;UACnE,IAAIC,aAAa,EAAE;YACf,KAAK,IAAIpd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGod,aAAa,CAACrd,MAAM,EAAEC,CAAC,EAAE,EAAE;cAC3C,IAAIqd,YAAY,GAAGD,aAAa,CAACpd,CAAC,CAAC;cACnC,IAAIqd,YAAY,KAAKtZ,IAAI,EAAE;gBACvBqZ,aAAa,CAACjI,MAAM,CAACnV,CAAC,EAAE,CAAC,CAAC;gBAC1B;gBACA+D,IAAI,CAACmX,SAAS,GAAG,IAAI;gBACrB,IAAIkC,aAAa,CAACrd,MAAM,KAAK,CAAC,EAAE;kBAC5B;kBACA;kBACAgE,IAAI,CAACuZ,UAAU,GAAG,IAAI;kBACtBvZ,IAAI,CAACyC,MAAM,CAAC2W,eAAe,CAAC,GAAG,IAAI;gBACvC;gBACA;cACJ;YACJ;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACpZ,IAAI,CAACuZ,UAAU,EAAE;UAClB;QACJ;QACA,OAAOhB,yBAAyB,CAAChc,IAAI,CAACyD,IAAI,CAACyC,MAAM,EAAEzC,IAAI,CAACoM,SAAS,EAAEpM,IAAI,CAAC8Y,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAE5X,IAAI,CAAC4F,OAAO,CAAC;MAC7J,CAAC;MACD,IAAI4T,uBAAuB,GAAG,UAAUxZ,IAAI,EAAE;QAC1C,OAAOsY,sBAAsB,CAAC/b,IAAI,CAAC8b,QAAQ,CAAC5V,MAAM,EAAE4V,QAAQ,CAACjM,SAAS,EAAEpM,IAAI,CAACJ,MAAM,EAAEyY,QAAQ,CAACzS,OAAO,CAAC;MAC1G,CAAC;MACD,IAAI6T,qBAAqB,GAAG,UAAUzZ,IAAI,EAAE;QACxC,OAAO0Y,0BAA0B,CAACnc,IAAI,CAAC8b,QAAQ,CAAC5V,MAAM,EAAE4V,QAAQ,CAACjM,SAAS,EAAEpM,IAAI,CAACJ,MAAM,EAAEyY,QAAQ,CAACzS,OAAO,CAAC;MAC9G,CAAC;MACD,IAAI8T,qBAAqB,GAAG,UAAU1Z,IAAI,EAAE;QACxC,OAAOuY,yBAAyB,CAAChc,IAAI,CAACyD,IAAI,CAACyC,MAAM,EAAEzC,IAAI,CAACoM,SAAS,EAAEpM,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAAC4F,OAAO,CAAC;MACjG,CAAC;MACD,IAAIhE,cAAc,GAAGmW,iBAAiB,GAAGiB,oBAAoB,GAAGQ,uBAAuB;MACvF,IAAIxX,YAAY,GAAG+V,iBAAiB,GAAGmB,kBAAkB,GAAGQ,qBAAqB;MACjF,IAAIC,6BAA6B,GAAG,UAAU3Z,IAAI,EAAEuC,QAAQ,EAAE;QAC1D,IAAIqX,cAAc,GAAG,OAAOrX,QAAQ;QACpC,OAAQqX,cAAc,KAAK,UAAU,IAAI5Z,IAAI,CAACZ,QAAQ,KAAKmD,QAAQ,IAC9DqX,cAAc,KAAK,QAAQ,IAAI5Z,IAAI,CAACoV,gBAAgB,KAAK7S,QAAS;MAC3E,CAAC;MACD,IAAIsX,OAAO,GAAIvD,YAAY,IAAIA,YAAY,CAACwD,IAAI,GAAIxD,YAAY,CAACwD,IAAI,GAAGH,6BAA6B;MACrG,IAAII,eAAe,GAAGzc,IAAI,CAACqM,YAAY,CAAC,kBAAkB,CAAC,CAAC;MAC5D,IAAIqQ,aAAa,GAAGlQ,OAAO,CAACH,YAAY,CAAC,gBAAgB,CAAC,CAAC;MAC3D,IAAIsQ,eAAe,GAAG,UAAUC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAElC,YAAY,EAAEQ,OAAO,EAAE;QAChH,IAAIR,YAAY,KAAK,KAAK,CAAC,EAAE;UAAEA,YAAY,GAAG,KAAK;QAAE;QACrD,IAAIQ,OAAO,KAAK,KAAK,CAAC,EAAE;UAAEA,OAAO,GAAG,KAAK;QAAE;QAC3C,OAAO,YAAY;UACf,IAAIlW,MAAM,GAAG,IAAI,IAAIqH,OAAO;UAC5B,IAAIsC,SAAS,GAAGrQ,SAAS,CAAC,CAAC,CAAC;UAC5B,IAAIua,YAAY,IAAIA,YAAY,CAACgE,iBAAiB,EAAE;YAChDlO,SAAS,GAAGkK,YAAY,CAACgE,iBAAiB,CAAClO,SAAS,CAAC;UACzD;UACA,IAAI7J,QAAQ,GAAGxG,SAAS,CAAC,CAAC,CAAC;UAC3B,IAAI,CAACwG,QAAQ,EAAE;YACX,OAAO2X,cAAc,CAAChV,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;UAChD;UACA,IAAI8O,MAAM,IAAIuB,SAAS,KAAK,mBAAmB,EAAE;YAC7C;YACA,OAAO8N,cAAc,CAAChV,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;UAChD;UACA;UACA;UACA;UACA,IAAIwe,aAAa,GAAG,KAAK;UACzB,IAAI,OAAOhY,QAAQ,KAAK,UAAU,EAAE;YAChC,IAAI,CAACA,QAAQ,CAAC6U,WAAW,EAAE;cACvB,OAAO8C,cAAc,CAAChV,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;YAChD;YACAwe,aAAa,GAAG,IAAI;UACxB;UACA,IAAIvC,eAAe,IAAI,CAACA,eAAe,CAACkC,cAAc,EAAE3X,QAAQ,EAAEE,MAAM,EAAE1G,SAAS,CAAC,EAAE;YAClF;UACJ;UACA,IAAI8c,OAAO,GAAGpD,gBAAgB,IAAI,CAAC,CAACuE,aAAa,IAAIA,aAAa,CAAC/L,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;UAC5F,IAAIxG,OAAO,GAAGgT,yBAAyB,CAAC7c,SAAS,CAAC,CAAC,CAAC,EAAE8c,OAAO,CAAC;UAC9D,IAAIkB,eAAe,EAAE;YACjB;YACA,KAAK,IAAI9d,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8d,eAAe,CAAC/d,MAAM,EAAEC,CAAC,EAAE,EAAE;cAC7C,IAAImQ,SAAS,KAAK2N,eAAe,CAAC9d,CAAC,CAAC,EAAE;gBAClC,IAAI4c,OAAO,EAAE;kBACT,OAAOqB,cAAc,CAAC3d,IAAI,CAACkG,MAAM,EAAE2J,SAAS,EAAE7J,QAAQ,EAAEqD,OAAO,CAAC;gBACpE,CAAC,MACI;kBACD,OAAOsU,cAAc,CAAChV,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;gBAChD;cACJ;YACJ;UACJ;UACA,IAAI+c,OAAO,GAAG,CAAClT,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACkT,OAAO;UACtF,IAAIvI,IAAI,GAAG3K,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC2K,IAAI,GAAG,KAAK;UACxE,IAAInS,IAAI,GAAGd,IAAI,CAACe,OAAO;UACvB,IAAI8a,gBAAgB,GAAGxD,oBAAoB,CAACvJ,SAAS,CAAC;UACtD,IAAI,CAAC+M,gBAAgB,EAAE;YACnBnD,iBAAiB,CAAC5J,SAAS,EAAE6J,iBAAiB,CAAC;YAC/CkD,gBAAgB,GAAGxD,oBAAoB,CAACvJ,SAAS,CAAC;UACtD;UACA,IAAIgN,eAAe,GAAGD,gBAAgB,CAACL,OAAO,GAAGvP,QAAQ,GAAGC,SAAS,CAAC;UACtE,IAAI6P,aAAa,GAAG5W,MAAM,CAAC2W,eAAe,CAAC;UAC3C,IAAIH,UAAU,GAAG,KAAK;UACtB,IAAII,aAAa,EAAE;YACf;YACAJ,UAAU,GAAG,IAAI;YACjB,IAAI7b,cAAc,EAAE;cAChB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGod,aAAa,CAACrd,MAAM,EAAEC,CAAC,EAAE,EAAE;gBAC3C,IAAI4d,OAAO,CAACR,aAAa,CAACpd,CAAC,CAAC,EAAEsG,QAAQ,CAAC,EAAE;kBACrC;kBACA;gBACJ;cACJ;YACJ;UACJ,CAAC,MACI;YACD8W,aAAa,GAAG5W,MAAM,CAAC2W,eAAe,CAAC,GAAG,EAAE;UAChD;UACA,IAAI/Z,MAAM;UACV,IAAImb,eAAe,GAAG/X,MAAM,CAACyH,WAAW,CAAC,MAAM,CAAC;UAChD,IAAIuQ,YAAY,GAAG7E,aAAa,CAAC4E,eAAe,CAAC;UACjD,IAAIC,YAAY,EAAE;YACdpb,MAAM,GAAGob,YAAY,CAACrO,SAAS,CAAC;UACpC;UACA,IAAI,CAAC/M,MAAM,EAAE;YACTA,MAAM,GAAGmb,eAAe,GAAGL,SAAS,IAC/BlE,iBAAiB,GAAGA,iBAAiB,CAAC7J,SAAS,CAAC,GAAGA,SAAS,CAAC;UACtE;UACA;UACA;UACAiM,QAAQ,CAACzS,OAAO,GAAGA,OAAO;UAC1B,IAAI2K,IAAI,EAAE;YACN;YACA;YACA;YACA8H,QAAQ,CAACzS,OAAO,CAAC2K,IAAI,GAAG,KAAK;UACjC;UACA8H,QAAQ,CAAC5V,MAAM,GAAGA,MAAM;UACxB4V,QAAQ,CAACS,OAAO,GAAGA,OAAO;UAC1BT,QAAQ,CAACjM,SAAS,GAAGA,SAAS;UAC9BiM,QAAQ,CAACY,UAAU,GAAGA,UAAU;UAChC,IAAIpY,IAAI,GAAGkX,iBAAiB,GAAGrC,8BAA8B,GAAG1U,SAAS;UACzE;UACA,IAAIH,IAAI,EAAE;YACNA,IAAI,CAACwX,QAAQ,GAAGA,QAAQ;UAC5B;UACA,IAAIrY,IAAI,GAAG5B,IAAI,CAAC6D,iBAAiB,CAAC5C,MAAM,EAAEkD,QAAQ,EAAE1B,IAAI,EAAEuZ,gBAAgB,EAAEC,cAAc,CAAC;UAC3F;UACA;UACAhC,QAAQ,CAAC5V,MAAM,GAAG,IAAI;UACtB;UACA,IAAI5B,IAAI,EAAE;YACNA,IAAI,CAACwX,QAAQ,GAAG,IAAI;UACxB;UACA;UACA;UACA,IAAI9H,IAAI,EAAE;YACN3K,OAAO,CAAC2K,IAAI,GAAG,IAAI;UACvB;UACA,IAAI,EAAE,CAACkF,gBAAgB,IAAI,OAAOzV,IAAI,CAAC4F,OAAO,KAAK,SAAS,CAAC,EAAE;YAC3D;YACA;YACA5F,IAAI,CAAC4F,OAAO,GAAGA,OAAO;UAC1B;UACA5F,IAAI,CAACyC,MAAM,GAAGA,MAAM;UACpBzC,IAAI,CAAC8Y,OAAO,GAAGA,OAAO;UACtB9Y,IAAI,CAACoM,SAAS,GAAGA,SAAS;UAC1B,IAAImO,aAAa,EAAE;YACf;YACAva,IAAI,CAACoV,gBAAgB,GAAG7S,QAAQ;UACpC;UACA,IAAI,CAACoW,OAAO,EAAE;YACVU,aAAa,CAACjU,IAAI,CAACpF,IAAI,CAAC;UAC5B,CAAC,MACI;YACDqZ,aAAa,CAACqB,OAAO,CAAC1a,IAAI,CAAC;UAC/B;UACA,IAAImY,YAAY,EAAE;YACd,OAAO1V,MAAM;UACjB;QACJ,CAAC;MACL,CAAC;MACDyK,KAAK,CAACqJ,kBAAkB,CAAC,GAAG0D,eAAe,CAAC3B,sBAAsB,EAAEtB,yBAAyB,EAAEpV,cAAc,EAAEI,YAAY,EAAEmW,YAAY,CAAC;MAC1I,IAAIO,0BAA0B,EAAE;QAC5BxL,KAAK,CAAC+J,sBAAsB,CAAC,GAAGgD,eAAe,CAACvB,0BAA0B,EAAExB,6BAA6B,EAAEuC,qBAAqB,EAAEzX,YAAY,EAAEmW,YAAY,EAAE,IAAI,CAAC;MACvK;MACAjL,KAAK,CAACuJ,qBAAqB,CAAC,GAAG,YAAY;QACvC,IAAIhU,MAAM,GAAG,IAAI,IAAIqH,OAAO;QAC5B,IAAIsC,SAAS,GAAGrQ,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAIua,YAAY,IAAIA,YAAY,CAACgE,iBAAiB,EAAE;UAChDlO,SAAS,GAAGkK,YAAY,CAACgE,iBAAiB,CAAClO,SAAS,CAAC;QACzD;QACA,IAAIxG,OAAO,GAAG7J,SAAS,CAAC,CAAC,CAAC;QAC1B,IAAI+c,OAAO,GAAG,CAAClT,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACkT,OAAO;QACtF,IAAIvW,QAAQ,GAAGxG,SAAS,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACwG,QAAQ,EAAE;UACX,OAAOgW,yBAAyB,CAACrT,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;QAC3D;QACA,IAAIic,eAAe,IACf,CAACA,eAAe,CAACO,yBAAyB,EAAEhW,QAAQ,EAAEE,MAAM,EAAE1G,SAAS,CAAC,EAAE;UAC1E;QACJ;QACA,IAAIod,gBAAgB,GAAGxD,oBAAoB,CAACvJ,SAAS,CAAC;QACtD,IAAIgN,eAAe;QACnB,IAAID,gBAAgB,EAAE;UAClBC,eAAe,GAAGD,gBAAgB,CAACL,OAAO,GAAGvP,QAAQ,GAAGC,SAAS,CAAC;QACtE;QACA,IAAI6P,aAAa,GAAGD,eAAe,IAAI3W,MAAM,CAAC2W,eAAe,CAAC;QAC9D,IAAIC,aAAa,EAAE;UACf,KAAK,IAAIpd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGod,aAAa,CAACrd,MAAM,EAAEC,CAAC,EAAE,EAAE;YAC3C,IAAIqd,YAAY,GAAGD,aAAa,CAACpd,CAAC,CAAC;YACnC,IAAI4d,OAAO,CAACP,YAAY,EAAE/W,QAAQ,CAAC,EAAE;cACjC8W,aAAa,CAACjI,MAAM,CAACnV,CAAC,EAAE,CAAC,CAAC;cAC1B;cACAqd,YAAY,CAACnC,SAAS,GAAG,IAAI;cAC7B,IAAIkC,aAAa,CAACrd,MAAM,KAAK,CAAC,EAAE;gBAC5B;gBACA;gBACAsd,YAAY,CAACC,UAAU,GAAG,IAAI;gBAC9B9W,MAAM,CAAC2W,eAAe,CAAC,GAAG,IAAI;gBAC9B;gBACA;gBACA;gBACA,IAAI,OAAOhN,SAAS,KAAK,QAAQ,EAAE;kBAC/B,IAAIuO,gBAAgB,GAAGlR,kBAAkB,GAAG,aAAa,GAAG2C,SAAS;kBACrE3J,MAAM,CAACkY,gBAAgB,CAAC,GAAG,IAAI;gBACnC;cACJ;cACArB,YAAY,CAAClb,IAAI,CAAC8D,UAAU,CAACoX,YAAY,CAAC;cAC1C,IAAInB,YAAY,EAAE;gBACd,OAAO1V,MAAM;cACjB;cACA;YACJ;UACJ;QACJ;QACA;QACA;QACA;QACA;QACA,OAAO8V,yBAAyB,CAACrT,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;MAC3D,CAAC;MACDmR,KAAK,CAACyJ,wBAAwB,CAAC,GAAG,YAAY;QAC1C,IAAIlU,MAAM,GAAG,IAAI,IAAIqH,OAAO;QAC5B,IAAIsC,SAAS,GAAGrQ,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAIua,YAAY,IAAIA,YAAY,CAACgE,iBAAiB,EAAE;UAChDlO,SAAS,GAAGkK,YAAY,CAACgE,iBAAiB,CAAClO,SAAS,CAAC;QACzD;QACA,IAAIwK,SAAS,GAAG,EAAE;QAClB,IAAIa,KAAK,GAAGmD,cAAc,CAACnY,MAAM,EAAEwT,iBAAiB,GAAGA,iBAAiB,CAAC7J,SAAS,CAAC,GAAGA,SAAS,CAAC;QAChG,KAAK,IAAInQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwb,KAAK,CAACzb,MAAM,EAAEC,CAAC,EAAE,EAAE;UACnC,IAAI+D,IAAI,GAAGyX,KAAK,CAACxb,CAAC,CAAC;UACnB,IAAIsG,QAAQ,GAAGvC,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACZ,QAAQ;UAC5EwX,SAAS,CAACxR,IAAI,CAAC7C,QAAQ,CAAC;QAC5B;QACA,OAAOqU,SAAS;MACpB,CAAC;MACD1J,KAAK,CAAC2J,mCAAmC,CAAC,GAAG,YAAY;QACrD,IAAIpU,MAAM,GAAG,IAAI,IAAIqH,OAAO;QAC5B,IAAIsC,SAAS,GAAGrQ,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAI,CAACqQ,SAAS,EAAE;UACZ,IAAIyO,IAAI,GAAG5c,MAAM,CAAC4c,IAAI,CAACpY,MAAM,CAAC;UAC9B,KAAK,IAAIxG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4e,IAAI,CAAC7e,MAAM,EAAEC,CAAC,EAAE,EAAE;YAClC,IAAI8P,IAAI,GAAG8O,IAAI,CAAC5e,CAAC,CAAC;YAClB,IAAI6e,KAAK,GAAGjF,sBAAsB,CAACkF,IAAI,CAAChP,IAAI,CAAC;YAC7C,IAAIiP,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;YAC/B;YACA;YACA;YACA;YACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAAgB,EAAE;cACzC,IAAI,CAACnE,mCAAmC,CAAC,CAACta,IAAI,CAAC,IAAI,EAAEye,OAAO,CAAC;YACjE;UACJ;UACA;UACA,IAAI,CAACnE,mCAAmC,CAAC,CAACta,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;QAC1E,CAAC,MACI;UACD,IAAI+Z,YAAY,IAAIA,YAAY,CAACgE,iBAAiB,EAAE;YAChDlO,SAAS,GAAGkK,YAAY,CAACgE,iBAAiB,CAAClO,SAAS,CAAC;UACzD;UACA,IAAI+M,gBAAgB,GAAGxD,oBAAoB,CAACvJ,SAAS,CAAC;UACtD,IAAI+M,gBAAgB,EAAE;YAClB,IAAIC,eAAe,GAAGD,gBAAgB,CAAC3P,SAAS,CAAC;YACjD,IAAIyR,sBAAsB,GAAG9B,gBAAgB,CAAC5P,QAAQ,CAAC;YACvD,IAAIkO,KAAK,GAAGhV,MAAM,CAAC2W,eAAe,CAAC;YACnC,IAAI8B,YAAY,GAAGzY,MAAM,CAACwY,sBAAsB,CAAC;YACjD,IAAIxD,KAAK,EAAE;cACP,IAAI0D,WAAW,GAAG1D,KAAK,CAACnb,KAAK,EAAE;cAC/B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkf,WAAW,CAACnf,MAAM,EAAEC,CAAC,EAAE,EAAE;gBACzC,IAAI+D,IAAI,GAAGmb,WAAW,CAAClf,CAAC,CAAC;gBACzB,IAAIsG,QAAQ,GAAGvC,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACZ,QAAQ;gBAC5E,IAAI,CAACqX,qBAAqB,CAAC,CAACla,IAAI,CAAC,IAAI,EAAE6P,SAAS,EAAE7J,QAAQ,EAAEvC,IAAI,CAAC4F,OAAO,CAAC;cAC7E;YACJ;YACA,IAAIsV,YAAY,EAAE;cACd,IAAIC,WAAW,GAAGD,YAAY,CAAC5e,KAAK,EAAE;cACtC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkf,WAAW,CAACnf,MAAM,EAAEC,CAAC,EAAE,EAAE;gBACzC,IAAI+D,IAAI,GAAGmb,WAAW,CAAClf,CAAC,CAAC;gBACzB,IAAIsG,QAAQ,GAAGvC,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACoV,gBAAgB,GAAGpV,IAAI,CAACZ,QAAQ;gBAC5E,IAAI,CAACqX,qBAAqB,CAAC,CAACla,IAAI,CAAC,IAAI,EAAE6P,SAAS,EAAE7J,QAAQ,EAAEvC,IAAI,CAAC4F,OAAO,CAAC;cAC7E;YACJ;UACJ;QACJ;QACA,IAAIuS,YAAY,EAAE;UACd,OAAO,IAAI;QACf;MACJ,CAAC;MACD;MACAxP,qBAAqB,CAACuE,KAAK,CAACqJ,kBAAkB,CAAC,EAAE+B,sBAAsB,CAAC;MACxE3P,qBAAqB,CAACuE,KAAK,CAACuJ,qBAAqB,CAAC,EAAE8B,yBAAyB,CAAC;MAC9E,IAAIE,wBAAwB,EAAE;QAC1B9P,qBAAqB,CAACuE,KAAK,CAAC2J,mCAAmC,CAAC,EAAE4B,wBAAwB,CAAC;MAC/F;MACA,IAAID,eAAe,EAAE;QACjB7P,qBAAqB,CAACuE,KAAK,CAACyJ,wBAAwB,CAAC,EAAE6B,eAAe,CAAC;MAC3E;MACA,OAAO,IAAI;IACf;IACA,IAAI4C,OAAO,GAAG,EAAE;IAChB,KAAK,IAAInf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoa,IAAI,CAACra,MAAM,EAAEC,CAAC,EAAE,EAAE;MAClCmf,OAAO,CAACnf,CAAC,CAAC,GAAG6b,uBAAuB,CAACzB,IAAI,CAACpa,CAAC,CAAC,EAAEqa,YAAY,CAAC;IAC/D;IACA,OAAO8E,OAAO;EAClB;EACA,SAASR,cAAc,CAACnY,MAAM,EAAE2J,SAAS,EAAE;IACvC,IAAI,CAACA,SAAS,EAAE;MACZ,IAAIiP,UAAU,GAAG,EAAE;MACnB,KAAK,IAAItP,IAAI,IAAItJ,MAAM,EAAE;QACrB,IAAIqY,KAAK,GAAGjF,sBAAsB,CAACkF,IAAI,CAAChP,IAAI,CAAC;QAC7C,IAAIiP,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;QAC/B,IAAIE,OAAO,KAAK,CAAC5O,SAAS,IAAI4O,OAAO,KAAK5O,SAAS,CAAC,EAAE;UAClD,IAAIqL,KAAK,GAAGhV,MAAM,CAACsJ,IAAI,CAAC;UACxB,IAAI0L,KAAK,EAAE;YACP,KAAK,IAAIxb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwb,KAAK,CAACzb,MAAM,EAAEC,CAAC,EAAE,EAAE;cACnCof,UAAU,CAACjW,IAAI,CAACqS,KAAK,CAACxb,CAAC,CAAC,CAAC;YAC7B;UACJ;QACJ;MACJ;MACA,OAAOof,UAAU;IACrB;IACA,IAAIjC,eAAe,GAAGzD,oBAAoB,CAACvJ,SAAS,CAAC;IACrD,IAAI,CAACgN,eAAe,EAAE;MAClBpD,iBAAiB,CAAC5J,SAAS,CAAC;MAC5BgN,eAAe,GAAGzD,oBAAoB,CAACvJ,SAAS,CAAC;IACrD;IACA,IAAIkP,iBAAiB,GAAG7Y,MAAM,CAAC2W,eAAe,CAAC5P,SAAS,CAAC,CAAC;IAC1D,IAAI+R,gBAAgB,GAAG9Y,MAAM,CAAC2W,eAAe,CAAC7P,QAAQ,CAAC,CAAC;IACxD,IAAI,CAAC+R,iBAAiB,EAAE;MACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACjf,KAAK,EAAE,GAAG,EAAE;IAC3D,CAAC,MACI;MACD,OAAOif,gBAAgB,GAAGD,iBAAiB,CAAC9e,MAAM,CAAC+e,gBAAgB,CAAC,GAChED,iBAAiB,CAAChf,KAAK,EAAE;IACjC;EACJ;EACA,SAAS2L,mBAAmB,CAACrL,MAAM,EAAEsR,GAAG,EAAE;IACtC,IAAIsN,KAAK,GAAG5e,MAAM,CAAC,OAAO,CAAC;IAC3B,IAAI4e,KAAK,IAAIA,KAAK,CAACnf,SAAS,EAAE;MAC1B6R,GAAG,CAACrG,WAAW,CAAC2T,KAAK,CAACnf,SAAS,EAAE,0BAA0B,EAAE,UAAUkG,QAAQ,EAAE;QAAE,OAAO,UAAUuD,IAAI,EAAEE,IAAI,EAAE;UAC5GF,IAAI,CAACiQ,4BAA4B,CAAC,GAAG,IAAI;UACzC;UACA;UACA;UACAxT,QAAQ,IAAIA,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;QAC1C,CAAC;MAAE,CAAC,CAAC;IACT;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS6C,cAAc,CAACqF,GAAG,EAAEzL,MAAM,EAAEgZ,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAE;IAChE,IAAIpU,MAAM,GAAGjK,IAAI,CAACH,UAAU,CAACue,MAAM,CAAC;IACpC,IAAIjZ,MAAM,CAAC8E,MAAM,CAAC,EAAE;MAChB;IACJ;IACA,IAAIqU,cAAc,GAAGnZ,MAAM,CAAC8E,MAAM,CAAC,GAAG9E,MAAM,CAACiZ,MAAM,CAAC;IACpDjZ,MAAM,CAACiZ,MAAM,CAAC,GAAG,UAAU3e,IAAI,EAAE8e,IAAI,EAAEjW,OAAO,EAAE;MAC5C,IAAIiW,IAAI,IAAIA,IAAI,CAACxf,SAAS,EAAE;QACxBsf,SAAS,CAACG,OAAO,CAAC,UAAU1c,QAAQ,EAAE;UAClC,IAAIC,MAAM,GAAG,EAAE,CAAC7C,MAAM,CAACif,UAAU,EAAE,GAAG,CAAC,CAACjf,MAAM,CAACkf,MAAM,EAAE,IAAI,CAAC,GAAGtc,QAAQ;UACvE,IAAI/C,SAAS,GAAGwf,IAAI,CAACxf,SAAS;UAC9B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI;YACA,IAAIA,SAAS,CAACwC,cAAc,CAACO,QAAQ,CAAC,EAAE;cACpC,IAAI2c,UAAU,GAAG7N,GAAG,CAAC7F,8BAA8B,CAAChM,SAAS,EAAE+C,QAAQ,CAAC;cACxE,IAAI2c,UAAU,IAAIA,UAAU,CAACzW,KAAK,EAAE;gBAChCyW,UAAU,CAACzW,KAAK,GAAG4I,GAAG,CAACzF,mBAAmB,CAACsT,UAAU,CAACzW,KAAK,EAAEjG,MAAM,CAAC;gBACpE6O,GAAG,CAACtF,iBAAiB,CAACiT,IAAI,CAACxf,SAAS,EAAE+C,QAAQ,EAAE2c,UAAU,CAAC;cAC/D,CAAC,MACI,IAAI1f,SAAS,CAAC+C,QAAQ,CAAC,EAAE;gBAC1B/C,SAAS,CAAC+C,QAAQ,CAAC,GAAG8O,GAAG,CAACzF,mBAAmB,CAACpM,SAAS,CAAC+C,QAAQ,CAAC,EAAEC,MAAM,CAAC;cAC9E;YACJ,CAAC,MACI,IAAIhD,SAAS,CAAC+C,QAAQ,CAAC,EAAE;cAC1B/C,SAAS,CAAC+C,QAAQ,CAAC,GAAG8O,GAAG,CAACzF,mBAAmB,CAACpM,SAAS,CAAC+C,QAAQ,CAAC,EAAEC,MAAM,CAAC;YAC9E;UACJ,CAAC,CACD,OAAO2U,EAAE,EAAE;YACP;YACA;UAAA;QAER,CAAC,CAAC;MACN;MACA,OAAO4H,cAAc,CAACrf,IAAI,CAACkG,MAAM,EAAE1F,IAAI,EAAE8e,IAAI,EAAEjW,OAAO,CAAC;IAC3D,CAAC;IACDsI,GAAG,CAACvF,qBAAqB,CAAClG,MAAM,CAACiZ,MAAM,CAAC,EAAEE,cAAc,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASlT,gBAAgB,CAACjG,MAAM,EAAEiK,YAAY,EAAEsP,gBAAgB,EAAE;IAC9D,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAAChgB,MAAM,KAAK,CAAC,EAAE;MACpD,OAAO0Q,YAAY;IACvB;IACA,IAAIuP,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAC,UAAUC,EAAE,EAAE;MAAE,OAAOA,EAAE,CAAC1Z,MAAM,KAAKA,MAAM;IAAE,CAAC,CAAC;IACjF,IAAI,CAACwZ,GAAG,IAAIA,GAAG,CAACjgB,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO0Q,YAAY;IACvB;IACA,IAAI0P,sBAAsB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACD,gBAAgB;IACpD,OAAOtP,YAAY,CAACwP,MAAM,CAAC,UAAUG,EAAE,EAAE;MAAE,OAAOD,sBAAsB,CAACnO,OAAO,CAACoO,EAAE,CAAC,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC;EACnG;EACA,SAASC,uBAAuB,CAAC7Z,MAAM,EAAEiK,YAAY,EAAEsP,gBAAgB,EAAE3f,SAAS,EAAE;IAChF;IACA;IACA,IAAI,CAACoG,MAAM,EAAE;MACT;IACJ;IACA,IAAI8Z,kBAAkB,GAAG7T,gBAAgB,CAACjG,MAAM,EAAEiK,YAAY,EAAEsP,gBAAgB,CAAC;IACjFpU,iBAAiB,CAACnF,MAAM,EAAE8Z,kBAAkB,EAAElgB,SAAS,CAAC;EAC5D;EACA;AACJ;AACA;AACA;EACI,SAASmgB,eAAe,CAAC/Z,MAAM,EAAE;IAC7B,OAAOxE,MAAM,CAACwe,mBAAmB,CAACha,MAAM,CAAC,CACpCyZ,MAAM,CAAC,UAAUnf,IAAI,EAAE;MAAE,OAAOA,IAAI,CAAC2f,UAAU,CAAC,IAAI,CAAC,IAAI3f,IAAI,CAACf,MAAM,GAAG,CAAC;IAAE,CAAC,CAAC,CAC5E2gB,GAAG,CAAC,UAAU5f,IAAI,EAAE;MAAE,OAAOA,IAAI,CAAC6f,SAAS,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAC3D;EACA,SAASC,uBAAuB,CAAC3O,GAAG,EAAEpE,OAAO,EAAE;IAC3C,IAAIe,MAAM,IAAI,CAACG,KAAK,EAAE;MAClB;IACJ;IACA,IAAI1N,IAAI,CAAC4Q,GAAG,CAAC3G,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;MACjC;MACA;IACJ;IACA,IAAIyU,gBAAgB,GAAGlS,OAAO,CAAC,6BAA6B,CAAC;IAC7D;IACA,IAAIgT,YAAY,GAAG,EAAE;IACrB,IAAI/R,SAAS,EAAE;MACX,IAAIgS,gBAAgB,GAAGjU,MAAM;MAC7BgU,YAAY,GAAGA,YAAY,CAACtgB,MAAM,CAAC,CAC/B,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EACzF,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,QAAQ,CACjG,CAAC;MACF,IAAIwgB,qBAAqB,GAAGnP,IAAI,EAAE,GAAG,CAAC;QAAEpL,MAAM,EAAEsa,gBAAgB;QAAEf,gBAAgB,EAAE,CAAC,OAAO;MAAE,CAAC,CAAC,GAAG,EAAE;MACrG;MACA;MACAM,uBAAuB,CAACS,gBAAgB,EAAEP,eAAe,CAACO,gBAAgB,CAAC,EAAEf,gBAAgB,GAAGA,gBAAgB,CAACxf,MAAM,CAACwgB,qBAAqB,CAAC,GAAGhB,gBAAgB,EAAEhT,oBAAoB,CAAC+T,gBAAgB,CAAC,CAAC;IAC9M;IACAD,YAAY,GAAGA,YAAY,CAACtgB,MAAM,CAAC,CAC/B,gBAAgB,EAAE,2BAA2B,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,EAC3F,aAAa,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,CAC5D,CAAC;IACF,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6gB,YAAY,CAAC9gB,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAIwG,MAAM,GAAGqH,OAAO,CAACgT,YAAY,CAAC7gB,CAAC,CAAC,CAAC;MACrCwG,MAAM,IAAIA,MAAM,CAACpG,SAAS,IACtBigB,uBAAuB,CAAC7Z,MAAM,CAACpG,SAAS,EAAEmgB,eAAe,CAAC/Z,MAAM,CAACpG,SAAS,CAAC,EAAE2f,gBAAgB,CAAC;IACtG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1e,IAAI,CAACoB,YAAY,CAAC,MAAM,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IACnD;IACA;IACA,IAAI+O,UAAU,GAAGT,eAAe,CAAC5f,MAAM,CAAC;IACxCsR,GAAG,CAACtG,iBAAiB,GAAGA,iBAAiB;IACzCsG,GAAG,CAACrG,WAAW,GAAGA,WAAW;IAC7BqG,GAAG,CAACpG,aAAa,GAAGA,aAAa;IACjCoG,GAAG,CAAClG,cAAc,GAAGA,cAAc;IACnC;IACA;IACA;IACA;IACA;IACA;IACA,IAAIkV,0BAA0B,GAAG5f,IAAI,CAACH,UAAU,CAAC,qBAAqB,CAAC;IACvE,IAAIggB,uBAAuB,GAAG7f,IAAI,CAACH,UAAU,CAAC,kBAAkB,CAAC;IACjE,IAAIP,MAAM,CAACugB,uBAAuB,CAAC,EAAE;MACjCvgB,MAAM,CAACsgB,0BAA0B,CAAC,GAAGtgB,MAAM,CAACugB,uBAAuB,CAAC;IACxE;IACA,IAAIvgB,MAAM,CAACsgB,0BAA0B,CAAC,EAAE;MACpC5f,IAAI,CAAC4f,0BAA0B,CAAC,GAAG5f,IAAI,CAAC6f,uBAAuB,CAAC,GAC5DvgB,MAAM,CAACsgB,0BAA0B,CAAC;IAC1C;IACAhP,GAAG,CAACjG,mBAAmB,GAAGA,mBAAmB;IAC7CiG,GAAG,CAACvG,gBAAgB,GAAGA,gBAAgB;IACvCuG,GAAG,CAAChG,UAAU,GAAGA,UAAU;IAC3BgG,GAAG,CAAC9F,oBAAoB,GAAGA,oBAAoB;IAC/C8F,GAAG,CAAC7F,8BAA8B,GAAGA,8BAA8B;IACnE6F,GAAG,CAAC5F,YAAY,GAAGA,YAAY;IAC/B4F,GAAG,CAAC3F,UAAU,GAAGA,UAAU;IAC3B2F,GAAG,CAAC1F,UAAU,GAAGA,UAAU;IAC3B0F,GAAG,CAACzF,mBAAmB,GAAGA,mBAAmB;IAC7CyF,GAAG,CAACxF,gBAAgB,GAAGA,gBAAgB;IACvCwF,GAAG,CAACvF,qBAAqB,GAAGA,qBAAqB;IACjDuF,GAAG,CAACtF,iBAAiB,GAAG3K,MAAM,CAACC,cAAc;IAC7CgQ,GAAG,CAACrF,cAAc,GAAGA,cAAc;IACnCqF,GAAG,CAAC/F,gBAAgB,GAAG,YAAY;MAAE,OAAQ;QACzCyN,aAAa,EAAEA,aAAa;QAC5BD,oBAAoB,EAAEA,oBAAoB;QAC1CsH,UAAU,EAAEA,UAAU;QACtBlS,SAAS,EAAEA,SAAS;QACpBC,KAAK,EAAEA,KAAK;QACZH,MAAM,EAAEA,MAAM;QACdtB,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA,SAAS;QACpBC,kBAAkB,EAAEA,kBAAkB;QACtCN,sBAAsB,EAAEA,sBAAsB;QAC9CC,yBAAyB,EAAEA;MAC/B,CAAC;IAAG,CAAC;EACT,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;EACI,IAAIgU,UAAU;EACd,IAAIC,eAAe;EACnB,IAAIC,yBAAyB;EAC7B,IAAIC,OAAO;EACX,IAAIC,kBAAkB;EACtB,SAASC,aAAa,GAAG;IACrBL,UAAU,GAAG9f,IAAI,CAACH,UAAU;IAC5BkgB,eAAe,GAAGpf,MAAM,CAACmf,UAAU,CAAC,gBAAgB,CAAC,CAAC,GAAGnf,MAAM,CAACC,cAAc;IAC9Eof,yBAAyB,GAAGrf,MAAM,CAACmf,UAAU,CAAC,0BAA0B,CAAC,CAAC,GACtEnf,MAAM,CAAC8K,wBAAwB;IACnCwU,OAAO,GAAGtf,MAAM,CAACiL,MAAM;IACvBsU,kBAAkB,GAAGJ,UAAU,CAAC,iBAAiB,CAAC;IAClDnf,MAAM,CAACC,cAAc,GAAG,UAAU4N,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;MAC/C,IAAI0R,gBAAgB,CAAC5R,GAAG,EAAEC,IAAI,CAAC,EAAE;QAC7B,MAAM,IAAI+E,SAAS,CAAC,wCAAwC,GAAG/E,IAAI,GAAG,QAAQ,GAAGD,GAAG,CAAC;MACzF;MACA,IAAI6R,wBAAwB,GAAG3R,IAAI,CAACzN,YAAY;MAChD,IAAIwN,IAAI,KAAK,WAAW,EAAE;QACtBC,IAAI,GAAG4R,iBAAiB,CAAC9R,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAC7C;MACA,OAAO6R,kBAAkB,CAAC/R,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE2R,wBAAwB,CAAC;IACxE,CAAC;IACD1f,MAAM,CAAC6f,gBAAgB,GAAG,UAAUhS,GAAG,EAAEiS,KAAK,EAAE;MAC5C9f,MAAM,CAAC4c,IAAI,CAACkD,KAAK,CAAC,CAACjC,OAAO,CAAC,UAAU/P,IAAI,EAAE;QACvC9N,MAAM,CAACC,cAAc,CAAC4N,GAAG,EAAEC,IAAI,EAAEgS,KAAK,CAAChS,IAAI,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,KAAK,IAAIqG,EAAE,GAAG,CAAC,EAAE4L,EAAE,GAAG/f,MAAM,CAACggB,qBAAqB,CAACF,KAAK,CAAC,EAAE3L,EAAE,GAAG4L,EAAE,CAAChiB,MAAM,EAAEoW,EAAE,EAAE,EAAE;QAC7E,IAAI8L,GAAG,GAAGF,EAAE,CAAC5L,EAAE,CAAC;QAChB,IAAIpG,IAAI,GAAG/N,MAAM,CAAC8K,wBAAwB,CAACgV,KAAK,EAAEG,GAAG,CAAC;QACtD;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIlS,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC1N,UAAU,EAAE;UAC7DL,MAAM,CAACC,cAAc,CAAC4N,GAAG,EAAEoS,GAAG,EAAEH,KAAK,CAACG,GAAG,CAAC,CAAC;QAC/C;MACJ;MACA,OAAOpS,GAAG;IACd,CAAC;IACD7N,MAAM,CAACiL,MAAM,GAAG,UAAUgE,KAAK,EAAEiR,gBAAgB,EAAE;MAC/C,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,IAAI,CAAClgB,MAAM,CAACmgB,QAAQ,CAACD,gBAAgB,CAAC,EAAE;QAC5ElgB,MAAM,CAAC4c,IAAI,CAACsD,gBAAgB,CAAC,CAACrC,OAAO,CAAC,UAAU/P,IAAI,EAAE;UAClDoS,gBAAgB,CAACpS,IAAI,CAAC,GAAG6R,iBAAiB,CAAC1Q,KAAK,EAAEnB,IAAI,EAAEoS,gBAAgB,CAACpS,IAAI,CAAC,CAAC;QACnF,CAAC,CAAC;MACN;MACA,OAAOwR,OAAO,CAACrQ,KAAK,EAAEiR,gBAAgB,CAAC;IAC3C,CAAC;IACDlgB,MAAM,CAAC8K,wBAAwB,GAAG,UAAU+C,GAAG,EAAEC,IAAI,EAAE;MACnD,IAAIC,IAAI,GAAGsR,yBAAyB,CAACxR,GAAG,EAAEC,IAAI,CAAC;MAC/C,IAAIC,IAAI,IAAI0R,gBAAgB,CAAC5R,GAAG,EAAEC,IAAI,CAAC,EAAE;QACrCC,IAAI,CAACzN,YAAY,GAAG,KAAK;MAC7B;MACA,OAAOyN,IAAI;IACf,CAAC;EACL;EACA,SAASpD,iBAAiB,CAACkD,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACxC,IAAI2R,wBAAwB,GAAG3R,IAAI,CAACzN,YAAY;IAChDyN,IAAI,GAAG4R,iBAAiB,CAAC9R,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;IACzC,OAAO6R,kBAAkB,CAAC/R,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE2R,wBAAwB,CAAC;EACxE;EACA,SAASD,gBAAgB,CAAC5R,GAAG,EAAEC,IAAI,EAAE;IACjC,OAAOD,GAAG,IAAIA,GAAG,CAAC0R,kBAAkB,CAAC,IAAI1R,GAAG,CAAC0R,kBAAkB,CAAC,CAACzR,IAAI,CAAC;EAC1E;EACA,SAAS6R,iBAAiB,CAAC9R,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACxC;IACA,IAAI,CAAC/N,MAAM,CAACmgB,QAAQ,CAACpS,IAAI,CAAC,EAAE;MACxBA,IAAI,CAACzN,YAAY,GAAG,IAAI;IAC5B;IACA,IAAI,CAACyN,IAAI,CAACzN,YAAY,EAAE;MACpB;MACA,IAAI,CAACuN,GAAG,CAAC0R,kBAAkB,CAAC,IAAI,CAACvf,MAAM,CAACmgB,QAAQ,CAACtS,GAAG,CAAC,EAAE;QACnDuR,eAAe,CAACvR,GAAG,EAAE0R,kBAAkB,EAAE;UAAE/S,QAAQ,EAAE,IAAI;UAAEnF,KAAK,EAAE,CAAC;QAAE,CAAC,CAAC;MAC3E;MACA,IAAIwG,GAAG,CAAC0R,kBAAkB,CAAC,EAAE;QACzB1R,GAAG,CAAC0R,kBAAkB,CAAC,CAACzR,IAAI,CAAC,GAAG,IAAI;MACxC;IACJ;IACA,OAAOC,IAAI;EACf;EACA,SAAS6R,kBAAkB,CAAC/R,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE2R,wBAAwB,EAAE;IACnE,IAAI;MACA,OAAON,eAAe,CAACvR,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAC3C,CAAC,CACD,OAAOnM,KAAK,EAAE;MACV,IAAImM,IAAI,CAACzN,YAAY,EAAE;QACnB;QACA;QACA,IAAI,OAAOof,wBAAwB,IAAI,WAAW,EAAE;UAChD,OAAO3R,IAAI,CAACzN,YAAY;QAC5B,CAAC,MACI;UACDyN,IAAI,CAACzN,YAAY,GAAGof,wBAAwB;QAChD;QACA,IAAI;UACA,OAAON,eAAe,CAACvR,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;QAC3C,CAAC,CACD,OAAOnM,KAAK,EAAE;UACV,IAAIwe,YAAY,GAAG,KAAK;UACxB,IAAItS,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,kBAAkB,IACzDA,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,0BAA0B,EAAE;YACpE;YACA;YACA;YACAsS,YAAY,GAAG,IAAI;UACvB;UACA,IAAI,CAACA,YAAY,EAAE;YACf,MAAMxe,KAAK;UACf;UACA;UACA;UACA;UACA,IAAIye,QAAQ,GAAG,IAAI;UACnB,IAAI;YACAA,QAAQ,GAAGlQ,IAAI,CAACC,SAAS,CAACrC,IAAI,CAAC;UACnC,CAAC,CACD,OAAOnM,KAAK,EAAE;YACVye,QAAQ,GAAGtS,IAAI,CAACzF,QAAQ,EAAE;UAC9B;UACAoI,OAAO,CAAC4P,GAAG,CAAC,2BAA2B,CAAC/hB,MAAM,CAACuP,IAAI,EAAE,qBAAqB,CAAC,CAACvP,MAAM,CAAC8hB,QAAQ,EAAE,eAAe,CAAC,CAAC9hB,MAAM,CAACsP,GAAG,EAAE,8BAA8B,CAAC,CAACtP,MAAM,CAACqD,KAAK,CAAC,CAAC;QAC5K;MACJ,CAAC,MACI;QACD,MAAMA,KAAK;MACf;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS2e,sBAAsB,CAAC1U,OAAO,EAAEoE,GAAG,EAAE;IAC1C,IAAI8P,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE8U,UAAU,GAAGe,EAAE,CAACf,UAAU;MAAErH,aAAa,GAAGoI,EAAE,CAACpI,aAAa;MAAED,oBAAoB,GAAGqI,EAAE,CAACrI,oBAAoB;MAAEpM,QAAQ,GAAGyU,EAAE,CAACzU,QAAQ;MAAEC,SAAS,GAAGwU,EAAE,CAACxU,SAAS;MAAEC,kBAAkB,GAAGuU,EAAE,CAACvU,kBAAkB;IAC3O,IAAIgV,aAAa,GAAG,2aAA2a;IAC/b,IAAIC,eAAe,GAAG,+WAA+W,CAChYC,KAAK,CAAC,GAAG,CAAC;IACf,IAAIC,YAAY,GAAG,aAAa;IAChC,IAAIvI,IAAI,GAAG,EAAE;IACb,IAAIwI,KAAK,GAAG/U,OAAO,CAAC,KAAK,CAAC;IAC1B,IAAIgV,mBAAmB,GAAGL,aAAa,CAACE,KAAK,CAAC,GAAG,CAAC;IAClD,IAAIE,KAAK,EAAE;MACP;MACAxI,IAAI,GAAGyI,mBAAmB,CAACnC,GAAG,CAAC,UAAUtM,CAAC,EAAE;QAAE,OAAO,MAAM,GAAGA,CAAC,GAAG,SAAS;MAAE,CAAC,CAAC,CAAC7T,MAAM,CAACkiB,eAAe,CAAC;IAC3G,CAAC,MACI,IAAI5U,OAAO,CAAC8U,YAAY,CAAC,EAAE;MAC5BvI,IAAI,CAACjR,IAAI,CAACwZ,YAAY,CAAC;IAC3B,CAAC,MACI;MACD;MACA;MACAvI,IAAI,GAAGqI,eAAe;IAC1B;IACA,IAAIK,gBAAgB,GAAGjV,OAAO,CAAC,yBAAyB,CAAC,IAAI,KAAK;IAClE,IAAIkV,yBAAyB,GAAGlV,OAAO,CAAC,mCAAmC,CAAC,IAAI,KAAK;IACrF,IAAI8D,QAAQ,GAAGM,GAAG,CAAChG,UAAU,EAAE;IAC/B,IAAI8O,yBAAyB,GAAG,oBAAoB;IACpD,IAAIiI,gBAAgB,GAAG,0BAA0B;IACjD,IAAIC,aAAa,GAAG,8DAA8D;IAClF,IAAIC,gBAAgB,GAAG;MACnB,iBAAiB,EAAE,eAAe;MAClC,eAAe,EAAE,aAAa;MAC9B,gBAAgB,EAAE,cAAc;MAChC,gBAAgB,EAAE,cAAc;MAChC,gBAAgB,EAAE,cAAc;MAChC,eAAe,EAAE,aAAa;MAC9B,cAAc,EAAE,YAAY;MAC5B,eAAe,EAAE,aAAa;MAC9B,aAAa,EAAE;IACnB,CAAC;IACD;IACA,KAAK,IAAIljB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGghB,UAAU,CAACjhB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC,IAAImQ,SAAS,GAAG6Q,UAAU,CAAChhB,CAAC,CAAC;MAC7B,IAAIia,cAAc,GAAG9J,SAAS,GAAG5C,SAAS;MAC1C,IAAI2M,aAAa,GAAG/J,SAAS,GAAG7C,QAAQ;MACxC,IAAIhC,MAAM,GAAGkC,kBAAkB,GAAGyM,cAAc;MAChD,IAAIE,aAAa,GAAG3M,kBAAkB,GAAG0M,aAAa;MACtDR,oBAAoB,CAACvJ,SAAS,CAAC,GAAG,CAAC,CAAC;MACpCuJ,oBAAoB,CAACvJ,SAAS,CAAC,CAAC5C,SAAS,CAAC,GAAGjC,MAAM;MACnDoO,oBAAoB,CAACvJ,SAAS,CAAC,CAAC7C,QAAQ,CAAC,GAAG6M,aAAa;IAC7D;IACA;IACA,KAAK,IAAIna,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6iB,mBAAmB,CAAC9iB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,IAAIwG,MAAM,GAAGqc,mBAAmB,CAAC7iB,CAAC,CAAC;MACnC,IAAImjB,OAAO,GAAGxJ,aAAa,CAACnT,MAAM,CAAC,GAAG,CAAC,CAAC;MACxC,KAAK,IAAIkK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsQ,UAAU,CAACjhB,MAAM,EAAE2Q,CAAC,EAAE,EAAE;QACxC,IAAIP,SAAS,GAAG6Q,UAAU,CAACtQ,CAAC,CAAC;QAC7ByS,OAAO,CAAChT,SAAS,CAAC,GAAG3J,MAAM,GAAGuU,yBAAyB,GAAG5K,SAAS;MACvE;IACJ;IACA,IAAIiT,sBAAsB,GAAG,UAAUzD,cAAc,EAAErZ,QAAQ,EAAEE,MAAM,EAAEuD,IAAI,EAAE;MAC3E,IAAI,CAAC+Y,gBAAgB,IAAInR,QAAQ,EAAE;QAC/B,IAAIoR,yBAAyB,EAAE;UAC3B,IAAI;YACA,IAAIM,UAAU,GAAG/c,QAAQ,CAACgE,QAAQ,EAAE;YACpC,IAAK+Y,UAAU,KAAKL,gBAAgB,IAAIK,UAAU,IAAIJ,aAAa,EAAG;cAClEtD,cAAc,CAAC1W,KAAK,CAACzC,MAAM,EAAEuD,IAAI,CAAC;cAClC,OAAO,KAAK;YAChB;UACJ,CAAC,CACD,OAAOnG,KAAK,EAAE;YACV+b,cAAc,CAAC1W,KAAK,CAACzC,MAAM,EAAEuD,IAAI,CAAC;YAClC,OAAO,KAAK;UAChB;QACJ,CAAC,MACI;UACD,IAAIsZ,UAAU,GAAG/c,QAAQ,CAACgE,QAAQ,EAAE;UACpC,IAAK+Y,UAAU,KAAKL,gBAAgB,IAAIK,UAAU,IAAIJ,aAAa,EAAG;YAClEtD,cAAc,CAAC1W,KAAK,CAACzC,MAAM,EAAEuD,IAAI,CAAC;YAClC,OAAO,KAAK;UAChB;QACJ;MACJ,CAAC,MACI,IAAIgZ,yBAAyB,EAAE;QAChC,IAAI;UACAzc,QAAQ,CAACgE,QAAQ,EAAE;QACvB,CAAC,CACD,OAAO1G,KAAK,EAAE;UACV+b,cAAc,CAAC1W,KAAK,CAACzC,MAAM,EAAEuD,IAAI,CAAC;UAClC,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf,CAAC;IACD,IAAIuZ,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAItjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoa,IAAI,CAACra,MAAM,EAAEC,CAAC,EAAE,EAAE;MAClC,IAAImE,IAAI,GAAG0J,OAAO,CAACuM,IAAI,CAACpa,CAAC,CAAC,CAAC;MAC3BsjB,QAAQ,CAACna,IAAI,CAAChF,IAAI,IAAIA,IAAI,CAAC/D,SAAS,CAAC;IACzC;IACA;IACA;IACA6R,GAAG,CAACvG,gBAAgB,CAACmC,OAAO,EAAEoE,GAAG,EAAEqR,QAAQ,EAAE;MACzCtH,EAAE,EAAEoH,sBAAsB;MAC1B/E,iBAAiB,EAAE,UAAUlO,SAAS,EAAE;QACpC,IAAIoT,gBAAgB,GAAGL,gBAAgB,CAAC/S,SAAS,CAAC;QAClD,OAAOoT,gBAAgB,IAAIpT,SAAS;MACxC;IACJ,CAAC,CAAC;IACF9O,IAAI,CAAC4Q,GAAG,CAAC3G,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAACuC,OAAO,CAAC8U,YAAY,CAAC;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA,SAAS1Z,KAAK,CAACgJ,GAAG,EAAEpE,OAAO,EAAE;IACzB,IAAIkU,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAEgB,sBAAsB,GAAG6U,EAAE,CAAC7U,sBAAsB;MAAEC,yBAAyB,GAAG4U,EAAE,CAAC5U,yBAAyB;IAC7I,IAAIqW,EAAE,GAAG3V,OAAO,CAAC4V,SAAS;IAC1B;IACA;IACA,IAAI,CAAC5V,OAAO,CAAC6V,WAAW,EAAE;MACtBzR,GAAG,CAACvG,gBAAgB,CAACmC,OAAO,EAAEoE,GAAG,EAAE,CAACuR,EAAE,CAACpjB,SAAS,CAAC,CAAC;IACtD;IACAyN,OAAO,CAAC4V,SAAS,GAAG,UAAUE,CAAC,EAAEC,CAAC,EAAE;MAChC,IAAIC,MAAM,GAAG/jB,SAAS,CAACC,MAAM,GAAG,CAAC,GAAG,IAAIyjB,EAAE,CAACG,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAIJ,EAAE,CAACG,CAAC,CAAC;MAC5D,IAAIG,WAAW;MACf,IAAIC,gBAAgB;MACpB;MACA,IAAIC,aAAa,GAAG/R,GAAG,CAAC7F,8BAA8B,CAACyX,MAAM,EAAE,WAAW,CAAC;MAC3E,IAAIG,aAAa,IAAIA,aAAa,CAAC1hB,YAAY,KAAK,KAAK,EAAE;QACvDwhB,WAAW,GAAG7R,GAAG,CAAC5F,YAAY,CAACwX,MAAM,CAAC;QACtC;QACA;QACA;QACAE,gBAAgB,GAAGF,MAAM;QACzB,CAAC3W,sBAAsB,EAAEC,yBAAyB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC0S,OAAO,CAAC,UAAUoE,QAAQ,EAAE;UAC7FH,WAAW,CAACG,QAAQ,CAAC,GAAG,YAAY;YAChC,IAAIla,IAAI,GAAGkI,GAAG,CAAC3F,UAAU,CAAChM,IAAI,CAACR,SAAS,CAAC;YACzC,IAAImkB,QAAQ,KAAK/W,sBAAsB,IAAI+W,QAAQ,KAAK9W,yBAAyB,EAAE;cAC/E,IAAIgD,SAAS,GAAGpG,IAAI,CAAChK,MAAM,GAAG,CAAC,GAAGgK,IAAI,CAAC,CAAC,CAAC,GAAGhF,SAAS;cACrD,IAAIoL,SAAS,EAAE;gBACX,IAAI+T,cAAc,GAAG7iB,IAAI,CAACH,UAAU,CAAC,aAAa,GAAGiP,SAAS,CAAC;gBAC/D0T,MAAM,CAACK,cAAc,CAAC,GAAGJ,WAAW,CAACI,cAAc,CAAC;cACxD;YACJ;YACA,OAAOL,MAAM,CAACI,QAAQ,CAAC,CAAChb,KAAK,CAAC4a,MAAM,EAAE9Z,IAAI,CAAC;UAC/C,CAAC;QACL,CAAC,CAAC;MACN,CAAC,MACI;QACD;QACA+Z,WAAW,GAAGD,MAAM;MACxB;MACA5R,GAAG,CAACtG,iBAAiB,CAACmY,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,EAAEC,gBAAgB,CAAC;MAC3F,OAAOD,WAAW;IACtB,CAAC;IACD,IAAIK,eAAe,GAAGtW,OAAO,CAAC,WAAW,CAAC;IAC1C,KAAK,IAAIiC,IAAI,IAAI0T,EAAE,EAAE;MACjBW,eAAe,CAACrU,IAAI,CAAC,GAAG0T,EAAE,CAAC1T,IAAI,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASsU,6BAA6B,CAACnS,GAAG,EAAEpE,OAAO,EAAE;IACjD,IAAIkU,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE0C,MAAM,GAAGmT,EAAE,CAACnT,MAAM;MAAEG,KAAK,GAAGgT,EAAE,CAAChT,KAAK;IACrE,IAAIH,MAAM,IAAI,CAACG,KAAK,EAAE;MAClB;IACJ;IACA,IAAI,CAACsV,6BAA6B,CAACpS,GAAG,EAAEpE,OAAO,CAAC,EAAE;MAC9C,IAAIyW,iBAAiB,GAAG,OAAOb,SAAS,KAAK,WAAW;MACxD;MACAc,6BAA6B,CAACtS,GAAG,CAAC;MAClCA,GAAG,CAAC1F,UAAU,CAAC,gBAAgB,CAAC;MAChC,IAAI+X,iBAAiB,EAAE;QACnBrb,KAAK,CAACgJ,GAAG,EAAEpE,OAAO,CAAC;MACvB;MACAxM,IAAI,CAAC4Q,GAAG,CAAC3G,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI;IAC1C;EACJ;EACA,SAAS+Y,6BAA6B,CAACpS,GAAG,EAAEpE,OAAO,EAAE;IACjD,IAAIkU,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE4C,SAAS,GAAGiT,EAAE,CAACjT,SAAS;MAAEC,KAAK,GAAGgT,EAAE,CAAChT,KAAK;IAC3E,IAAI,CAACD,SAAS,IAAIC,KAAK,KACnB,CAACkD,GAAG,CAAC7F,8BAA8B,CAACoY,WAAW,CAACpkB,SAAS,EAAE,SAAS,CAAC,IACrE,OAAOqkB,OAAO,KAAK,WAAW,EAAE;MAChC;MACA;MACA,IAAI1U,IAAI,GAAGkC,GAAG,CAAC7F,8BAA8B,CAACqY,OAAO,CAACrkB,SAAS,EAAE,SAAS,CAAC;MAC3E,IAAI2P,IAAI,IAAI,CAACA,IAAI,CAACzN,YAAY,EAC1B,OAAO,KAAK;MAChB;MACA;MACA,IAAIyN,IAAI,EAAE;QACNkC,GAAG,CAAC9F,oBAAoB,CAACsY,OAAO,CAACrkB,SAAS,EAAE,SAAS,EAAE;UACnDiC,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBJ,GAAG,EAAE,YAAY;YACb,OAAO,IAAI;UACf;QACJ,CAAC,CAAC;QACF,IAAIwiB,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACvC,IAAIvV,MAAM,GAAG,CAAC,CAACqV,GAAG,CAACG,OAAO;QAC1B5S,GAAG,CAAC9F,oBAAoB,CAACsY,OAAO,CAACrkB,SAAS,EAAE,SAAS,EAAE2P,IAAI,CAAC;QAC5D,OAAOV,MAAM;MACjB;IACJ;IACA,IAAIyV,cAAc,GAAGjX,OAAO,CAAC,gBAAgB,CAAC;IAC9C,IAAI,CAACiX,cAAc,EAAE;MACjB;MACA,OAAO,KAAK;IAChB;IACA,IAAIC,qBAAqB,GAAG,oBAAoB;IAChD,IAAIC,uBAAuB,GAAGF,cAAc,CAAC1kB,SAAS;IACtD,IAAI6kB,OAAO,GAAGhT,GAAG,CAAC7F,8BAA8B,CAAC4Y,uBAAuB,EAAED,qBAAqB,CAAC;IAChG;IACA;IACA;IACA;IACA;IACA;IACA,IAAIE,OAAO,EAAE;MACThT,GAAG,CAAC9F,oBAAoB,CAAC6Y,uBAAuB,EAAED,qBAAqB,EAAE;QACrE1iB,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBJ,GAAG,EAAE,YAAY;UACb,OAAO,IAAI;QACf;MACJ,CAAC,CAAC;MACF,IAAIgjB,GAAG,GAAG,IAAIJ,cAAc,EAAE;MAC9B,IAAIzV,MAAM,GAAG,CAAC,CAAC6V,GAAG,CAACC,kBAAkB;MACrC;MACAlT,GAAG,CAAC9F,oBAAoB,CAAC6Y,uBAAuB,EAAED,qBAAqB,EAAEE,OAAO,IAAI,CAAC,CAAC,CAAC;MACvF,OAAO5V,MAAM;IACjB,CAAC,MACI;MACD,IAAI+V,gCAAgC,GAAGnT,GAAG,CAAC3G,MAAM,CAAC,MAAM,CAAC;MACzD2G,GAAG,CAAC9F,oBAAoB,CAAC6Y,uBAAuB,EAAED,qBAAqB,EAAE;QACrE1iB,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBJ,GAAG,EAAE,YAAY;UACb,OAAO,IAAI,CAACkjB,gCAAgC,CAAC;QACjD,CAAC;QACD3W,GAAG,EAAE,UAAUpF,KAAK,EAAE;UAClB,IAAI,CAAC+b,gCAAgC,CAAC,GAAG/b,KAAK;QAClD;MACJ,CAAC,CAAC;MACF,IAAI6b,GAAG,GAAG,IAAIJ,cAAc,EAAE;MAC9B,IAAIO,UAAU,GAAG,YAAY,CAAE,CAAC;MAChCH,GAAG,CAACC,kBAAkB,GAAGE,UAAU;MACnC,IAAIhW,MAAM,GAAG6V,GAAG,CAACE,gCAAgC,CAAC,KAAKC,UAAU;MACjEH,GAAG,CAACC,kBAAkB,GAAG,IAAI;MAC7B,OAAO9V,MAAM;IACjB;EACJ;EACA,IAAIiW,6BAA6B,GAAG,CAChC,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,oBAAoB,EACpB,UAAU,EACV,aAAa,EACb,MAAM,EACN,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,QAAQ,EACR,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,WAAW,EACX,OAAO,EACP,OAAO,EACP,aAAa,EACb,YAAY,EACZ,UAAU,EACV,MAAM,EACN,SAAS,EACT,WAAW,EACX,UAAU,EACV,WAAW,EACX,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,SAAS,EACT,UAAU,EACV,mBAAmB,EACnB,OAAO,EACP,SAAS,EACT,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,oBAAoB,EACpB,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACV,WAAW,EACX,SAAS,EACT,YAAY,EACZ,mBAAmB,EACnB,OAAO,EACP,MAAM,EACN,SAAS,EACT,eAAe,EACf,aAAa,EACb,cAAc,EACd,cAAc,EACd,mBAAmB,EACnB,sBAAsB,EACtB,2BAA2B,EAC3B,kBAAkB,EAClB,qBAAqB,EACrB,wBAAwB,EACxB,aAAa,EACb,UAAU,EACV,aAAa,EACb,WAAW,EACX,UAAU,EACV,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,iBAAiB,EACjB,aAAa,EACb,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,cAAc,EACd,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,kBAAkB,EAClB,eAAe,EACf,SAAS,EACT,OAAO,CACV;EACD,IAAIC,kBAAkB,GAAG,CACrB,oBAAoB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,kBAAkB,EAC7F,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,iBAAiB,EACxF,oBAAoB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,kBAAkB,EACtF,kBAAkB,EAAE,QAAQ,CAC/B;EACD,IAAIC,gBAAgB,GAAG,CACnB,2BAA2B,EAC3B,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,aAAa,EACb,cAAc,EACd,mBAAmB,EACnB,2BAA2B,EAC3B,iBAAiB,EACjB,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,UAAU,EACV,UAAU,EACV,UAAU,EACV,kBAAkB,EAClB,SAAS,EACT,oBAAoB,EACpB,QAAQ,EACR,eAAe,EACf,oBAAoB,EACpB,uBAAuB,EACvB,wBAAwB,CAC3B;EACD,IAAIC,qBAAqB,GAAG,CACxB,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EACxF,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,iBAAiB,EAAE,oBAAoB,EACpF,0BAA0B,EAAE,sBAAsB,EAAE,qBAAqB,CAC5E;EACD,IAAIC,mBAAmB,GAAG,CACtB,UAAU,EACV,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,YAAY,EACZ,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,cAAc,EACd,gBAAgB,EAChB,aAAa,EACb,MAAM,EACN,SAAS,EACT,WAAW,EACX,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,UAAU,EACV,SAAS,EACT,YAAY,EACZ,cAAc,EACd,SAAS,EACT,yBAAyB,EACzB,YAAY,EACZ,MAAM,EACN,eAAe,EACf,4BAA4B,EAC5B,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,gBAAgB,EAChB,sBAAsB,EACtB,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,cAAc,EACd,eAAe,EACf,aAAa,EACb,YAAY,EACZ,+BAA+B,EAC/B,kBAAkB,EAClB,MAAM,EACN,eAAe,CAClB;EACD,IAAIC,eAAe,GAAG,CAAC,sBAAsB,EAAE,kBAAkB,EAAE,2BAA2B,CAAC;EAC/F,IAAIC,cAAc,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC;EAC1D,IAAIC,gBAAgB,GAAG,CAAC,QAAQ,CAAC;EACjC,IAAI7E,UAAU,GAAGthB,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAE,EAAE4lB,6BAA6B,EAAE,IAAI,CAAC,EAAEK,eAAe,EAAE,IAAI,CAAC,EAAEC,cAAc,EAAE,IAAI,CAAC,EAAEC,gBAAgB,EAAE,IAAI,CAAC,EAAEN,kBAAkB,EAAE,IAAI,CAAC,EAAEC,gBAAgB,EAAE,IAAI,CAAC,EAAEC,qBAAqB,EAAE,IAAI,CAAC,EAAEC,mBAAmB,EAAE,IAAI,CAAC;EAC/V;EACA;EACA;EACA,SAASnB,6BAA6B,CAACtS,GAAG,EAAE;IACxC,IAAI6T,UAAU,GAAG7T,GAAG,CAAC3G,MAAM,CAAC,SAAS,CAAC;IACtC,IAAIya,OAAO,GAAG,UAAU/lB,CAAC,EAAE;MACvB,IAAIgmB,QAAQ,GAAGhF,UAAU,CAAChhB,CAAC,CAAC;MAC5B,IAAIimB,UAAU,GAAG,IAAI,GAAGD,QAAQ;MAChCnc,IAAI,CAAC0G,gBAAgB,CAACyV,QAAQ,EAAE,UAAU9W,KAAK,EAAE;QAC7C,IAAIgX,GAAG,GAAGhX,KAAK,CAAC1I,MAAM;UAAE2f,KAAK;UAAE/iB,MAAM;QACrC,IAAI8iB,GAAG,EAAE;UACL9iB,MAAM,GAAG8iB,GAAG,CAACjY,WAAW,CAAC,MAAM,CAAC,GAAG,GAAG,GAAGgY,UAAU;QACvD,CAAC,MACI;UACD7iB,MAAM,GAAG,UAAU,GAAG6iB,UAAU;QACpC;QACA,OAAOC,GAAG,EAAE;UACR,IAAIA,GAAG,CAACD,UAAU,CAAC,IAAI,CAACC,GAAG,CAACD,UAAU,CAAC,CAACH,UAAU,CAAC,EAAE;YACjDK,KAAK,GAAGlU,GAAG,CAACzF,mBAAmB,CAAC0Z,GAAG,CAACD,UAAU,CAAC,EAAE7iB,MAAM,CAAC;YACxD+iB,KAAK,CAACL,UAAU,CAAC,GAAGI,GAAG,CAACD,UAAU,CAAC;YACnCC,GAAG,CAACD,UAAU,CAAC,GAAGE,KAAK;UAC3B;UACAD,GAAG,GAAGA,GAAG,CAACE,aAAa;QAC3B;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC;IACD,KAAK,IAAIpmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGghB,UAAU,CAACjhB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC+lB,OAAO,CAAC/lB,CAAC,CAAC;IACd;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASqmB,oBAAoB,CAACxY,OAAO,EAAEoE,GAAG,EAAE;IACxC,IAAI8P,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE4C,SAAS,GAAGiT,EAAE,CAACjT,SAAS;MAAEC,KAAK,GAAGgT,EAAE,CAAChT,KAAK;IAC3E,IAAK,CAACD,SAAS,IAAI,CAACC,KAAK,IAAK,EAAE,iBAAiB,IAAIlB,OAAO,CAAC8W,QAAQ,CAAC,EAAE;MACpE;IACJ;IACA,IAAIjF,SAAS,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,0BAA0B,CAAC;IACvGzN,GAAG,CAACrF,cAAc,CAACqF,GAAG,EAAE0S,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAEjF,SAAS,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,CAAC,UAAU7R,OAAO,EAAE;IAChB,IAAI5M,YAAY,GAAG4M,OAAO,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;IACvE,SAAS3M,UAAU,CAACJ,IAAI,EAAE;MACtB,OAAOG,YAAY,GAAGH,IAAI;IAC9B;IACA+M,OAAO,CAAC3M,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,YAAY;MAC7C,IAAIG,IAAI,GAAGwM,OAAO,CAAC,MAAM,CAAC;MAC1BxM,IAAI,CAACoB,YAAY,CAAC,gBAAgB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;QAC7DA,GAAG,CAACtF,iBAAiB,GAAGA,iBAAiB;QACzC6U,aAAa,EAAE;MACnB,CAAC,CAAC;MACFngB,IAAI,CAACoB,YAAY,CAAC,iBAAiB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;QAC9DoU,oBAAoB,CAAC1lB,MAAM,EAAEsR,GAAG,CAAC;MACrC,CAAC,CAAC;MACF5Q,IAAI,CAACoB,YAAY,CAAC,mBAAmB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;QAChEsQ,sBAAsB,CAAC5hB,MAAM,EAAEsR,GAAG,CAAC;QACnCmS,6BAA6B,CAACnS,GAAG,EAAEtR,MAAM,CAAC;MAC9C,CAAC,CAAC;IACN,CAAC;EACL,CAAC,EAAE,OAAOkM,MAAM,KAAK,WAAW,GAC5BA,MAAM,GACN,OAAOlM,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOkJ,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAAC,CAAC,CAAC;EACrF;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIyc,UAAU,GAAG5Y,YAAY,CAAC,UAAU,CAAC;EACzC,SAAS6Y,UAAU,CAAC1Z,MAAM,EAAE2Z,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACzD,IAAIpV,SAAS,GAAG,IAAI;IACpB,IAAIqV,WAAW,GAAG,IAAI;IACtBH,OAAO,IAAIE,UAAU;IACrBD,UAAU,IAAIC,UAAU;IACxB,IAAIE,eAAe,GAAG,CAAC,CAAC;IACxB,SAASzhB,YAAY,CAACpB,IAAI,EAAE;MACxB,IAAIa,IAAI,GAAGb,IAAI,CAACa,IAAI;MACpBA,IAAI,CAACmF,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;QACvB,OAAOhG,IAAI,CAACJ,MAAM,CAACsF,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;MAC7C,CAAC;MACD8E,IAAI,CAAC2F,QAAQ,GAAG+G,SAAS,CAACrI,KAAK,CAAC4D,MAAM,EAAEjI,IAAI,CAACmF,IAAI,CAAC;MAClD,OAAOhG,IAAI;IACf;IACA,SAAS8iB,SAAS,CAAC9iB,IAAI,EAAE;MACrB,OAAO4iB,WAAW,CAACrmB,IAAI,CAACuM,MAAM,EAAE9I,IAAI,CAACa,IAAI,CAAC2F,QAAQ,CAAC;IACvD;IACA+G,SAAS,GACL1F,WAAW,CAACiB,MAAM,EAAE2Z,OAAO,EAAE,UAAUlgB,QAAQ,EAAE;MAAE,OAAO,UAAUuD,IAAI,EAAEE,IAAI,EAAE;QAC5E,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;UAC/B,IAAI+c,SAAS,GAAG;YACZjiB,UAAU,EAAE6hB,UAAU,KAAK,UAAU;YACrCK,KAAK,EAAGL,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,UAAU,GAAI3c,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACzEhF,SAAS;YACbgF,IAAI,EAAEA;UACV,CAAC;UACD,IAAIid,UAAU,GAAGjd,IAAI,CAAC,CAAC,CAAC;UACxBA,IAAI,CAAC,CAAC,CAAC,GAAG,SAASkd,KAAK,GAAG;YACvB,IAAI;cACA,OAAOD,UAAU,CAAC/d,KAAK,CAAC,IAAI,EAAEnJ,SAAS,CAAC;YAC5C,CAAC,SACO;cACJ;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAI,CAAEgnB,SAAS,CAACjiB,UAAW,EAAE;gBACzB,IAAI,OAAOiiB,SAAS,CAACvc,QAAQ,KAAK,QAAQ,EAAE;kBACxC;kBACA;kBACA,OAAOqc,eAAe,CAACE,SAAS,CAACvc,QAAQ,CAAC;gBAC9C,CAAC,MACI,IAAIuc,SAAS,CAACvc,QAAQ,EAAE;kBACzB;kBACA;kBACAuc,SAAS,CAACvc,QAAQ,CAAC+b,UAAU,CAAC,GAAG,IAAI;gBACzC;cACJ;YACJ;UACJ,CAAC;UACD,IAAIviB,IAAI,GAAG0J,gCAAgC,CAAC+Y,OAAO,EAAEzc,IAAI,CAAC,CAAC,CAAC,EAAE+c,SAAS,EAAE3hB,YAAY,EAAE0hB,SAAS,CAAC;UACjG,IAAI,CAAC9iB,IAAI,EAAE;YACP,OAAOA,IAAI;UACf;UACA;UACA,IAAImjB,MAAM,GAAGnjB,IAAI,CAACa,IAAI,CAAC2F,QAAQ;UAC/B,IAAI,OAAO2c,MAAM,KAAK,QAAQ,EAAE;YAC5B;YACA;YACAN,eAAe,CAACM,MAAM,CAAC,GAAGnjB,IAAI;UAClC,CAAC,MACI,IAAImjB,MAAM,EAAE;YACb;YACA;YACAA,MAAM,CAACZ,UAAU,CAAC,GAAGviB,IAAI;UAC7B;UACA;UACA;UACA,IAAImjB,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,KAAK,IAAI,OAAOF,MAAM,CAACC,GAAG,KAAK,UAAU,IACxE,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU,EAAE;YACpCrjB,IAAI,CAACojB,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,IAAI,CAACH,MAAM,CAAC;YAClCnjB,IAAI,CAACqjB,KAAK,GAAGF,MAAM,CAACE,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;UAC1C;UACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;YACtC,OAAOA,MAAM;UACjB;UACA,OAAOnjB,IAAI;QACf,CAAC,MACI;UACD;UACA,OAAOuC,QAAQ,CAAC2C,KAAK,CAAC4D,MAAM,EAAE9C,IAAI,CAAC;QACvC;MACJ,CAAC;IAAE,CAAC,CAAC;IACT4c,WAAW,GACP/a,WAAW,CAACiB,MAAM,EAAE4Z,UAAU,EAAE,UAAUngB,QAAQ,EAAE;MAAE,OAAO,UAAUuD,IAAI,EAAEE,IAAI,EAAE;QAC/E,IAAIud,EAAE,GAAGvd,IAAI,CAAC,CAAC,CAAC;QAChB,IAAIhG,IAAI;QACR,IAAI,OAAOujB,EAAE,KAAK,QAAQ,EAAE;UACxB;UACAvjB,IAAI,GAAG6iB,eAAe,CAACU,EAAE,CAAC;QAC9B,CAAC,MACI;UACD;UACAvjB,IAAI,GAAGujB,EAAE,IAAIA,EAAE,CAAChB,UAAU,CAAC;UAC3B;UACA,IAAI,CAACviB,IAAI,EAAE;YACPA,IAAI,GAAGujB,EAAE;UACb;QACJ;QACA,IAAIvjB,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,KAAK,QAAQ,EAAE;UACvC,IAAIJ,IAAI,CAACE,KAAK,KAAK,cAAc,KAC5BF,IAAI,CAACe,QAAQ,IAAIf,IAAI,CAACa,IAAI,CAACC,UAAU,IAAId,IAAI,CAACW,QAAQ,KAAK,CAAC,CAAC,EAAE;YAChE,IAAI,OAAO4iB,EAAE,KAAK,QAAQ,EAAE;cACxB,OAAOV,eAAe,CAACU,EAAE,CAAC;YAC9B,CAAC,MACI,IAAIA,EAAE,EAAE;cACTA,EAAE,CAAChB,UAAU,CAAC,GAAG,IAAI;YACzB;YACA;YACAviB,IAAI,CAAC5B,IAAI,CAAC8D,UAAU,CAAClC,IAAI,CAAC;UAC9B;QACJ,CAAC,MACI;UACD;UACAuC,QAAQ,CAAC2C,KAAK,CAAC4D,MAAM,EAAE9C,IAAI,CAAC;QAChC;MACJ,CAAC;IAAE,CAAC,CAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASwd,mBAAmB,CAAC1Z,OAAO,EAAEoE,GAAG,EAAE;IACvC,IAAI8P,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE4C,SAAS,GAAGiT,EAAE,CAACjT,SAAS;MAAEC,KAAK,GAAGgT,EAAE,CAAChT,KAAK;IAC3E,IAAK,CAACD,SAAS,IAAI,CAACC,KAAK,IAAK,CAAClB,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,IAAIA,OAAO,CAAC,EAAE;MACxF;IACJ;IACA,IAAI6R,SAAS,GAAG,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,0BAA0B,CAAC;IAC5GzN,GAAG,CAACrF,cAAc,CAACqF,GAAG,EAAEpE,OAAO,CAAC2Z,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAE9H,SAAS,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS+H,gBAAgB,CAAC5Z,OAAO,EAAEoE,GAAG,EAAE;IACpC,IAAI5Q,IAAI,CAAC4Q,GAAG,CAAC3G,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE;MACtC;MACA;IACJ;IACA,IAAIyW,EAAE,GAAG9P,GAAG,CAAC/F,gBAAgB,EAAE;MAAE8U,UAAU,GAAGe,EAAE,CAACf,UAAU;MAAEtH,oBAAoB,GAAGqI,EAAE,CAACrI,oBAAoB;MAAEpM,QAAQ,GAAGyU,EAAE,CAACzU,QAAQ;MAAEC,SAAS,GAAGwU,EAAE,CAACxU,SAAS;MAAEC,kBAAkB,GAAGuU,EAAE,CAACvU,kBAAkB;IACzM;IACA,KAAK,IAAIxN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGghB,UAAU,CAACjhB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC,IAAImQ,SAAS,GAAG6Q,UAAU,CAAChhB,CAAC,CAAC;MAC7B,IAAIia,cAAc,GAAG9J,SAAS,GAAG5C,SAAS;MAC1C,IAAI2M,aAAa,GAAG/J,SAAS,GAAG7C,QAAQ;MACxC,IAAIhC,MAAM,GAAGkC,kBAAkB,GAAGyM,cAAc;MAChD,IAAIE,aAAa,GAAG3M,kBAAkB,GAAG0M,aAAa;MACtDR,oBAAoB,CAACvJ,SAAS,CAAC,GAAG,CAAC,CAAC;MACpCuJ,oBAAoB,CAACvJ,SAAS,CAAC,CAAC5C,SAAS,CAAC,GAAGjC,MAAM;MACnDoO,oBAAoB,CAACvJ,SAAS,CAAC,CAAC7C,QAAQ,CAAC,GAAG6M,aAAa;IAC7D;IACA,IAAIwI,YAAY,GAAG9U,OAAO,CAAC,aAAa,CAAC;IACzC,IAAI,CAAC8U,YAAY,IAAI,CAACA,YAAY,CAACviB,SAAS,EAAE;MAC1C;IACJ;IACA6R,GAAG,CAACvG,gBAAgB,CAACmC,OAAO,EAAEoE,GAAG,EAAE,CAAC0Q,YAAY,IAAIA,YAAY,CAACviB,SAAS,CAAC,CAAC;IAC5E,OAAO,IAAI;EACf;EACA,SAASsnB,UAAU,CAAC/mB,MAAM,EAAEsR,GAAG,EAAE;IAC7BA,GAAG,CAACjG,mBAAmB,CAACrL,MAAM,EAAEsR,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI5Q,IAAI,CAACoB,YAAY,CAAC,QAAQ,EAAE,UAAU9B,MAAM,EAAE;IAC1C,IAAIgnB,WAAW,GAAGhnB,MAAM,CAACU,IAAI,CAACH,UAAU,CAAC,aAAa,CAAC,CAAC;IACxD,IAAIymB,WAAW,EAAE;MACbA,WAAW,EAAE;IACjB;EACJ,CAAC,CAAC;EACFtmB,IAAI,CAACoB,YAAY,CAAC,gBAAgB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC7DA,GAAG,CAACrG,WAAW,CAACjL,MAAM,EAAE,gBAAgB,EAAE,UAAU2F,QAAQ,EAAE;MAC1D,OAAO,UAAUuD,IAAI,EAAEE,IAAI,EAAE;QACzB1I,IAAI,CAACe,OAAO,CAACsD,iBAAiB,CAAC,gBAAgB,EAAEqE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC;IACL,CAAC,CAAC;EACN,CAAC,CAAC;EACF1I,IAAI,CAACoB,YAAY,CAAC,QAAQ,EAAE,UAAU9B,MAAM,EAAE;IAC1C,IAAI8N,GAAG,GAAG,KAAK;IACf,IAAImZ,KAAK,GAAG,OAAO;IACnBrB,UAAU,CAAC5lB,MAAM,EAAE8N,GAAG,EAAEmZ,KAAK,EAAE,SAAS,CAAC;IACzCrB,UAAU,CAAC5lB,MAAM,EAAE8N,GAAG,EAAEmZ,KAAK,EAAE,UAAU,CAAC;IAC1CrB,UAAU,CAAC5lB,MAAM,EAAE8N,GAAG,EAAEmZ,KAAK,EAAE,WAAW,CAAC;EAC/C,CAAC,CAAC;EACFvmB,IAAI,CAACoB,YAAY,CAAC,uBAAuB,EAAE,UAAU9B,MAAM,EAAE;IACzD4lB,UAAU,CAAC5lB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IACzD4lB,UAAU,CAAC5lB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC;IAC/D4lB,UAAU,CAAC5lB,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACzE,CAAC,CAAC;EACFU,IAAI,CAACoB,YAAY,CAAC,UAAU,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE;IAClD,IAAIwmB,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IACpD,KAAK,IAAI7nB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6nB,eAAe,CAAC9nB,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC7C,IAAI8nB,MAAM,GAAGD,eAAe,CAAC7nB,CAAC,CAAC;MAC/B4L,WAAW,CAACjL,MAAM,EAAEmnB,MAAM,EAAE,UAAUxhB,QAAQ,EAAEgF,MAAM,EAAExK,IAAI,EAAE;QAC1D,OAAO,UAAUinB,CAAC,EAAEhe,IAAI,EAAE;UACtB,OAAO1I,IAAI,CAACe,OAAO,CAACoB,GAAG,CAAC8C,QAAQ,EAAE3F,MAAM,EAAEoJ,IAAI,EAAEjJ,IAAI,CAAC;QACzD,CAAC;MACL,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFO,IAAI,CAACoB,YAAY,CAAC,aAAa,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC1DyV,UAAU,CAAC/mB,MAAM,EAAEsR,GAAG,CAAC;IACvBwV,gBAAgB,CAAC9mB,MAAM,EAAEsR,GAAG,CAAC;IAC7B;IACA,IAAI+V,yBAAyB,GAAGrnB,MAAM,CAAC,2BAA2B,CAAC;IACnE,IAAIqnB,yBAAyB,IAAIA,yBAAyB,CAAC5nB,SAAS,EAAE;MAClE6R,GAAG,CAACvG,gBAAgB,CAAC/K,MAAM,EAAEsR,GAAG,EAAE,CAAC+V,yBAAyB,CAAC5nB,SAAS,CAAC,CAAC;IAC5E;EACJ,CAAC,CAAC;EACFiB,IAAI,CAACoB,YAAY,CAAC,kBAAkB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC/D1F,UAAU,CAAC,kBAAkB,CAAC;IAC9BA,UAAU,CAAC,wBAAwB,CAAC;EACxC,CAAC,CAAC;EACFlL,IAAI,CAACoB,YAAY,CAAC,sBAAsB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IACnE1F,UAAU,CAAC,sBAAsB,CAAC;EACtC,CAAC,CAAC;EACFlL,IAAI,CAACoB,YAAY,CAAC,YAAY,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IACzD1F,UAAU,CAAC,YAAY,CAAC;EAC5B,CAAC,CAAC;EACFlL,IAAI,CAACoB,YAAY,CAAC,aAAa,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC1D2O,uBAAuB,CAAC3O,GAAG,EAAEtR,MAAM,CAAC;EACxC,CAAC,CAAC;EACFU,IAAI,CAACoB,YAAY,CAAC,gBAAgB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE4Q,GAAG,EAAE;IAC7DsV,mBAAmB,CAAC5mB,MAAM,EAAEsR,GAAG,CAAC;EACpC,CAAC,CAAC;EACF5Q,IAAI,CAACoB,YAAY,CAAC,KAAK,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE;IAC7C;IACA4mB,QAAQ,CAACtnB,MAAM,CAAC;IAChB,IAAIunB,QAAQ,GAAGxa,YAAY,CAAC,SAAS,CAAC;IACtC,IAAIya,QAAQ,GAAGza,YAAY,CAAC,SAAS,CAAC;IACtC,IAAI0a,YAAY,GAAG1a,YAAY,CAAC,aAAa,CAAC;IAC9C,IAAI2a,aAAa,GAAG3a,YAAY,CAAC,cAAc,CAAC;IAChD,IAAI4a,OAAO,GAAG5a,YAAY,CAAC,QAAQ,CAAC;IACpC,IAAI6a,0BAA0B,GAAG7a,YAAY,CAAC,yBAAyB,CAAC;IACxE,SAASua,QAAQ,CAACpb,MAAM,EAAE;MACtB,IAAIiY,cAAc,GAAGjY,MAAM,CAAC,gBAAgB,CAAC;MAC7C,IAAI,CAACiY,cAAc,EAAE;QACjB;QACA;MACJ;MACA,IAAIE,uBAAuB,GAAGF,cAAc,CAAC1kB,SAAS;MACtD,SAASooB,eAAe,CAAChiB,MAAM,EAAE;QAC7B,OAAOA,MAAM,CAAC0hB,QAAQ,CAAC;MAC3B;MACA,IAAIO,cAAc,GAAGzD,uBAAuB,CAAC5X,8BAA8B,CAAC;MAC5E,IAAIsb,iBAAiB,GAAG1D,uBAAuB,CAAC3X,iCAAiC,CAAC;MAClF,IAAI,CAACob,cAAc,EAAE;QACjB,IAAIE,2BAA2B,GAAG9b,MAAM,CAAC,2BAA2B,CAAC;QACrE,IAAI8b,2BAA2B,EAAE;UAC7B,IAAIC,kCAAkC,GAAGD,2BAA2B,CAACvoB,SAAS;UAC9EqoB,cAAc,GAAGG,kCAAkC,CAACxb,8BAA8B,CAAC;UACnFsb,iBAAiB,GAAGE,kCAAkC,CAACvb,iCAAiC,CAAC;QAC7F;MACJ;MACA,IAAIwb,kBAAkB,GAAG,kBAAkB;MAC3C,IAAIC,SAAS,GAAG,WAAW;MAC3B,SAAS3jB,YAAY,CAACpB,IAAI,EAAE;QACxB,IAAIa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACpB,IAAI4B,MAAM,GAAG5B,IAAI,CAAC4B,MAAM;QACxBA,MAAM,CAAC6hB,aAAa,CAAC,GAAG,KAAK;QAC7B7hB,MAAM,CAAC+hB,0BAA0B,CAAC,GAAG,KAAK;QAC1C;QACA,IAAInZ,QAAQ,GAAG5I,MAAM,CAAC4hB,YAAY,CAAC;QACnC,IAAI,CAACK,cAAc,EAAE;UACjBA,cAAc,GAAGjiB,MAAM,CAAC4G,8BAA8B,CAAC;UACvDsb,iBAAiB,GAAGliB,MAAM,CAAC6G,iCAAiC,CAAC;QACjE;QACA,IAAI+B,QAAQ,EAAE;UACVsZ,iBAAiB,CAACpoB,IAAI,CAACkG,MAAM,EAAEqiB,kBAAkB,EAAEzZ,QAAQ,CAAC;QAChE;QACA,IAAI2Z,WAAW,GAAGviB,MAAM,CAAC4hB,YAAY,CAAC,GAAG,YAAY;UACjD,IAAI5hB,MAAM,CAACwiB,UAAU,KAAKxiB,MAAM,CAACyiB,IAAI,EAAE;YACnC;YACA;YACA,IAAI,CAACrkB,IAAI,CAACskB,OAAO,IAAI1iB,MAAM,CAAC6hB,aAAa,CAAC,IAAItkB,IAAI,CAACE,KAAK,KAAK6kB,SAAS,EAAE;cACpE;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAIK,SAAS,GAAG3iB,MAAM,CAACnF,IAAI,CAACH,UAAU,CAAC,WAAW,CAAC,CAAC;cACpD,IAAIsF,MAAM,CAAC0Q,MAAM,KAAK,CAAC,IAAIiS,SAAS,IAAIA,SAAS,CAACppB,MAAM,GAAG,CAAC,EAAE;gBAC1D,IAAIqpB,WAAW,GAAGrlB,IAAI,CAACJ,MAAM;gBAC7BI,IAAI,CAACJ,MAAM,GAAG,YAAY;kBACtB;kBACA;kBACA,IAAIwlB,SAAS,GAAG3iB,MAAM,CAACnF,IAAI,CAACH,UAAU,CAAC,WAAW,CAAC,CAAC;kBACpD,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmpB,SAAS,CAACppB,MAAM,EAAEC,CAAC,EAAE,EAAE;oBACvC,IAAImpB,SAAS,CAACnpB,CAAC,CAAC,KAAK+D,IAAI,EAAE;sBACvBolB,SAAS,CAAChU,MAAM,CAACnV,CAAC,EAAE,CAAC,CAAC;oBAC1B;kBACJ;kBACA,IAAI,CAAC4E,IAAI,CAACskB,OAAO,IAAInlB,IAAI,CAACE,KAAK,KAAK6kB,SAAS,EAAE;oBAC3CM,WAAW,CAAC9oB,IAAI,CAACyD,IAAI,CAAC;kBAC1B;gBACJ,CAAC;gBACDolB,SAAS,CAAChgB,IAAI,CAACpF,IAAI,CAAC;cACxB,CAAC,MACI;gBACDA,IAAI,CAACJ,MAAM,EAAE;cACjB;YACJ,CAAC,MACI,IAAI,CAACiB,IAAI,CAACskB,OAAO,IAAI1iB,MAAM,CAAC6hB,aAAa,CAAC,KAAK,KAAK,EAAE;cACvD;cACA7hB,MAAM,CAAC+hB,0BAA0B,CAAC,GAAG,IAAI;YAC7C;UACJ;QACJ,CAAC;QACDE,cAAc,CAACnoB,IAAI,CAACkG,MAAM,EAAEqiB,kBAAkB,EAAEE,WAAW,CAAC;QAC5D,IAAIM,UAAU,GAAG7iB,MAAM,CAAC0hB,QAAQ,CAAC;QACjC,IAAI,CAACmB,UAAU,EAAE;UACb7iB,MAAM,CAAC0hB,QAAQ,CAAC,GAAGnkB,IAAI;QAC3B;QACAulB,UAAU,CAACrgB,KAAK,CAACzC,MAAM,EAAE5B,IAAI,CAACmF,IAAI,CAAC;QACnCvD,MAAM,CAAC6hB,aAAa,CAAC,GAAG,IAAI;QAC5B,OAAOtkB,IAAI;MACf;MACA,SAASwlB,mBAAmB,GAAG,CAAE;MACjC,SAAS1C,SAAS,CAAC9iB,IAAI,EAAE;QACrB,IAAIa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACpB;QACA;QACAA,IAAI,CAACskB,OAAO,GAAG,IAAI;QACnB,OAAOM,WAAW,CAACvgB,KAAK,CAACrE,IAAI,CAAC4B,MAAM,EAAE5B,IAAI,CAACmF,IAAI,CAAC;MACpD;MACA,IAAI0f,UAAU,GAAG7d,WAAW,CAACoZ,uBAAuB,EAAE,MAAM,EAAE,YAAY;QAAE,OAAO,UAAUnb,IAAI,EAAEE,IAAI,EAAE;UACrGF,IAAI,CAACse,QAAQ,CAAC,GAAGpe,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;UACjCF,IAAI,CAACye,OAAO,CAAC,GAAGve,IAAI,CAAC,CAAC,CAAC;UACvB,OAAO0f,UAAU,CAACxgB,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;QACvC,CAAC;MAAE,CAAC,CAAC;MACL,IAAI2f,qBAAqB,GAAG,qBAAqB;MACjD,IAAIC,iBAAiB,GAAGjc,YAAY,CAAC,mBAAmB,CAAC;MACzD,IAAIkc,mBAAmB,GAAGlc,YAAY,CAAC,qBAAqB,CAAC;MAC7D,IAAI4b,UAAU,GAAG1d,WAAW,CAACoZ,uBAAuB,EAAE,MAAM,EAAE,YAAY;QAAE,OAAO,UAAUnb,IAAI,EAAEE,IAAI,EAAE;UACrG,IAAI1I,IAAI,CAACe,OAAO,CAACwnB,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC5C;YACA;YACA;YACA,OAAON,UAAU,CAACrgB,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;UACvC;UACA,IAAIF,IAAI,CAACse,QAAQ,CAAC,EAAE;YAChB;YACA,OAAOmB,UAAU,CAACrgB,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;UACvC,CAAC,MACI;YACD,IAAIJ,OAAO,GAAG;cAAEnD,MAAM,EAAEqD,IAAI;cAAEggB,GAAG,EAAEhgB,IAAI,CAACye,OAAO,CAAC;cAAEzjB,UAAU,EAAE,KAAK;cAAEkF,IAAI,EAAEA,IAAI;cAAEmf,OAAO,EAAE;YAAM,CAAC;YACjG,IAAInlB,IAAI,GAAG0J,gCAAgC,CAACic,qBAAqB,EAAEH,mBAAmB,EAAE5f,OAAO,EAAExE,YAAY,EAAE0hB,SAAS,CAAC;YACzH,IAAIhd,IAAI,IAAIA,IAAI,CAAC0e,0BAA0B,CAAC,KAAK,IAAI,IAAI,CAAC5e,OAAO,CAACuf,OAAO,IACrEnlB,IAAI,CAACE,KAAK,KAAK6kB,SAAS,EAAE;cAC1B;cACA;cACA;cACA/kB,IAAI,CAACJ,MAAM,EAAE;YACjB;UACJ;QACJ,CAAC;MAAE,CAAC,CAAC;MACL,IAAI6lB,WAAW,GAAG5d,WAAW,CAACoZ,uBAAuB,EAAE,OAAO,EAAE,YAAY;QAAE,OAAO,UAAUnb,IAAI,EAAEE,IAAI,EAAE;UACvG,IAAIhG,IAAI,GAAGykB,eAAe,CAAC3e,IAAI,CAAC;UAChC,IAAI9F,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,IAAI,QAAQ,EAAE;YACtC;YACA;YACA;YACA;YACA,IAAIJ,IAAI,CAACe,QAAQ,IAAI,IAAI,IAAKf,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACskB,OAAQ,EAAE;cAC3D;YACJ;YACAnlB,IAAI,CAAC5B,IAAI,CAAC8D,UAAU,CAAClC,IAAI,CAAC;UAC9B,CAAC,MACI,IAAI1C,IAAI,CAACe,OAAO,CAACunB,iBAAiB,CAAC,KAAK,IAAI,EAAE;YAC/C;YACA,OAAOH,WAAW,CAACvgB,KAAK,CAACY,IAAI,EAAEE,IAAI,CAAC;UACxC;UACA;UACA;UACA;QACJ,CAAC;MAAE,CAAC,CAAC;IACT;EACJ,CAAC,CAAC;;EACF1I,IAAI,CAACoB,YAAY,CAAC,aAAa,EAAE,UAAU9B,MAAM,EAAE;IAC/C;IACA,IAAIA,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,WAAW,CAAC,CAACmpB,WAAW,EAAE;MACxD/b,cAAc,CAACpN,MAAM,CAAC,WAAW,CAAC,CAACmpB,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;IAC5F;EACJ,CAAC,CAAC;EACFzoB,IAAI,CAACoB,YAAY,CAAC,uBAAuB,EAAE,UAAU9B,MAAM,EAAEU,IAAI,EAAE;IAC/D;IACA,SAAS0oB,2BAA2B,CAAChL,OAAO,EAAE;MAC1C,OAAO,UAAUvM,CAAC,EAAE;QAChB,IAAIwX,UAAU,GAAGrL,cAAc,CAAChe,MAAM,EAAEoe,OAAO,CAAC;QAChDiL,UAAU,CAACnK,OAAO,CAAC,UAAUzb,SAAS,EAAE;UACpC;UACA;UACA,IAAI6lB,qBAAqB,GAAGtpB,MAAM,CAAC,uBAAuB,CAAC;UAC3D,IAAIspB,qBAAqB,EAAE;YACvB,IAAIC,GAAG,GAAG,IAAID,qBAAqB,CAAClL,OAAO,EAAE;cAAE5K,OAAO,EAAE3B,CAAC,CAAC2B,OAAO;cAAEiD,MAAM,EAAE5E,CAAC,CAACC;YAAU,CAAC,CAAC;YACzFrO,SAAS,CAACT,MAAM,CAACumB,GAAG,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,IAAIvpB,MAAM,CAAC,uBAAuB,CAAC,EAAE;MACjCU,IAAI,CAACqM,YAAY,CAAC,kCAAkC,CAAC,CAAC,GAClDqc,2BAA2B,CAAC,oBAAoB,CAAC;MACrD1oB,IAAI,CAACqM,YAAY,CAAC,yBAAyB,CAAC,CAAC,GACzCqc,2BAA2B,CAAC,kBAAkB,CAAC;IACvD;EACJ,CAAC,CAAC;AACN,CAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}