{"ast": null, "code": "// This file is required by karma.conf.js and loads recursively all the .spec and framework files\nimport 'zone.js/dist/zone-testing';\nimport { getTestBed } from '@angular/core/testing';\nimport { BrowserDynamicTestingModule, platformBrowserDynamicTesting } from '@angular/platform-browser-dynamic/testing';\n// First, initialize the Angular testing environment.\ngetTestBed().initTestEnvironment(BrowserDynamicTestingModule, platformBrowserDynamicTesting());\n// Then we find all the tests.\nconst context = require.context('./', true, /\\.spec\\.ts$/);\n// And load the modules.\ncontext.keys().map(context);", "map": {"version": 3, "mappings": "AAAA;AAEA,OAAO,2BAA2B;AAClC,SAASA,UAAU,QAAQ,uBAAuB;AAClD,SACEC,2BAA2B,EAC3BC,6BAA6B,QACxB,2CAA2C;AASlD;AACAF,UAAU,EAAE,CAACG,mBAAmB,CAC9BF,2BAA2B,EAC3BC,6BAA6B,EAAE,CAChC;AAED;AACA,MAAME,OAAO,GAAGC,OAAO,CAACD,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;AAC1D;AACAA,OAAO,CAACE,IAAI,EAAE,CAACC,GAAG,CAACH,OAAO,CAAC", "names": ["getTestBed", "BrowserDynamicTestingModule", "platformBrowserDynamicTesting", "initTestEnvironment", "context", "require", "keys", "map"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\test.ts"], "sourcesContent": ["// This file is required by karma.conf.js and loads recursively all the .spec and framework files\n\nimport 'zone.js/dist/zone-testing';\nimport { getTestBed } from '@angular/core/testing';\nimport {\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting\n} from '@angular/platform-browser-dynamic/testing';\n\ndeclare const require: {\n  context(path: string, deep?: boolean, filter?: RegExp): {\n    keys(): string[];\n    <T>(id: string): T;\n  };\n};\n\n// First, initialize the Angular testing environment.\ngetTestBed().initTestEnvironment(\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting(),\n);\n\n// Then we find all the tests.\nconst context = require.context('./', true, /\\.spec\\.ts$/);\n// And load the modules.\ncontext.keys().map(context);\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}