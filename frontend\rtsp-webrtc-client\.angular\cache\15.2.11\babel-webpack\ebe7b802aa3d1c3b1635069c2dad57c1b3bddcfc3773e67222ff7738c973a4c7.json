{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nlet StreamViewerComponent = class StreamViewerComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    // Component properties\n    this.connectionState = 'disconnected';\n    this.currentStreamId = null;\n    this.activeStreams = null;\n    this.errorMessage = null;\n    this.hasStream = false;\n    // default Form data\n    this.rtspUrl = 'rtsp://admin:Admin@123@*************';\n    this.cameraId = 1;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n    // Subscribe to stream changes\n    this.subscriptions.push(this.webrtcService.stream$.subscribe(stream => {\n      if (stream && this.videoElement) {\n        this.videoElement.nativeElement.srcObject = stream;\n        this.hasStream = true;\n        this.clearError();\n      } else {\n        this.hasStream = false;\n        if (this.videoElement) {\n          this.videoElement.nativeElement.srcObject = null;\n        }\n      }\n    }));\n    // Load active streams on init\n    this.refreshStreams();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n  startStream() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.clearError();\n        if (!_this.rtspUrl.trim()) {\n          _this.errorMessage = 'Please enter a valid RTSP URL';\n          return;\n        }\n        const streamRequest = {\n          rtsp: _this.rtspUrl.trim(),\n          camera: _this.cameraId\n        };\n        const response = yield _this.webrtcService.startStream(streamRequest);\n        _this.currentStreamId = response.stream_id;\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this.errorMessage = `Failed to start stream: ${error}`;\n      }\n    })();\n  }\n  stopStream() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.webrtcService.stopStream(_this2.currentStreamId || undefined);\n        _this2.currentStreamId = null;\n        _this2.hasStream = false;\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this2.errorMessage = `Failed to stop stream: ${error}`;\n      }\n    })();\n  }\n  refreshStreams() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const streams = yield _this3.webrtcService.getActiveStreams();\n        _this3.activeStreams = streams;\n      } catch (error) {\n        console.error('Failed to refresh streams:', error);\n      }\n    })();\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  isConnecting() {\n    return this.connectionState === 'connecting';\n  }\n  isConnected() {\n    return this.connectionState === 'connected';\n  }\n};\n__decorate([ViewChild('videoElement', {\n  static: true\n})], StreamViewerComponent.prototype, \"videoElement\", void 0);\nStreamViewerComponent = __decorate([Component({\n  selector: 'app-stream-viewer',\n  templateUrl: './stream-viewer.component.html',\n  styles: [`\n    .stream-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n      font-family: Arial, sans-serif;\n    }\n\n    .config-panel, .status-panel, .video-panel, .error-panel {\n      background: #f5f5f5;\n      border-radius: 8px;\n      padding: 20px;\n      margin-bottom: 20px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .form-group {\n      margin-bottom: 15px;\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: 5px;\n      font-weight: bold;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 10px;\n      flex-wrap: wrap;\n    }\n\n    .btn {\n      padding: 10px 20px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-size: 14px;\n      transition: background-color 0.3s;\n    }\n\n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .btn-primary {\n      background-color: #007bff;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background-color: #0056b3;\n    }\n\n    .btn-secondary {\n      background-color: #6c757d;\n      color: white;\n    }\n\n    .btn-secondary:hover:not(:disabled) {\n      background-color: #545b62;\n    }\n\n    .btn-info {\n      background-color: #17a2b8;\n      color: white;\n    }\n\n    .btn-info:hover:not(:disabled) {\n      background-color: #117a8b;\n    }\n\n    .btn-small {\n      padding: 5px 10px;\n      font-size: 12px;\n    }\n\n    .status-item {\n      display: flex;\n      margin-bottom: 10px;\n    }\n\n    .status-label {\n      font-weight: bold;\n      min-width: 120px;\n    }\n\n    .status-value {\n      padding: 2px 8px;\n      border-radius: 4px;\n    }\n\n    .status-connected {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-connecting {\n      background-color: #fff3cd;\n      color: #856404;\n    }\n\n    .status-disconnected {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .video-container {\n      position: relative;\n      background: #000;\n      border-radius: 4px;\n      overflow: hidden;\n    }\n\n    .video-player {\n      width: 100%;\n      height: auto;\n      max-height: 600px;\n      display: block;\n    }\n\n    .video-player.hidden {\n      display: none;\n    }\n\n    .video-placeholder {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 300px;\n      color: #666;\n      text-align: center;\n    }\n\n    .error-panel {\n      background-color: #f8d7da;\n      border: 1px solid #f5c6cb;\n    }\n\n    .error-message {\n      color: #721c24;\n      margin-bottom: 10px;\n    }\n\n    h1, h3 {\n      color: #333;\n      margin-bottom: 15px;\n    }\n\n    h1 {\n      text-align: center;\n      border-bottom: 2px solid #007bff;\n      padding-bottom: 10px;\n    }\n  `]\n})], StreamViewerComponent);\nexport { StreamViewerComponent };", "map": {"version": 3, "mappings": ";;AAAA,SAASA,SAAS,EAAcC,SAAS,QAA2B,eAAe;AA6K5E,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAgBhCC,YAAoBC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAbjC;IACA,oBAAe,GAAW,cAAc;IACxC,oBAAe,GAAkB,IAAI;IACrC,kBAAa,GAAQ,IAAI;IACzB,iBAAY,GAAkB,IAAI;IAClC,cAAS,GAAY,KAAK;IAE1B;IACA,YAAO,GAAG,sCAAsC;IAChD,aAAQ,GAAG,CAAC;IAEJ,kBAAa,GAAmB,EAAE;EAES;EAEnDC,QAAQ;IACN;IACA,IAAI,CAACC,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACI,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACJ,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACQ,OAAO,CAACH,SAAS,CAACI,MAAM,IAAG;MAC5C,IAAIA,MAAM,IAAI,IAAI,CAACC,YAAY,EAAE;QAC/B,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAGH,MAAM;QAClD,IAAI,CAACI,SAAS,GAAG,IAAI;QACrB,IAAI,CAACC,UAAU,EAAE;OAClB,MAAM;QACL,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,IAAI,IAAI,CAACH,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI;;;IAGtD,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACG,cAAc,EAAE;EACvB;EAEAC,WAAW;IACT,IAAI,CAACd,aAAa,CAACe,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEMC,WAAW;IAAA;IAAA;MACf,IAAI;QACF,KAAI,CAACP,UAAU,EAAE;QAEjB,IAAI,CAAC,KAAI,CAACQ,OAAO,CAACC,IAAI,EAAE,EAAE;UACxB,KAAI,CAACC,YAAY,GAAG,+BAA+B;UACnD;;QAGF,MAAMC,aAAa,GAAkB;UACnCC,IAAI,EAAE,KAAI,CAACJ,OAAO,CAACC,IAAI,EAAE;UACzBI,MAAM,EAAE,KAAI,CAACC;SACd;QAED,MAAMC,QAAQ,SAAyB,KAAI,CAAC7B,aAAa,CAACqB,WAAW,CAACI,aAAa,CAAC;QACpF,KAAI,CAACK,eAAe,GAAGD,QAAQ,CAACE,SAAS;QAEzCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,QAAQ,CAAC;OAEtD,CAAC,OAAOK,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,KAAI,CAACV,YAAY,GAAG,2BAA2BU,KAAK,EAAE;;IACvD;EACH;EAEMd,UAAU;IAAA;IAAA;MACd,IAAI;QACF,MAAM,MAAI,CAACpB,aAAa,CAACoB,UAAU,CAAC,MAAI,CAACU,eAAe,IAAIK,SAAS,CAAC;QACtE,MAAI,CAACL,eAAe,GAAG,IAAI;QAC3B,MAAI,CAACjB,SAAS,GAAG,KAAK;QACtBmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACV,YAAY,GAAG,0BAA0BU,KAAK,EAAE;;IACtD;EACH;EAEMnB,cAAc;IAAA;IAAA;MAClB,IAAI;QACF,MAAMqB,OAAO,SAAS,MAAI,CAACpC,aAAa,CAACqC,gBAAgB,EAAE;QAC3D,MAAI,CAACC,aAAa,GAAGF,OAAO;OAC7B,CAAC,OAAOF,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAEApB,UAAU;IACR,IAAI,CAACU,YAAY,GAAG,IAAI;EAC1B;EAEAe,YAAY;IACV,OAAO,IAAI,CAAChC,eAAe,KAAK,YAAY;EAC9C;EAEAiC,WAAW;IACT,OAAO,IAAI,CAACjC,eAAe,KAAK,WAAW;EAC7C;CACD;AA3G8CkC,YAA5C5C,SAAS,CAAC,cAAc,EAAE;EAAE6C,MAAM,EAAE;AAAI,CAAE,CAAC,2DAA6C;AAD9E5C,qBAAqB,eAzKjCF,SAAS,CAAC;EACT+C,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoKR;CACF,CAAC,GACW/C,qBAAqB,CA4GjC;SA5GYA,qBAAqB", "names": ["Component", "ViewChild", "StreamViewerComponent", "constructor", "webrtcService", "ngOnInit", "subscriptions", "push", "connectionState$", "subscribe", "state", "connectionState", "stream$", "stream", "videoElement", "nativeElement", "srcObject", "hasStream", "clearError", "refreshStreams", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "stopStream", "startStream", "rtspUrl", "trim", "errorMessage", "streamRequest", "rtsp", "camera", "cameraId", "response", "currentStreamId", "stream_id", "console", "log", "error", "undefined", "streams", "getActiveStreams", "activeStreams", "isConnecting", "isConnected", "__decorate", "static", "selector", "templateUrl", "styles"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\stream-viewer.component.ts"], "sourcesContent": ["import { Compo<PERSON>, ElementRef, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { WebRTCService, StreamRequest, StreamResponse } from '../services/webrtc.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-stream-viewer',\n  templateUrl: './stream-viewer.component.html',\n  styles: [`\n    .stream-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n      font-family: Arial, sans-serif;\n    }\n\n    .config-panel, .status-panel, .video-panel, .error-panel {\n      background: #f5f5f5;\n      border-radius: 8px;\n      padding: 20px;\n      margin-bottom: 20px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .form-group {\n      margin-bottom: 15px;\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: 5px;\n      font-weight: bold;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 10px;\n      flex-wrap: wrap;\n    }\n\n    .btn {\n      padding: 10px 20px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-size: 14px;\n      transition: background-color 0.3s;\n    }\n\n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .btn-primary {\n      background-color: #007bff;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background-color: #0056b3;\n    }\n\n    .btn-secondary {\n      background-color: #6c757d;\n      color: white;\n    }\n\n    .btn-secondary:hover:not(:disabled) {\n      background-color: #545b62;\n    }\n\n    .btn-info {\n      background-color: #17a2b8;\n      color: white;\n    }\n\n    .btn-info:hover:not(:disabled) {\n      background-color: #117a8b;\n    }\n\n    .btn-small {\n      padding: 5px 10px;\n      font-size: 12px;\n    }\n\n    .status-item {\n      display: flex;\n      margin-bottom: 10px;\n    }\n\n    .status-label {\n      font-weight: bold;\n      min-width: 120px;\n    }\n\n    .status-value {\n      padding: 2px 8px;\n      border-radius: 4px;\n    }\n\n    .status-connected {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-connecting {\n      background-color: #fff3cd;\n      color: #856404;\n    }\n\n    .status-disconnected {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .video-container {\n      position: relative;\n      background: #000;\n      border-radius: 4px;\n      overflow: hidden;\n    }\n\n    .video-player {\n      width: 100%;\n      height: auto;\n      max-height: 600px;\n      display: block;\n    }\n\n    .video-player.hidden {\n      display: none;\n    }\n\n    .video-placeholder {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 300px;\n      color: #666;\n      text-align: center;\n    }\n\n    .error-panel {\n      background-color: #f8d7da;\n      border: 1px solid #f5c6cb;\n    }\n\n    .error-message {\n      color: #721c24;\n      margin-bottom: 10px;\n    }\n\n    h1, h3 {\n      color: #333;\n      margin-bottom: 15px;\n    }\n\n    h1 {\n      text-align: center;\n      border-bottom: 2px solid #007bff;\n      padding-bottom: 10px;\n    }\n  `]\n})\nexport class StreamViewerComponent implements OnInit, OnDestroy {\n  @ViewChild('videoElement', { static: true }) videoElement!: ElementRef<HTMLVideoElement>;\n\n  // Component properties\n  connectionState: string = 'disconnected';\n  currentStreamId: string | null = null;\n  activeStreams: any = null;\n  errorMessage: string | null = null;\n  hasStream: boolean = false;\n\n  // default Form data\n  rtspUrl = 'rtsp://admin:Admin@123@*************';\n  cameraId = 1;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(private webrtcService: WebRTCService) {}\n\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n\n    // Subscribe to stream changes\n    this.subscriptions.push(\n      this.webrtcService.stream$.subscribe(stream => {\n        if (stream && this.videoElement) {\n          this.videoElement.nativeElement.srcObject = stream;\n          this.hasStream = true;\n          this.clearError();\n        } else {\n          this.hasStream = false;\n          if (this.videoElement) {\n            this.videoElement.nativeElement.srcObject = null;\n          }\n        }\n      })\n    );\n\n    // Load active streams on init\n    this.refreshStreams();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n\n  async startStream() {\n    try {\n      this.clearError();\n\n      if (!this.rtspUrl.trim()) {\n        this.errorMessage = 'Please enter a valid RTSP URL';\n        return;\n      }\n\n      const streamRequest: StreamRequest = {\n        rtsp: this.rtspUrl.trim(),\n        camera: this.cameraId\n      };\n\n      const response: StreamResponse = await this.webrtcService.startStream(streamRequest);\n      this.currentStreamId = response.stream_id;\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream: ${error}`;\n    }\n  }\n\n  async stopStream() {\n    try {\n      await this.webrtcService.stopStream(this.currentStreamId || undefined);\n      this.currentStreamId = null;\n      this.hasStream = false;\n      console.log('Stream stopped successfully');\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream: ${error}`;\n    }\n  }\n\n  async refreshStreams() {\n    try {\n      const streams = await this.webrtcService.getActiveStreams();\n      this.activeStreams = streams;\n    } catch (error) {\n      console.error('Failed to refresh streams:', error);\n    }\n  }\n\n  clearError() {\n    this.errorMessage = null;\n  }\n\n  isConnecting(): boolean {\n    return this.connectionState === 'connecting';\n  }\n\n  isConnected(): boolean {\n    return this.connectionState === 'connected';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}