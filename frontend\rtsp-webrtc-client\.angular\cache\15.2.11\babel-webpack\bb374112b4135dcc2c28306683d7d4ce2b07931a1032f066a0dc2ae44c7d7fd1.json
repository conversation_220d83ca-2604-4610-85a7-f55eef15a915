{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CameraChannel } from '../models/camera.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/camera.service\";\nimport * as i2 from \"../services/webrtc.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"videoElem\"];\nfunction IcccCameraComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"input\", 14);\n    i0.ɵɵlistener(\"change\", function IcccCameraComponent_div_11_Template_input_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const camera_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onToggleStream(camera_r2, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const camera_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"camera-\" + camera_r2.channelid)(\"checked\", camera_r2.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"camera-\" + camera_r2.channelid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", camera_r2.channelname, \" \");\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_video_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 22);\n  }\n  if (rf & 2) {\n    const data_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", \"video-\" + data_r6.channelid);\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"p\");\n    i0.ɵɵtext(2, \"No stream available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 3)(2, \"div\", 4)(3, \"h6\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 5);\n    i0.ɵɵtemplate(6, IcccCameraComponent_div_14_div_3_video_6_Template, 1, 1, \"video\", 20);\n    i0.ɵɵtemplate(7, IcccCameraComponent_div_14_div_3_div_7_Template, 3, 0, \"div\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(data_r6.channelname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", data_r6.webrtcUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !data_r6.webrtcUrl);\n  }\n}\nfunction IcccCameraComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵtemplate(3, IcccCameraComponent_div_14_div_3_Template, 8, 3, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.resultLive);\n  }\n}\nexport class IcccCameraComponent {\n  constructor(cameraService, webrtcService, http) {\n    this.cameraService = cameraService;\n    this.webrtcService = webrtcService;\n    this.http = http;\n    this.camera = [];\n    this.filteredCamera = [];\n  }\n  ngOnInit() {\n    this.getCamera();\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [new CameraChannel(1, undefined, 'Camera 1', '192.168.4.244', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined), new CameraChannel(2, undefined, 'Camera 2', '192.168.4.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined), new CameraChannel(3, undefined, 'Camera 3', '192.168.4.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined)];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream, cam) {\n    const videoElement = document.getElementById(`video-${cam}`);\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n  onToggleStream(camera, event) {\n    const checked = event.target.checked;\n    this.toggleStream(camera.username, camera.password, camera.channelip, camera.channelid, checked);\n  }\n  toggleStream(username, password, channelip, channelid, checked) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!checked) {\n        _this.stopStream(channelip, channelid, username, password, checked);\n      } else {\n        const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n        let cam = String(channelid);\n        try {\n          const response = yield _this.webrtcService.startCameraStream(rtsp, cam);\n          const camera = _this.camera.find(data => data.channelid === channelid);\n          if (camera) {\n            camera.webrtcUrl = response.webrtc_url;\n            camera.streamId = response.stream_id;\n            camera.checked = true;\n            // Subscribe to WebRTC stream and play it\n            setTimeout(() => {\n              _this.webrtcService.stream$.subscribe(ms => {\n                if (ms) {\n                  _this.playWebRTC(ms, cam);\n                }\n              });\n              console.log(`WebRTC URL for ${cam}:`, response.webrtc_url);\n            }, 1000);\n          }\n        } catch (error) {\n          console.error('Error starting WebRTC stream:', error);\n        }\n      }\n    })();\n  }\n  stopStream(channelip, channelid, username, password, checked) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n      let cam = String(channelid);\n      try {\n        yield _this2.webrtcService.stopCameraStream(rtsp, cam);\n        const camera = _this2.camera.find(data => data.channelid === channelid);\n        if (camera) {\n          camera.webrtcUrl = undefined;\n          camera.streamId = undefined;\n          camera.checked = false;\n          // Stop video element\n          const videoElement = document.getElementById(`video-${cam}`);\n          if (videoElement) {\n            videoElement.srcObject = null;\n          }\n        }\n      } catch (error) {\n        console.error('Error stopping WebRTC stream:', error);\n      }\n    })();\n  }\n  filterCameras() {\n    if (!this.searchTerm) {\n      this.filteredCamera = this.camera;\n    } else {\n      this.filteredCamera = this.camera.filter(camera => camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()) || camera.channelip.includes(this.searchTerm));\n    }\n  }\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n  static {\n    this.ɵfac = function IcccCameraComponent_Factory(t) {\n      return new (t || IcccCameraComponent)(i0.ɵɵdirectiveInject(i1.CameraService), i0.ɵɵdirectiveInject(i2.WebRTCService), i0.ɵɵdirectiveInject(i3.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IcccCameraComponent,\n      selectors: [[\"app-iccc-camera\"]],\n      viewQuery: function IcccCameraComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElem = _t.first);\n        }\n      },\n      decls: 15,\n      vars: 3,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\", \"col-lg-3\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\"], [1, \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Search cameras...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"camera-list\", 2, \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"class\", \"camera-item mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-lg-9\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"camera-item\", \"mb-2\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"mb-4\"], [1, \"video-grid\"], [\"class\", \"video-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"video-card\"], [\"class\", \"w-100\", \"style\", \"height: 200px; background: #000;\", \"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 3, \"id\", 4, \"ngIf\"], [\"class\", \"video-placeholder\", 4, \"ngIf\"], [\"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 1, \"w-100\", 2, \"height\", \"200px\", \"background\", \"#000\", 3, \"id\"], [1, \"video-placeholder\"]],\n      template: function IcccCameraComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h5\");\n          i0.ɵɵtext(6, \"Camera Search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function IcccCameraComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function IcccCameraComponent_Template_input_input_9_listener() {\n            return ctx.filterCameras();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵtemplate(11, IcccCameraComponent_div_11_Template, 5, 4, \"div\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\");\n          i0.ɵɵtemplate(14, IcccCameraComponent_div_14_Template, 4, 1, \"div\", 11);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredCamera);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.resultLive);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".video-grid[_ngcontent-%COMP%] {\\n      display: grid;\\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n      gap: 1rem;\\n    }\\n\\n    .video-card[_ngcontent-%COMP%] {\\n      border: 1px solid #ddd;\\n      border-radius: 8px;\\n      overflow: hidden;\\n    }\\n\\n    .video-placeholder[_ngcontent-%COMP%] {\\n      height: 200px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      background: #f8f9fa;\\n      color: #6c757d;\\n    }\\n\\n    .camera-item[_ngcontent-%COMP%] {\\n      padding: 0.5rem;\\n      border: 1px solid #e9ecef;\\n      border-radius: 4px;\\n      background: #f8f9fa;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9pY2NjLWNhbWVyYS5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLDREQUE0RDtNQUM1RCxTQUFTO0lBQ1g7O0lBRUE7TUFDRSxzQkFBc0I7TUFDdEIsa0JBQWtCO01BQ2xCLGdCQUFnQjtJQUNsQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixhQUFhO01BQ2IsbUJBQW1CO01BQ25CLHVCQUF1QjtNQUN2QixtQkFBbUI7TUFDbkIsY0FBYztJQUNoQjs7SUFFQTtNQUNFLGVBQWU7TUFDZix5QkFBeUI7TUFDekIsa0JBQWtCO01BQ2xCLG1CQUFtQjtJQUNyQiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC52aWRlby1ncmlkIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gICAgICBnYXA6IDFyZW07XG4gICAgfVxuXG4gICAgLnZpZGVvLWNhcmQge1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RkZDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgfVxuXG4gICAgLnZpZGVvLXBsYWNlaG9sZGVyIHtcbiAgICAgIGhlaWdodDogMjAwcHg7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIGNvbG9yOiAjNmM3NTdkO1xuICAgIH1cblxuICAgIC5jYW1lcmEtaXRlbSB7XG4gICAgICBwYWRkaW5nOiAwLjVyZW07XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAIA,SAASA,aAAa,QAAwB,yBAAyB;;;;;;;;;;;IAyBvDC,+BAAoE;IAO9DA;MAAA;MAAA;MAAA;MAAA,OAAUA,uDAA8B;IAAA,EAAC;IAL3CA,iBAK4C;IAC5CA,iCAAqE;IACnEA,YACF;IAAAA,iBAAQ;;;;IALNA,eAAmC;IAAnCA,oDAAmC;IAGLA,eAAoC;IAApCA,qDAAoC;IAClEA,eACF;IADEA,sDACF;;;;;IAoBIA,4BAQQ;;;;IANNA,iDAAgC;;;;;IAOlCA,+BAAuD;IAClDA,mCAAmB;IAAAA,iBAAI;;;;;IAhBlCA,+BAAwD;IAG9CA,YAAsB;IAAAA,iBAAK;IAEjCA,8BAAuB;IACrBA,sFAQQ;IACRA,kFAEM;IACRA,iBAAM;;;;IAfAA,eAAsB;IAAtBA,yCAAsB;IAIvBA,eAAoB;IAApBA,wCAAoB;IAQjBA,eAAqB;IAArBA,yCAAqB;;;;;IAlBvCA,8BAAoC;IAG9BA,4EAoBM;IACRA,iBAAM;;;;IArBkBA,eAAa;IAAbA,2CAAa;;;AA2DrD,OAAM,MAAOC,mBAAmB;EAQ9BC,YACSC,aAA4B,EAC3BC,aAA4B,EAC5BC,IAAgB;IAFjB,kBAAa,GAAbF,aAAa;IACZ,kBAAa,GAAbC,aAAa;IACb,SAAI,GAAJC,IAAI;IAPd,WAAM,GAAoB,EAAE;IAE5B,mBAAc,GAAU,EAAE;EAMvB;EAEHC,QAAQ;IACN,IAAI,CAACC,SAAS,EAAE;IAChBC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAK;MAC3CC,cAAc,CAACC,UAAU,CAAC,kBAAkB,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAJ,SAAS;IACP,MAAMK,UAAU,GAAGF,cAAc,CAACG,OAAO,CAAC,kBAAkB,CAAC;IAC7D,IAAID,UAAU,EAAE;MACd,IAAI,CAACE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MACpC,IAAI,CAACK,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL;MACA,IAAI,CAACA,MAAM,GAAG,CACZ,IAAIf,aAAa,CAAC,CAAC,EAAEmB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,EACnP,IAAInB,aAAa,CAAC,CAAC,EAAEmB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,EACnP,IAAInB,aAAa,CAAC,CAAC,EAAEmB,SAAS,EAAE,UAAU,EAAE,eAAe,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,OAAO,EAAE,WAAW,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,KAAK,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,CACpP;MACD,IAAI,CAACD,cAAc,GAAG,IAAI,CAACH,MAAM;MACjCJ,cAAc,CAACS,OAAO,CAAC,kBAAkB,EAAEJ,IAAI,CAACK,SAAS,CAAC,IAAI,CAACN,MAAM,CAAC,CAAC;;EAE3E;EAEA;EACAO,UAAU,CAACC,MAAmB,EAAEC,GAAW;IACzC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASH,GAAG,EAAE,CAAqB;IAEhF,IAAI,CAACD,MAAM,EAAE;MACXK,OAAO,CAACC,KAAK,CAAC,mCAAmCL,GAAG,EAAE,CAAC;MACvD;;IAGF,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACK,SAAS,GAAGP,MAAM;MAC/BE,YAAY,CAACM,IAAI,EAAE,CAACC,KAAK,CAACH,KAAK,IAAG;QAChCD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;MACFD,OAAO,CAACK,GAAG,CAAC,6BAA6BT,GAAG,EAAE,CAAC;;EAEnD;EAEAU,cAAc,CAACnB,MAAqB,EAAEoB,KAAU;IAC9C,MAAMC,OAAO,GAAID,KAAK,CAACE,MAA2B,CAACD,OAAO;IAC1D,IAAI,CAACE,YAAY,CAACvB,MAAM,CAACwB,QAAQ,EAAExB,MAAM,CAACyB,QAAQ,EAAEzB,MAAM,CAAC0B,SAAS,EAAE1B,MAAM,CAAC2B,SAAS,EAAEN,OAAO,CAAC;EAClG;EAEME,YAAY,CAACC,QAAiB,EAAEC,QAAiB,EAAEC,SAAkB,EAAEC,SAAkB,EAAEN,OAAiB;IAAA;IAAA;MAChH,IAAI,CAACA,OAAO,EAAE;QACZ,KAAI,CAACO,UAAU,CAACF,SAAS,EAAEC,SAAS,EAAEH,QAAQ,EAAEC,QAAQ,EAAEJ,OAAO,CAAC;OACnE,MAAM;QACL,MAAMQ,IAAI,GAAG,UAAUL,QAAQ,IAAIC,QAAQ,IAAIC,SAAS,WAAW;QACnE,IAAIjB,GAAG,GAAWqB,MAAM,CAACH,SAAS,CAAC;QAEnC,IAAI;UACF,MAAMI,QAAQ,SAAS,KAAI,CAACzC,aAAa,CAAC0C,iBAAiB,CAACH,IAAI,EAAEpB,GAAG,CAAC;UACtE,MAAMT,MAAM,GAAG,KAAI,CAACA,MAAM,CAACiC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACP,SAAS,KAAKA,SAAS,CAAC;UACvE,IAAI3B,MAAM,EAAE;YACVA,MAAM,CAACmC,SAAS,GAAGJ,QAAQ,CAACK,UAAU;YACtCpC,MAAM,CAACqC,QAAQ,GAAGN,QAAQ,CAACO,SAAS;YACpCtC,MAAM,CAACqB,OAAO,GAAG,IAAI;YAErB;YACAkB,UAAU,CAAC,MAAK;cACd,KAAI,CAACjD,aAAa,CAACkD,OAAO,CAACC,SAAS,CAACC,EAAE,IAAG;gBACxC,IAAIA,EAAE,EAAE;kBACN,KAAI,CAACnC,UAAU,CAACmC,EAAE,EAAEjC,GAAG,CAAC;;cAE5B,CAAC,CAAC;cACFI,OAAO,CAACK,GAAG,CAAC,kBAAkBT,GAAG,GAAG,EAAEsB,QAAQ,CAACK,UAAU,CAAC;YAC5D,CAAC,EAAE,IAAI,CAAC;;SAEX,CAAC,OAAOtB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;;IAExD;EACH;EAEMc,UAAU,CAACF,SAAkB,EAAEC,SAAkB,EAAEH,QAAiB,EAAEC,QAAiB,EAAEJ,OAAiB;IAAA;IAAA;MAC9G,MAAMQ,IAAI,GAAG,UAAUL,QAAQ,IAAIC,QAAQ,IAAIC,SAAS,WAAW;MACnE,IAAIjB,GAAG,GAAWqB,MAAM,CAACH,SAAS,CAAC;MAEnC,IAAI;QACF,MAAM,MAAI,CAACrC,aAAa,CAACqD,gBAAgB,CAACd,IAAI,EAAEpB,GAAG,CAAC;QACpD,MAAMT,MAAM,GAAG,MAAI,CAACA,MAAM,CAACiC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACP,SAAS,KAAKA,SAAS,CAAC;QACvE,IAAI3B,MAAM,EAAE;UACVA,MAAM,CAACmC,SAAS,GAAG/B,SAAS;UAC5BJ,MAAM,CAACqC,QAAQ,GAAGjC,SAAS;UAC3BJ,MAAM,CAACqB,OAAO,GAAG,KAAK;UAEtB;UACA,MAAMX,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASH,GAAG,EAAE,CAAqB;UAChF,IAAIC,YAAY,EAAE;YAChBA,YAAY,CAACK,SAAS,GAAG,IAAI;;;OAGlC,CAAC,OAAOD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEA8B,aAAa;IACX,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB,IAAI,CAAC1C,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL,IAAI,CAACG,cAAc,GAAG,IAAI,CAACH,MAAM,CAAC8C,MAAM,CAAC9C,MAAM,IAC7CA,MAAM,CAAC+C,WAAW,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACG,WAAW,EAAE,CAAC,IACxEhD,MAAM,CAAC0B,SAAS,CAACuB,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAAC,CAC3C;;EAEL;EAEA,IAAIK,UAAU;IACZ,OAAO,IAAI,CAAClD,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC8C,MAAM,CAACK,IAAI,IAAIA,IAAI,CAAC9B,OAAO,CAAC,GAAG,EAAE;EACpE;;;uBAhIWlC,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAiE;MAAAC;QAAA;;;;;;;;;;;;;UAvG5BnE,8BAA6B;UAMfA,6BAAa;UAAAA,iBAAK;UAExBA,8BAAuB;UAMjBA;YAAA;UAAA,EAAwB;YAAA,OACfoE,mBAAe;UAAA,EADA;UAJ1BpE,iBAK4B;UAI9BA,+BAAsE;UACpEA,sEAYM;UACRA,iBAAM;UAMZA,gCAA6B;UAEzBA,uEA0BM;UACRA,iBAAM;;;UAtDEA,eAAwB;UAAxBA,wCAAwB;UAMFA,eAAiB;UAAjBA,4CAAiB;UAqB3BA,eAAgB;UAAhBA,qCAAgB", "names": ["CameraChannel", "i0", "IcccCameraComponent", "constructor", "cameraService", "webrtcService", "http", "ngOnInit", "getCamera", "window", "addEventListener", "sessionStorage", "removeItem", "storedData", "getItem", "camera", "JSON", "parse", "filteredCamera", "undefined", "setItem", "stringify", "playWebRTC", "stream", "cam", "videoElement", "document", "getElementById", "console", "error", "srcObject", "play", "catch", "log", "onToggleStream", "event", "checked", "target", "toggleStream", "username", "password", "channelip", "channelid", "stopStream", "rtsp", "String", "response", "startCameraStream", "find", "data", "webrtcUrl", "webrtc_url", "streamId", "stream_id", "setTimeout", "stream$", "subscribe", "ms", "stopCameraStream", "filterCameras", "searchTerm", "filter", "channelname", "toLowerCase", "includes", "resultLive", "item", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\iccc-camera.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { CameraService } from '../services/camera.service';\nimport { WebRTCService } from '../services/webrtc.service';\nimport { CameraChannel, CameraResponse } from '../models/camera.models';\n\n@Component({\n  selector: 'app-iccc-camera',\n  template: `\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <!-- Search Section -->\n        <div class=\"col-12 col-lg-3\">\n          <div class=\"card\">\n            <div class=\"card-header\">\n              <h5>Camera Search</h5>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"mb-3\">\n                <input\n                  type=\"text\"\n                  class=\"form-control\"\n                  placeholder=\"Search cameras...\"\n                  [(ngModel)]=\"searchTerm\"\n                  (input)=\"filterCameras()\">\n              </div>\n\n              <!-- Camera List -->\n              <div class=\"camera-list\" style=\"max-height: 400px; overflow-y: auto;\">\n                <div *ngFor=\"let camera of filteredCamera\" class=\"camera-item mb-2\">\n                  <div class=\"form-check form-switch\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      [id]=\"'camera-' + camera.channelid\"\n                      [checked]=\"camera.checked\"\n                      (change)=\"onToggleStream(camera, $event)\">\n                    <label class=\"form-check-label\" [for]=\"'camera-' + camera.channelid\">\n                      {{camera.channelname}}\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Video Grid Section -->\n        <div class=\"col-12 col-lg-9\">\n          <div>\n            <div class=\"row\" *ngIf=\"resultLive\">\n              <div class=\"mb-4\">\n                <div class=\"video-grid\">\n                  <div *ngFor=\"let data of resultLive\" class=\"video-card\">\n                    <div class=\"card\">\n                      <div class=\"card-header\">\n                        <h6>{{ data.channelname }}</h6>\n                      </div>\n                      <div class=\"card-body\">\n                        <video\n                          *ngIf=\"data.webrtcUrl\"\n                          [id]=\"'video-' + data.channelid\"\n                          class=\"w-100\"\n                          style=\"height: 200px; background: #000;\"\n                          controls\n                          muted\n                          autoplay>\n                        </video>\n                        <div *ngIf=\"!data.webrtcUrl\" class=\"video-placeholder\">\n                          <p>No stream available</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .video-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n      gap: 1rem;\n    }\n\n    .video-card {\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .video-placeholder {\n      height: 200px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: #f8f9fa;\n      color: #6c757d;\n    }\n\n    .camera-item {\n      padding: 0.5rem;\n      border: 1px solid #e9ecef;\n      border-radius: 4px;\n      background: #f8f9fa;\n    }\n  `]\n})\nexport class IcccCameraComponent implements OnInit {\n  @ViewChild('videoElem') videoElem!: ElementRef<HTMLVideoElement>;\n\n  camerares!: CameraResponse;\n  camera: CameraChannel[] = [];\n  searchTerm: any;\n  filteredCamera: any[] = [];\n\n  constructor(\n    public cameraService: CameraService,\n    private webrtcService: WebRTCService,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    this.getCamera();\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [\n        new CameraChannel(1, undefined, 'Camera 1', '192.168.4.244', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined),\n        new CameraChannel(2, undefined, 'Camera 2', '192.168.4.243', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined),\n        new CameraChannel(3, undefined, 'Camera 3', '192.168.4.242', undefined, undefined, undefined, undefined, undefined, 'admin', 'Admin@123', undefined, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, undefined)\n      ];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n\n  // WebRTC play method similar to HLS playStream\n  playWebRTC(stream: MediaStream, cam: string): void {\n    const videoElement = document.getElementById(`video-${cam}`) as HTMLVideoElement;\n\n    if (!stream) {\n      console.error(`WebRTC stream not available for ${cam}`);\n      return;\n    }\n\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing WebRTC stream:', error);\n      });\n      console.log(`WebRTC stream started for ${cam}`);\n    }\n  }\n\n  onToggleStream(camera: CameraChannel, event: any) {\n    const checked = (event.target as HTMLInputElement).checked;\n    this.toggleStream(camera.username, camera.password, camera.channelip, camera.channelid, checked);\n  }\n\n  async toggleStream(username?: string, password?: string, channelip?: string, channelid?: number, checked?: boolean) {\n    if (!checked) {\n      this.stopStream(channelip, channelid, username, password, checked);\n    } else {\n      const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n      let cam: string = String(channelid);\n\n      try {\n        const response = await this.webrtcService.startCameraStream(rtsp, cam);\n        const camera = this.camera.find((data) => data.channelid === channelid);\n        if (camera) {\n          camera.webrtcUrl = response.webrtc_url;\n          camera.streamId = response.stream_id;\n          camera.checked = true;\n\n          // Subscribe to WebRTC stream and play it\n          setTimeout(() => {\n            this.webrtcService.stream$.subscribe(ms => {\n              if (ms) {\n                this.playWebRTC(ms, cam);\n              }\n            });\n            console.log(`WebRTC URL for ${cam}:`, response.webrtc_url);\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Error starting WebRTC stream:', error);\n      }\n    }\n  }\n\n  async stopStream(channelip?: string, channelid?: number, username?: string, password?: string, checked?: boolean) {\n    const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n    let cam: string = String(channelid);\n\n    try {\n      await this.webrtcService.stopCameraStream(rtsp, cam);\n      const camera = this.camera.find((data) => data.channelid === channelid);\n      if (camera) {\n        camera.webrtcUrl = undefined;\n        camera.streamId = undefined;\n        camera.checked = false;\n\n        // Stop video element\n        const videoElement = document.getElementById(`video-${cam}`) as HTMLVideoElement;\n        if (videoElement) {\n          videoElement.srcObject = null;\n        }\n      }\n    } catch (error) {\n      console.error('Error stopping WebRTC stream:', error);\n    }\n  }\n\n  filterCameras() {\n    if (!this.searchTerm) {\n      this.filteredCamera = this.camera;\n    } else {\n      this.filteredCamera = this.camera.filter(camera =>\n        camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        camera.channelip.includes(this.searchTerm)\n      );\n    }\n  }\n\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}