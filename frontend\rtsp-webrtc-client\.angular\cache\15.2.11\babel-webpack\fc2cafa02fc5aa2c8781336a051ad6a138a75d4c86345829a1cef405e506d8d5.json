{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class WebRTCService {\n  constructor() {\n    this.API_BASE_URL = process.env['API_BASE_URL'] || 'http://localhost:8081';\n    this.peerConnection = null;\n    this.websocket = null;\n    this.localStream = null;\n    this.connectionStateSubject = new BehaviorSubject('disconnected');\n    this.connectionState$ = this.connectionStateSubject.asObservable();\n    this.streamSubject = new BehaviorSubject(null);\n    this.stream$ = this.streamSubject.asObservable();\n  }\n  startStream(streamRequest) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Call the backend API to start the stream\n        const response = yield fetch(`${_this.API_BASE_URL}/start_stream`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(streamRequest)\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const streamResponse = yield response.json();\n        // Initialize WebRTC connection\n        yield _this.initializeWebRTC(streamResponse.stream_id);\n        return streamResponse;\n      } catch (error) {\n        console.error('Error starting stream:', error);\n        throw error;\n      }\n    })();\n  }\n  initializeWebRTC(streamId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Create peer connection\n        _this2.peerConnection = new RTCPeerConnection({\n          iceServers: [{\n            urls: 'stun:stun.l.google.com:19302'\n          }, {\n            urls: 'stun:stun1.l.google.com:19302'\n          }]\n        });\n        // Handle connection state changes\n        _this2.peerConnection.onconnectionstatechange = () => {\n          if (_this2.peerConnection) {\n            console.log('Connection state:', _this2.peerConnection.connectionState);\n            _this2.connectionStateSubject.next(_this2.peerConnection.connectionState);\n          }\n        };\n        // Handle incoming tracks (video stream)\n        _this2.peerConnection.ontrack = event => {\n          console.log('Received remote track:', event);\n          if (event.streams && event.streams[0]) {\n            _this2.streamSubject.next(event.streams[0]);\n          }\n        };\n        // Handle ICE candidates\n        _this2.peerConnection.onicecandidate = event => {\n          if (event.candidate && _this2.websocket) {\n            _this2.websocket.send(JSON.stringify({\n              type: 'ice-candidate',\n              candidate: event.candidate\n            }));\n          }\n        };\n        // Connect to WebSocket\n        yield _this2.connectWebSocket(streamId);\n      } catch (error) {\n        console.error('Error initializing WebRTC:', error);\n        throw error;\n      }\n    })();\n  }\n  connectWebSocket(streamId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        const wsUrl = `ws://localhost:8081/ws/${streamId}`;\n        _this3.websocket = new WebSocket(wsUrl);\n        _this3.websocket.onopen = /*#__PURE__*/_asyncToGenerator(function* () {\n          console.log('WebSocket connected');\n          try {\n            yield _this3.createOffer();\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n        _this3.websocket.onmessage = /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (event) {\n            try {\n              const message = JSON.parse(event.data);\n              yield _this3.handleWebSocketMessage(message);\n            } catch (error) {\n              console.error('Error handling WebSocket message:', error);\n            }\n          });\n          return function (_x) {\n            return _ref2.apply(this, arguments);\n          };\n        }();\n        _this3.websocket.onerror = error => {\n          console.error('WebSocket error:', error);\n          reject(error);\n        };\n        _this3.websocket.onclose = () => {\n          console.log('WebSocket disconnected');\n          _this3.connectionStateSubject.next('disconnected');\n        };\n      });\n    })();\n  }\n  createOffer() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.peerConnection || !_this4.websocket) {\n        throw new Error('Peer connection or WebSocket not initialized');\n      }\n      try {\n        // Create offer\n        const offer = yield _this4.peerConnection.createOffer({\n          offerToReceiveVideo: true,\n          offerToReceiveAudio: true\n        });\n        // Set local description\n        yield _this4.peerConnection.setLocalDescription(offer);\n        // Send offer to server\n        _this4.websocket.send(JSON.stringify({\n          type: 'offer',\n          sdp: offer.sdp\n        }));\n      } catch (error) {\n        console.error('Error creating offer:', error);\n        throw error;\n      }\n    })();\n  }\n  handleWebSocketMessage(message) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.peerConnection) {\n        return;\n      }\n      try {\n        switch (message.type) {\n          case 'answer':\n            const answer = new RTCSessionDescription({\n              type: 'answer',\n              sdp: message.sdp\n            });\n            yield _this5.peerConnection.setRemoteDescription(answer);\n            break;\n          case 'ice-candidate':\n            if (message.candidate) {\n              yield _this5.peerConnection.addIceCandidate(new RTCIceCandidate(message.candidate));\n            }\n            break;\n          default:\n            console.log('Unknown message type:', message.type);\n        }\n      } catch (error) {\n        console.error('Error handling WebSocket message:', error);\n      }\n    })();\n  }\n  stopStream(streamId) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Stop the stream on the server if streamId is provided\n        if (streamId) {\n          yield fetch(`${_this6.API_BASE_URL}/stop_stream/${streamId}`, {\n            method: 'DELETE'\n          });\n        }\n        // Close WebSocket\n        if (_this6.websocket) {\n          _this6.websocket.close();\n          _this6.websocket = null;\n        }\n        // Close peer connection\n        if (_this6.peerConnection) {\n          _this6.peerConnection.close();\n          _this6.peerConnection = null;\n        }\n        // Reset streams\n        _this6.streamSubject.next(null);\n        _this6.connectionStateSubject.next('disconnected');\n      } catch (error) {\n        console.error('Error stopping stream:', error);\n        throw error;\n      }\n    })();\n  }\n  getActiveStreams() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch(`${_this7.API_BASE_URL}/streams`);\n        return yield response.json();\n      } catch (error) {\n        console.error('Error getting active streams:', error);\n        throw error;\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function WebRTCService_Factory(t) {\n      return new (t || WebRTCService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WebRTCService,\n      factory: WebRTCService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAe,QAAoB,MAAM;;AAiBlD,OAAM,MAAOC,aAAa;EAYxBC;IAXiB,iBAAY,GAAGC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;IAC9E,mBAAc,GAA6B,IAAI;IAC/C,cAAS,GAAqB,IAAI;IAClC,gBAAW,GAAuB,IAAI;IAEtC,2BAAsB,GAAG,IAAIJ,eAAe,CAAS,cAAc,CAAC;IACrE,qBAAgB,GAAG,IAAI,CAACK,sBAAsB,CAACC,YAAY,EAAE;IAE5D,kBAAa,GAAG,IAAIN,eAAe,CAAqB,IAAI,CAAC;IAC9D,YAAO,GAAG,IAAI,CAACO,aAAa,CAACD,YAAY,EAAE;EAEnC;EAETE,WAAW,CAACC,aAA4B;IAAA;IAAA;MAC5C,IAAI;QACF;QACA,MAAMC,QAAQ,SAASC,KAAK,CAAC,GAAG,KAAI,CAACC,YAAY,eAAe,EAAE;UAChEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;WACjB;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,aAAa;SACnC,CAAC;QAEF,IAAI,CAACC,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBT,QAAQ,CAACU,MAAM,EAAE,CAAC;;QAG3D,MAAMC,cAAc,SAAyBX,QAAQ,CAACY,IAAI,EAAE;QAE5D;QACA,MAAM,KAAI,CAACC,gBAAgB,CAACF,cAAc,CAACG,SAAS,CAAC;QAErD,OAAOH,cAAc;OACtB,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;;IACZ;EACH;EAEcF,gBAAgB,CAACI,QAAgB;IAAA;IAAA;MAC7C,IAAI;QACF;QACA,MAAI,CAACC,cAAc,GAAG,IAAIC,iBAAiB,CAAC;UAC1CC,UAAU,EAAE,CACV;YAAEC,IAAI,EAAE;UAA8B,CAAE,EACxC;YAAEA,IAAI,EAAE;UAA+B,CAAE;SAE5C,CAAC;QAEF;QACA,MAAI,CAACH,cAAc,CAACI,uBAAuB,GAAG,MAAK;UACjD,IAAI,MAAI,CAACJ,cAAc,EAAE;YACvBF,OAAO,CAACO,GAAG,CAAC,mBAAmB,EAAE,MAAI,CAACL,cAAc,CAACM,eAAe,CAAC;YACrE,MAAI,CAAC7B,sBAAsB,CAAC8B,IAAI,CAAC,MAAI,CAACP,cAAc,CAACM,eAAe,CAAC;;QAEzE,CAAC;QAED;QACA,MAAI,CAACN,cAAc,CAACQ,OAAO,GAAIC,KAAK,IAAI;UACtCX,OAAO,CAACO,GAAG,CAAC,wBAAwB,EAAEI,KAAK,CAAC;UAC5C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;YACrC,MAAI,CAAC/B,aAAa,CAAC4B,IAAI,CAACE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;;QAE7C,CAAC;QAED;QACA,MAAI,CAACV,cAAc,CAACW,cAAc,GAAIF,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACG,SAAS,IAAI,MAAI,CAACC,SAAS,EAAE;YACrC,MAAI,CAACA,SAAS,CAACC,IAAI,CAAC1B,IAAI,CAACC,SAAS,CAAC;cACjC0B,IAAI,EAAE,eAAe;cACrBH,SAAS,EAAEH,KAAK,CAACG;aAClB,CAAC,CAAC;;QAEP,CAAC;QAED;QACA,MAAM,MAAI,CAACI,gBAAgB,CAACjB,QAAQ,CAAC;OAEtC,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;;IACZ;EACH;EAEcmB,gBAAgB,CAACjB,QAAgB;IAAA;IAAA;MAC7C,OAAO,IAAIkB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMC,KAAK,GAAG,0BAA0BrB,QAAQ,EAAE;QAClD,MAAI,CAACc,SAAS,GAAG,IAAIQ,SAAS,CAACD,KAAK,CAAC;QAErC,MAAI,CAACP,SAAS,CAACS,MAAM,kCAAG,aAAW;UACjCxB,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAC;UAClC,IAAI;YACF,MAAM,MAAI,CAACkB,WAAW,EAAE;YACxBL,OAAO,EAAE;WACV,CAAC,OAAOrB,KAAK,EAAE;YACdsB,MAAM,CAACtB,KAAK,CAAC;;QAEjB,CAAC;QAED,MAAI,CAACgB,SAAS,CAACW,SAAS;UAAA,8BAAG,WAAOf,KAAK,EAAI;YACzC,IAAI;cACF,MAAMgB,OAAO,GAAGrC,IAAI,CAACsC,KAAK,CAACjB,KAAK,CAACkB,IAAI,CAAC;cACtC,MAAM,MAAI,CAACC,sBAAsB,CAACH,OAAO,CAAC;aAC3C,CAAC,OAAO5B,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;UAE7D,CAAC;UAAA;YAAA;UAAA;QAAA;QAED,MAAI,CAACgB,SAAS,CAACgB,OAAO,GAAIhC,KAAK,IAAI;UACjCC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxCsB,MAAM,CAACtB,KAAK,CAAC;QACf,CAAC;QAED,MAAI,CAACgB,SAAS,CAACiB,OAAO,GAAG,MAAK;UAC5BhC,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAC;UACrC,MAAI,CAAC5B,sBAAsB,CAAC8B,IAAI,CAAC,cAAc,CAAC;QAClD,CAAC;MACH,CAAC,CAAC;IAAC;EACL;EAEcgB,WAAW;IAAA;IAAA;MACvB,IAAI,CAAC,MAAI,CAACvB,cAAc,IAAI,CAAC,MAAI,CAACa,SAAS,EAAE;QAC3C,MAAM,IAAItB,KAAK,CAAC,8CAA8C,CAAC;;MAGjE,IAAI;QACF;QACA,MAAMwC,KAAK,SAAS,MAAI,CAAC/B,cAAc,CAACuB,WAAW,CAAC;UAClDS,mBAAmB,EAAE,IAAI;UACzBC,mBAAmB,EAAE;SACtB,CAAC;QAEF;QACA,MAAM,MAAI,CAACjC,cAAc,CAACkC,mBAAmB,CAACH,KAAK,CAAC;QAEpD;QACA,MAAI,CAAClB,SAAS,CAACC,IAAI,CAAC1B,IAAI,CAACC,SAAS,CAAC;UACjC0B,IAAI,EAAE,OAAO;UACboB,GAAG,EAAEJ,KAAK,CAACI;SACZ,CAAC,CAAC;OAEJ,CAAC,OAAOtC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;;IACZ;EACH;EAEc+B,sBAAsB,CAACH,OAAY;IAAA;IAAA;MAC/C,IAAI,CAAC,MAAI,CAACzB,cAAc,EAAE;QACxB;;MAGF,IAAI;QACF,QAAQyB,OAAO,CAACV,IAAI;UAClB,KAAK,QAAQ;YACX,MAAMqB,MAAM,GAAG,IAAIC,qBAAqB,CAAC;cACvCtB,IAAI,EAAE,QAAQ;cACdoB,GAAG,EAAEV,OAAO,CAACU;aACd,CAAC;YACF,MAAM,MAAI,CAACnC,cAAc,CAACsC,oBAAoB,CAACF,MAAM,CAAC;YACtD;UAEF,KAAK,eAAe;YAClB,IAAIX,OAAO,CAACb,SAAS,EAAE;cACrB,MAAM,MAAI,CAACZ,cAAc,CAACuC,eAAe,CACvC,IAAIC,eAAe,CAACf,OAAO,CAACb,SAAS,CAAC,CACvC;;YAEH;UAEF;YACEd,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAEoB,OAAO,CAACV,IAAI,CAAC;QAAC;OAExD,CAAC,OAAOlB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;IAC1D;EACH;EAEM4C,UAAU,CAAC1C,QAAiB;IAAA;IAAA;MAChC,IAAI;QACF;QACA,IAAIA,QAAQ,EAAE;UACZ,MAAMhB,KAAK,CAAC,GAAG,MAAI,CAACC,YAAY,gBAAgBe,QAAQ,EAAE,EAAE;YAC1Dd,MAAM,EAAE;WACT,CAAC;;QAGJ;QACA,IAAI,MAAI,CAAC4B,SAAS,EAAE;UAClB,MAAI,CAACA,SAAS,CAAC6B,KAAK,EAAE;UACtB,MAAI,CAAC7B,SAAS,GAAG,IAAI;;QAGvB;QACA,IAAI,MAAI,CAACb,cAAc,EAAE;UACvB,MAAI,CAACA,cAAc,CAAC0C,KAAK,EAAE;UAC3B,MAAI,CAAC1C,cAAc,GAAG,IAAI;;QAG5B;QACA,MAAI,CAACrB,aAAa,CAAC4B,IAAI,CAAC,IAAI,CAAC;QAC7B,MAAI,CAAC9B,sBAAsB,CAAC8B,IAAI,CAAC,cAAc,CAAC;OAEjD,CAAC,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;;IACZ;EACH;EAEM8C,gBAAgB;IAAA;IAAA;MACpB,IAAI;QACF,MAAM7D,QAAQ,SAASC,KAAK,CAAC,GAAG,MAAI,CAACC,YAAY,UAAU,CAAC;QAC5D,aAAaF,QAAQ,CAACY,IAAI,EAAE;OAC7B,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;;IACZ;EACH;;;uBA3NWxB,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAuE,SAAbvE,aAAa;MAAAwE,YAFZ;IAAM;EAAA", "names": ["BehaviorSubject", "WebRTCService", "constructor", "process", "env", "connectionStateSubject", "asObservable", "streamSubject", "startStream", "streamRequest", "response", "fetch", "API_BASE_URL", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "streamResponse", "json", "initializeWebRTC", "stream_id", "error", "console", "streamId", "peerConnection", "RTCPeerConnection", "iceServers", "urls", "onconnectionstatechange", "log", "connectionState", "next", "ontrack", "event", "streams", "onicecandidate", "candidate", "websocket", "send", "type", "connectWebSocket", "Promise", "resolve", "reject", "wsUrl", "WebSocket", "onopen", "createOffer", "onmessage", "message", "parse", "data", "handleWebSocketMessage", "onerror", "onclose", "offer", "offerToReceiveVideo", "offerToReceiveAudio", "setLocalDescription", "sdp", "answer", "RTCSessionDescription", "setRemoteDescription", "addIceCandidate", "RTCIceCandidate", "stopStream", "close", "getActiveStreams", "factory", "providedIn"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\services\\webrtc.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport interface StreamRequest {\n  rtsp: string;\n  camera: number;\n}\n\nexport interface StreamResponse {\n  stream_id: string;\n  webrtc_url: string;\n  status: string;\n  camera: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WebRTCService {\n  private readonly API_BASE_URL = process.env['API_BASE_URL'] || 'http://localhost:8081';\n  private peerConnection: RTCPeerConnection | null = null;\n  private websocket: WebSocket | null = null;\n  private localStream: MediaStream | null = null;\n  \n  private connectionStateSubject = new BehaviorSubject<string>('disconnected');\n  public connectionState$ = this.connectionStateSubject.asObservable();\n  \n  private streamSubject = new BehaviorSubject<MediaStream | null>(null);\n  public stream$ = this.streamSubject.asObservable();\n\n  constructor() {}\n\n  async startStream(streamRequest: StreamRequest): Promise<StreamResponse> {\n    try {\n      // Call the backend API to start the stream\n      const response = await fetch(`${this.API_BASE_URL}/start_stream`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(streamRequest)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const streamResponse: StreamResponse = await response.json();\n      \n      // Initialize WebRTC connection\n      await this.initializeWebRTC(streamResponse.stream_id);\n      \n      return streamResponse;\n    } catch (error) {\n      console.error('Error starting stream:', error);\n      throw error;\n    }\n  }\n\n  private async initializeWebRTC(streamId: string): Promise<void> {\n    try {\n      // Create peer connection\n      this.peerConnection = new RTCPeerConnection({\n        iceServers: [\n          { urls: 'stun:stun.l.google.com:19302' },\n          { urls: 'stun:stun1.l.google.com:19302' }\n        ]\n      });\n\n      // Handle connection state changes\n      this.peerConnection.onconnectionstatechange = () => {\n        if (this.peerConnection) {\n          console.log('Connection state:', this.peerConnection.connectionState);\n          this.connectionStateSubject.next(this.peerConnection.connectionState);\n        }\n      };\n\n      // Handle incoming tracks (video stream)\n      this.peerConnection.ontrack = (event) => {\n        console.log('Received remote track:', event);\n        if (event.streams && event.streams[0]) {\n          this.streamSubject.next(event.streams[0]);\n        }\n      };\n\n      // Handle ICE candidates\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.websocket) {\n          this.websocket.send(JSON.stringify({\n            type: 'ice-candidate',\n            candidate: event.candidate\n          }));\n        }\n      };\n\n      // Connect to WebSocket\n      await this.connectWebSocket(streamId);\n      \n    } catch (error) {\n      console.error('Error initializing WebRTC:', error);\n      throw error;\n    }\n  }\n\n  private async connectWebSocket(streamId: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const wsUrl = `ws://localhost:8081/ws/${streamId}`;\n      this.websocket = new WebSocket(wsUrl);\n\n      this.websocket.onopen = async () => {\n        console.log('WebSocket connected');\n        try {\n          await this.createOffer();\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      };\n\n      this.websocket.onmessage = async (event) => {\n        try {\n          const message = JSON.parse(event.data);\n          await this.handleWebSocketMessage(message);\n        } catch (error) {\n          console.error('Error handling WebSocket message:', error);\n        }\n      };\n\n      this.websocket.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        reject(error);\n      };\n\n      this.websocket.onclose = () => {\n        console.log('WebSocket disconnected');\n        this.connectionStateSubject.next('disconnected');\n      };\n    });\n  }\n\n  private async createOffer(): Promise<void> {\n    if (!this.peerConnection || !this.websocket) {\n      throw new Error('Peer connection or WebSocket not initialized');\n    }\n\n    try {\n      // Create offer\n      const offer = await this.peerConnection.createOffer({\n        offerToReceiveVideo: true,\n        offerToReceiveAudio: true\n      });\n\n      // Set local description\n      await this.peerConnection.setLocalDescription(offer);\n\n      // Send offer to server\n      this.websocket.send(JSON.stringify({\n        type: 'offer',\n        sdp: offer.sdp\n      }));\n\n    } catch (error) {\n      console.error('Error creating offer:', error);\n      throw error;\n    }\n  }\n\n  private async handleWebSocketMessage(message: any): Promise<void> {\n    if (!this.peerConnection) {\n      return;\n    }\n\n    try {\n      switch (message.type) {\n        case 'answer':\n          const answer = new RTCSessionDescription({\n            type: 'answer',\n            sdp: message.sdp\n          });\n          await this.peerConnection.setRemoteDescription(answer);\n          break;\n\n        case 'ice-candidate':\n          if (message.candidate) {\n            await this.peerConnection.addIceCandidate(\n              new RTCIceCandidate(message.candidate)\n            );\n          }\n          break;\n\n        default:\n          console.log('Unknown message type:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n    }\n  }\n\n  async stopStream(streamId?: string): Promise<void> {\n    try {\n      // Stop the stream on the server if streamId is provided\n      if (streamId) {\n        await fetch(`${this.API_BASE_URL}/stop_stream/${streamId}`, {\n          method: 'DELETE'\n        });\n      }\n\n      // Close WebSocket\n      if (this.websocket) {\n        this.websocket.close();\n        this.websocket = null;\n      }\n\n      // Close peer connection\n      if (this.peerConnection) {\n        this.peerConnection.close();\n        this.peerConnection = null;\n      }\n\n      // Reset streams\n      this.streamSubject.next(null);\n      this.connectionStateSubject.next('disconnected');\n\n    } catch (error) {\n      console.error('Error stopping stream:', error);\n      throw error;\n    }\n  }\n\n  async getActiveStreams(): Promise<any> {\n    try {\n      const response = await fetch(`${this.API_BASE_URL}/streams`);\n      return await response.json();\n    } catch (error) {\n      console.error('Error getting active streams:', error);\n      throw error;\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}