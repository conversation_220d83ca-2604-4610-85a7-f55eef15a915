{"ast": null, "code": "// Models to match iccc-operator structure\nexport class CameraChannel {\n  constructor(channelid = null, status = null, channelname = null, channelip = null, channeltype = null, snapurl = null, majorurl = null, minorurl = null, analyticurl = null, username = null, password = null, latitude = null, longitude = null, description = null, recordingserverid = null, recordingservername = null, checked = null, hlsUrl = null, webrtcUrl = null, streamId = null, type = null) {\n    this.channelid = channelid;\n    this.status = status;\n    this.channelname = channelname;\n    this.channelip = channelip;\n    this.channeltype = channeltype;\n    this.snapurl = snapurl;\n    this.majorurl = majorurl;\n    this.minorurl = minorurl;\n    this.analyticurl = analyticurl;\n    this.username = username;\n    this.password = password;\n    this.latitude = latitude;\n    this.longitude = longitude;\n    this.description = description;\n    this.recordingserverid = recordingserverid;\n    this.recordingservername = recordingservername;\n    this.checked = checked;\n    this.hlsUrl = hlsUrl;\n    this.webrtcUrl = webrtcUrl;\n    this.streamId = streamId;\n    this.type = type;\n  }\n}", "map": {"version": 3, "mappings": "AAAA;AACA,OAAM,MAAOA,aAAa;EACxBC,YACSC,YAAoB,IAAI,EACxBC,SAAc,IAAI,EAClBC,cAAsB,IAAI,EAC1BC,YAAoB,IAAI,EACxBC,cAAsB,IAAI,EAC1BC,UAAkB,IAAI,EACtBC,WAAmB,IAAI,EACvBC,WAAmB,IAAI,EACvBC,cAAsB,IAAI,EAC1BC,WAAmB,IAAI,EACvBC,WAAmB,IAAI,EACvBC,WAAgB,IAAI,EACpBC,YAAiB,IAAI,EACrBC,cAAsB,IAAI,EAC1BC,oBAA4B,IAAI,EAChCC,sBAA8B,IAAI,EAClCC,UAAmB,IAAI,EACvBC,SAAiB,IAAI,EACrBC,YAAoB,IAAI,EACxBC,WAAmB,IAAI,EACvBC,OAAY,IAAI;IApBhB,cAAS,GAATpB,SAAS;IACT,WAAM,GAANC,MAAM;IACN,gBAAW,GAAXC,WAAW;IACX,cAAS,GAATC,SAAS;IACT,gBAAW,GAAXC,WAAW;IACX,YAAO,GAAPC,OAAO;IACP,aAAQ,GAARC,QAAQ;IACR,aAAQ,GAARC,QAAQ;IACR,gBAAW,GAAXC,WAAW;IACX,aAAQ,GAARC,QAAQ;IACR,aAAQ,GAARC,QAAQ;IACR,aAAQ,GAARC,QAAQ;IACR,cAAS,GAATC,SAAS;IACT,gBAAW,GAAXC,WAAW;IACX,sBAAiB,GAAjBC,iBAAiB;IACjB,wBAAmB,GAAnBC,mBAAmB;IACnB,YAAO,GAAPC,OAAO;IACP,WAAM,GAANC,MAAM;IACN,cAAS,GAATC,SAAS;IACT,aAAQ,GAARC,QAAQ;IACR,SAAI,GAAJC,IAAI;EACV", "names": ["CameraChannel", "constructor", "channelid", "status", "channelname", "channelip", "channeltype", "<PERSON>url", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "analyticurl", "username", "password", "latitude", "longitude", "description", "recordingserverid", "recordingservername", "checked", "hlsUrl", "webrtcUrl", "streamId", "type"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\models\\camera.models.ts"], "sourcesContent": ["// Models to match iccc-operator structure\nexport class CameraChannel {\n  constructor(\n    public channelid: number = null,\n    public status: any = null,\n    public channelname: string = null,\n    public channelip: string = null,\n    public channeltype: number = null,\n    public snapurl: string = null,\n    public majorurl: string = null,\n    public minorurl: string = null,\n    public analyticurl: string = null,\n    public username: string = null,\n    public password: string = null,\n    public latitude: any = null,\n    public longitude: any = null,\n    public description: string = null,\n    public recordingserverid: string = null,\n    public recordingservername: string = null,\n    public checked: boolean = null,\n    public hlsUrl: string = null,\n    public webrtcUrl: string = null,\n    public streamId: string = null,\n    public type: any = null\n  ) {}\n}\n\nexport interface CameraResponse {\n  result: CameraChannel[];\n  status: string;\n  message?: string;\n}\n\nexport interface StreamRequest {\n  rtsp: string;\n  camera: number;\n}\n\nexport interface StreamResponse {\n  stream_id: string;\n  webrtc_url: string;\n  status: string;\n  camera: number;\n}\n\nexport interface CameraStreamRequest {\n  rtsp: string;\n  cam: string;\n}\n\nexport interface CameraStreamResponse {\n  stream_id: string;\n  webrtc_url: string;\n  status: string;\n  cam: string;\n  hls_url: string; // Empty but kept for compatibility\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}