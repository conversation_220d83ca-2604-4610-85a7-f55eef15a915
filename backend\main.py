import asyncio
import json
import logging
import uuid
import threading
import queue
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Set, Optional
from contextlib import asynccontextmanager
import cv2
import numpy as np
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request
from pydantic import BaseModel
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from aiortc.contrib.media import MediaPlayer
from av import VideoFrame
import uvicorn
from urllib.parse import urlparse
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="RTSP to WebRTC Gateway")

# Enable CORS for Angular frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Angular dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global storage for peer connections and streams
peer_connections: Set[RTCPeerConnection] = set()
active_streams: Dict[str, 'RTSPVideoTrack'] = {}
websocket_connections: Dict[str, WebSocket] = {}

# Thread management
thread_pool = ThreadPoolExecutor(max_workers=10, thread_name_prefix="rtsp_capture")
stream_threads: Dict[str, threading.Thread] = {}
stream_stop_events: Dict[str, threading.Event] = {}
streams_lock = threading.RLock()  # Reentrant lock for thread-safe operations

class StreamRequest(BaseModel):
    rtsp: str
    camera: int

# Additional request model to match iccc-operator expectations
class CameraStreamRequest(BaseModel):
    rtsp: str
    cam: str

class RTSPVideoTrack(VideoStreamTrack):
    """
    A VideoStreamTrack that reads frames from an RTSP source via OpenCV using multithreading.
    Each stream runs on a separate thread to prevent blocking other streams.
    """
    def __init__(self, rtsp_url: str, stream_id: str):
        super().__init__()
        self.rtsp_url = rtsp_url
        self.stream_id = stream_id
        self.frame_queue = queue.Queue(maxsize=5)  # Buffer up to 5 frames
        self.stop_event = threading.Event()
        self.capture_thread = None
        self.cap = None
        self._is_running = False
        self._lock = threading.Lock()

        # Start the capture thread
        self._start_capture_thread()

    def _start_capture_thread(self):
        """Start the RTSP capture thread"""
        with self._lock:
            if not self._is_running:
                self.capture_thread = threading.Thread(
                    target=self._capture_frames,
                    name=f"rtsp_capture_{self.stream_id}",
                    daemon=True
                )
                self._is_running = True
                self.capture_thread.start()
                logger.info(f"Started capture thread for stream {self.stream_id}")

    def _capture_frames(self):
        """Capture frames in a separate thread"""
        frame_count = 0
        failed_reads = 0
        max_failed_reads = 10

        try:
            self._initialize_capture()
            logger.info(f"Starting frame capture loop for stream {self.stream_id}")

            while not self.stop_event.is_set():
                if self.cap is None or not self.cap.isOpened():
                    try:
                        logger.warning(f"Reinitializing capture for {self.stream_id}")
                        self._initialize_capture()
                        failed_reads = 0  # Reset failed read counter
                    except Exception as e:
                        logger.error(f"Failed to reinitialize capture for {self.stream_id}: {e}")
                        break

                ret, frame = self.cap.read()
                if not ret:
                    failed_reads += 1
                    logger.warning(f"Failed to read frame from RTSP stream {self.stream_id} (attempt {failed_reads}/{max_failed_reads})")

                    if failed_reads >= max_failed_reads:
                        logger.error(f"Too many failed reads for {self.stream_id}, stopping capture")
                        break

                    # Brief pause before retry
                    import time
                    time.sleep(0.1)
                    continue

                # Reset failed read counter on successful read
                failed_reads = 0
                frame_count += 1

                if frame_count % 100 == 0:  # Log every 100 frames
                    logger.info(f"Captured {frame_count} frames for stream {self.stream_id}")

                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                try:
                    # Put frame in queue (non-blocking, drop oldest if full)
                    if self.frame_queue.full():
                        try:
                            self.frame_queue.get_nowait()  # Remove oldest frame
                        except queue.Empty:
                            pass

                    self.frame_queue.put_nowait(frame)

                    if frame_count <= 5:  # Log first few frames
                        logger.info(f"Frame {frame_count} added to queue for stream {self.stream_id}")

                except queue.Full:
                    # Queue is still full, skip this frame
                    logger.warning(f"Frame queue full for stream {self.stream_id}, dropping frame")

        except Exception as e:
            logger.error(f"Error in capture thread for {self.stream_id}: {e}")
        finally:
            self._cleanup_capture()
            logger.info(f"Capture thread for stream {self.stream_id} ended after {frame_count} frames")

    def _initialize_capture(self):
        """Initialize the video capture with retry logic"""
        try:
            if self.cap:
                self.cap.release()

            # Create VideoCapture with additional options for better RTSP handling
            self.cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)

            # Set additional properties for better RTSP streaming
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_FPS, 25)
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))

            if not self.cap.isOpened():
                # Try alternative approach
                logger.warning(f"First attempt failed, trying alternative method for: {self.rtsp_url}")
                self.cap = cv2.VideoCapture(self.rtsp_url)
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                if not self.cap.isOpened():
                    raise ConnectionError(f"Failed to open RTSP stream: {self.rtsp_url}")

            # Test if we can read a frame
            ret, test_frame = self.cap.read()
            if not ret:
                raise ConnectionError(f"Cannot read frames from RTSP stream: {self.rtsp_url}")

            logger.info(f"Successfully connected to RTSP stream: {self.rtsp_url}")
            logger.info(f"Stream resolution: {int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")

        except Exception as e:
            logger.error(f"Failed to initialize RTSP capture: {e}")
            raise

    async def recv(self):
        """Receive the next video frame from the queue"""
        pts, time_base = await self.next_timestamp()

        try:
            # Get frame from queue with timeout
            frame = await asyncio.get_event_loop().run_in_executor(
                None, self._get_frame_from_queue
            )

            if frame is None:
                raise ConnectionError(f"No frames available for stream {self.stream_id}")

            # Create video frame
            video_frame = VideoFrame.from_ndarray(frame, format="rgb24")
            video_frame.pts = pts
            video_frame.time_base = time_base

            return video_frame

        except Exception as e:
            logger.error(f"Error receiving frame for stream {self.stream_id}: {e}")
            raise

    def _get_frame_from_queue(self):
        """Get frame from queue with timeout"""
        try:
            frame = self.frame_queue.get(timeout=2.0)  # Increased timeout
            return frame
        except queue.Empty:
            logger.warning(f"No frames in queue for stream {self.stream_id}, queue size: {self.frame_queue.qsize()}")
            return None

    def _cleanup_capture(self):
        """Clean up capture resources"""
        if self.cap:
            self.cap.release()
            self.cap = None

    def stop(self):
        """Clean up resources and stop the capture thread"""
        with self._lock:
            if self._is_running:
                logger.info(f"Stopping stream {self.stream_id}")
                self.stop_event.set()
                self._is_running = False

                # Wait for thread to finish (with timeout)
                if self.capture_thread and self.capture_thread.is_alive():
                    self.capture_thread.join(timeout=2.0)
                    if self.capture_thread.is_alive():
                        logger.warning(f"Capture thread for {self.stream_id} did not stop gracefully")

                self._cleanup_capture()

                # Clear the frame queue
                while not self.frame_queue.empty():
                    try:
                        self.frame_queue.get_nowait()
                    except queue.Empty:
                        break

# WebRTC endpoints that match iccc-operator expectations
@app.post("/start-stream")
async def start_camera_stream(request_data: CameraStreamRequest, request: Request):
    """Start WebRTC streaming from RTSP source (iccc-operator compatible)"""
    try:
        # Parse host and scheme from the incoming request
        parsed = urlparse(str(request.base_url))
        host = parsed.hostname or "localhost"
        port = parsed.port or 8081
        scheme = "wss" if parsed.scheme == "https" else "ws"

        # Generate unique stream ID
        stream_id = str(uuid.uuid4())

        # Create RTSP video track with thread safety
        with streams_lock:
            video_track = RTSPVideoTrack(request_data.rtsp, stream_id)
            active_streams[stream_id] = video_track

        # Construct WebRTC WebSocket signaling URL
        webrtc_url = f"{scheme}://{host}:{port}/ws/{stream_id}"

        return {
            "stream_id": stream_id,
            "webrtc_url": webrtc_url,
            "status": "stream_started",
            "cam": request_data.cam,
            "hls_url": ""  # Empty for compatibility, but we're using WebRTC
        }

    except Exception as e:
        logger.error(f"Failed to start stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start stream: {str(e)}")

@app.post("/stop-stream")
async def stop_camera_stream(request_data: CameraStreamRequest):
    """Stop WebRTC streaming (iccc-operator compatible)"""
    try:
        # Find and stop the stream for this camera with thread safety
        stream_to_stop = None
        with streams_lock:
            for stream_id, stream in active_streams.items():
                # You might want to store camera info with the stream for better matching
                stream_to_stop = stream_id
                break

            if stream_to_stop:
                active_streams[stream_to_stop].stop()
                del active_streams[stream_to_stop]

        return {
            "status": "stream_stopped",
            "cam": request_data.cam,
            "hls_url": ""  # Empty for compatibility
        }

    except Exception as e:
        logger.error(f"Failed to stop stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop stream: {str(e)}")

# Original WebRTC endpoints
@app.post("/start_stream")
async def start_webrtc_stream(request_data: StreamRequest, request: Request):
    """Start streaming from RTSP source"""
    try:
        # Parse host and scheme from the incoming request
        parsed = urlparse(str(request.base_url))
        host = parsed.hostname or "localhost"
        port = parsed.port or 8000
        scheme = "wss" if parsed.scheme == "https" else "ws"

        # Generate unique stream ID
        stream_id = str(uuid.uuid4())

        # Create RTSP video track with thread safety
        with streams_lock:
            video_track = RTSPVideoTrack(request_data.rtsp, stream_id)
            active_streams[stream_id] = video_track

        # Construct WebRTC WebSocket signaling URL
        webrtc_url = f"{scheme}://{host}:{port}/ws/{stream_id}"

        return {
            "stream_id": stream_id,
            "webrtc_url": webrtc_url,
            "status": "stream_started",
            "camera": request_data.camera
        }

    except Exception as e:
        logger.error(f"Failed to start stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start stream: {str(e)}")

@app.websocket("/ws/{stream_id}")
async def websocket_endpoint(websocket: WebSocket, stream_id: str):
    """WebSocket endpoint for WebRTC signaling"""
    await websocket.accept()
    websocket_connections[stream_id] = websocket

    try:
        # Create peer connection
        pc = RTCPeerConnection()
        peer_connections.add(pc)

        # Add video track if stream exists (thread-safe)
        with streams_lock:
            if stream_id in active_streams:
                pc.addTrack(active_streams[stream_id])

        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"Connection state is {pc.connectionState}")
            if pc.connectionState == "failed":
                await pc.close()
                peer_connections.discard(pc)

        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                logger.info(f"Received WebSocket message: {message['type']}")

                if message["type"] == "offer":
                    # Handle SDP offer
                    offer = RTCSessionDescription(
                        sdp=message["sdp"],
                        type=message["type"]
                    )

                    await pc.setRemoteDescription(offer)
                    answer = await pc.createAnswer()
                    await pc.setLocalDescription(answer)

                    # Send answer back to client
                    await websocket.send_text(json.dumps({
                        "type": "answer",
                        "sdp": pc.localDescription.sdp
                    }))

                elif message["type"] == "ice-candidate":
                    # Handle ICE candidate
                    if message.get("candidate"):
                        try:
                            from aiortc import RTCIceCandidate
                            candidate_data = message["candidate"]
                            logger.info(f"Received ICE candidate: {candidate_data}")

                            # Parse the candidate string properly
                            candidate_string = candidate_data["candidate"]
                            sdp_mid = candidate_data["sdpMid"]
                            sdp_mline_index = candidate_data["sdpMLineIndex"]

                            # Create RTCIceCandidate with positional arguments
                            candidate = RTCIceCandidate(
                                candidate_string,
                                sdp_mid,
                                sdp_mline_index
                            )
                            await pc.addIceCandidate(candidate)
                            logger.info("ICE candidate added successfully")
                        except Exception as e:
                            logger.error(f"Error handling ICE candidate: {e}")
                            logger.error(f"Candidate data: {message.get('candidate')}")

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break

    finally:
        # Cleanup
        websocket_connections.pop(stream_id, None)
        if pc in peer_connections:
            await pc.close()
            peer_connections.discard(pc)

@app.delete("/stop_stream/{stream_id}")
async def stop_stream(stream_id: str):
    """Stop a specific stream"""
    with streams_lock:
        if stream_id in active_streams:
            active_streams[stream_id].stop()
            del active_streams[stream_id]
            return {"status": "stream_stopped", "stream_id": stream_id}
        else:
            raise HTTPException(status_code=404, detail="Stream not found")

@app.get("/streams")
async def list_streams():
    """List all active streams"""
    return {
        "active_streams": list(active_streams.keys()),
        "count": len(active_streams)
    }

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    # Close all peer connections
    for pc in peer_connections:
        await pc.close()

    # Stop all WebRTC streams with thread safety
    with streams_lock:
        for stream in active_streams.values():
            stream.stop()
        active_streams.clear()

    peer_connections.clear()

    # Shutdown thread pool
    thread_pool.shutdown(wait=True, timeout=5.0)
    logger.info("All threads and resources cleaned up")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
