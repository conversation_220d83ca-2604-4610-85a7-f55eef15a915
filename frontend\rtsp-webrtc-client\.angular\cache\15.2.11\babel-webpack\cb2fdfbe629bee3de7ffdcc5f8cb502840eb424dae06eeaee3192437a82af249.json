{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/webrtc.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"videoElement\"];\nfunction StreamViewerComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2, \"Stream ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.currentStreamId);\n  }\n}\nfunction StreamViewerComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2, \"Active Streams:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.activeStreams == null ? null : ctx_r1.activeStreams.count) || 0);\n  }\n}\nfunction StreamViewerComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p\");\n    i0.ɵɵtext(2, \"No stream available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Configure RTSP URL and click \\\"Start Stream\\\" to begin\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StreamViewerComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function StreamViewerComponent_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clearError());\n    });\n    i0.ɵɵtext(6, \"Clear\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.errorMessage);\n  }\n}\nexport class StreamViewerComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    // Component properties\n    this.connectionState = 'disconnected';\n    this.currentStreamId = null;\n    this.activeStreams = null;\n    this.errorMessage = null;\n    this.hasStream = false;\n    // default Form data\n    this.rtspUrl = 'rtsp://admin:Admin@123@*************';\n    this.cameraId = 1;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n    // Subscribe to stream changes\n    this.subscriptions.push(this.webrtcService.stream$.subscribe(stream => {\n      if (stream && this.videoElement) {\n        this.videoElement.nativeElement.srcObject = stream;\n        this.hasStream = true;\n        this.clearError();\n      } else {\n        this.hasStream = false;\n        if (this.videoElement) {\n          this.videoElement.nativeElement.srcObject = null;\n        }\n      }\n    }));\n    // Load active streams on init\n    this.refreshStreams();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n  startStream() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.clearError();\n        if (!_this.rtspUrl.trim()) {\n          _this.errorMessage = 'Please enter a valid RTSP URL';\n          return;\n        }\n        const streamRequest = {\n          rtsp: _this.rtspUrl.trim(),\n          camera: _this.cameraId\n        };\n        const response = yield _this.webrtcService.startStream(streamRequest);\n        _this.currentStreamId = response.stream_id;\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this.errorMessage = `Failed to start stream: ${error}`;\n      }\n    })();\n  }\n  stopStream() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.webrtcService.stopStream(_this2.currentStreamId || undefined);\n        _this2.currentStreamId = null;\n        _this2.hasStream = false;\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this2.errorMessage = `Failed to stop stream: ${error}`;\n      }\n    })();\n  }\n  refreshStreams() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const streams = yield _this3.webrtcService.getActiveStreams();\n        _this3.activeStreams = streams;\n      } catch (error) {\n        console.error('Failed to refresh streams:', error);\n      }\n    })();\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  isConnecting() {\n    return this.connectionState === 'connecting';\n  }\n  isConnected() {\n    return this.connectionState === 'connected';\n  }\n  static {\n    this.ɵfac = function StreamViewerComponent_Factory(t) {\n      return new (t || StreamViewerComponent)(i0.ɵɵdirectiveInject(i1.WebRTCService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StreamViewerComponent,\n      selectors: [[\"app-stream-viewer\"]],\n      viewQuery: function StreamViewerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElement = _t.first);\n        }\n      },\n      decls: 39,\n      vars: 14,\n      consts: [[1, \"stream-container\"], [1, \"config-panel\"], [1, \"form-group\"], [\"for\", \"rtspUrl\"], [\"id\", \"rtspUrl\", \"type\", \"text\", \"placeholder\", \"rtsp://admin:Admin@123@*************\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"cameraId\"], [\"id\", \"cameraId\", \"type\", \"number\", \"placeholder\", \"1\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"button-group\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"status-panel\"], [1, \"status-item\"], [1, \"status-label\"], [\"class\", \"status-item\", 4, \"ngIf\"], [1, \"video-panel\"], [1, \"video-container\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", \"controls\", \"\", 1, \"video-player\"], [\"videoElement\", \"\"], [\"class\", \"video-placeholder\", 4, \"ngIf\"], [\"class\", \"error-panel\", 4, \"ngIf\"], [1, \"status-value\"], [1, \"video-placeholder\"], [1, \"error-panel\"], [1, \"error-message\"], [1, \"btn\", \"btn-small\", 3, \"click\"]],\n      template: function StreamViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"RTSP to WebRTC Stream Viewer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"h3\");\n          i0.ɵɵtext(5, \"Stream Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"label\", 3);\n          i0.ɵɵtext(8, \"RTSP URL:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function StreamViewerComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.rtspUrl = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 2)(11, \"label\", 5);\n          i0.ɵɵtext(12, \"Camera ID:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function StreamViewerComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.cameraId = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_15_listener() {\n            return ctx.startStream();\n          });\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_17_listener() {\n            return ctx.stopStream();\n          });\n          i0.ɵɵtext(18, \" Stop Stream \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_19_listener() {\n            return ctx.refreshStreams();\n          });\n          i0.ɵɵtext(20, \" Refresh Active Streams \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h3\");\n          i0.ɵɵtext(23, \"Connection Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"State:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, StreamViewerComponent_div_29_Template, 5, 1, \"div\", 14);\n          i0.ɵɵtemplate(30, StreamViewerComponent_div_30_Template, 5, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"h3\");\n          i0.ɵɵtext(33, \"Live Stream\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 16);\n          i0.ɵɵelement(35, \"video\", 17, 18);\n          i0.ɵɵtemplate(37, StreamViewerComponent_div_37_Template, 5, 0, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(38, StreamViewerComponent_div_38_Template, 7, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.rtspUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.cameraId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting() || ctx.isConnected());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isConnecting() ? \"Connecting...\" : \"Start Stream\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.isConnected() && !ctx.isConnecting());\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassMap(\"status-value status-\" + ctx.connectionState);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.connectionState, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStreamId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeStreams);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"hidden\", !ctx.hasStream);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasStream);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.NgIf, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".stream-container[_ngcontent-%COMP%] {\\n      max-width: 1200px;\\n      margin: 0 auto;\\n      padding: 20px;\\n      font-family: Arial, sans-serif;\\n    }\\n\\n    .config-panel[_ngcontent-%COMP%], .status-panel[_ngcontent-%COMP%], .video-panel[_ngcontent-%COMP%], .error-panel[_ngcontent-%COMP%] {\\n      background: #f5f5f5;\\n      border-radius: 8px;\\n      padding: 20px;\\n      margin-bottom: 20px;\\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n    }\\n\\n    .form-group[_ngcontent-%COMP%] {\\n      margin-bottom: 15px;\\n    }\\n\\n    .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n      display: block;\\n      margin-bottom: 5px;\\n      font-weight: bold;\\n    }\\n\\n    .form-control[_ngcontent-%COMP%] {\\n      width: 100%;\\n      padding: 8px 12px;\\n      border: 1px solid #ddd;\\n      border-radius: 4px;\\n      font-size: 14px;\\n    }\\n\\n    .button-group[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 10px;\\n      flex-wrap: wrap;\\n    }\\n\\n    .btn[_ngcontent-%COMP%] {\\n      padding: 10px 20px;\\n      border: none;\\n      border-radius: 4px;\\n      cursor: pointer;\\n      font-size: 14px;\\n      transition: background-color 0.3s;\\n    }\\n\\n    .btn[_ngcontent-%COMP%]:disabled {\\n      opacity: 0.6;\\n      cursor: not-allowed;\\n    }\\n\\n    .btn-primary[_ngcontent-%COMP%] {\\n      background-color: #007bff;\\n      color: white;\\n    }\\n\\n    .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      background-color: #0056b3;\\n    }\\n\\n    .btn-secondary[_ngcontent-%COMP%] {\\n      background-color: #6c757d;\\n      color: white;\\n    }\\n\\n    .btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      background-color: #545b62;\\n    }\\n\\n    .btn-info[_ngcontent-%COMP%] {\\n      background-color: #17a2b8;\\n      color: white;\\n    }\\n\\n    .btn-info[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      background-color: #117a8b;\\n    }\\n\\n    .btn-small[_ngcontent-%COMP%] {\\n      padding: 5px 10px;\\n      font-size: 12px;\\n    }\\n\\n    .status-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      margin-bottom: 10px;\\n    }\\n\\n    .status-label[_ngcontent-%COMP%] {\\n      font-weight: bold;\\n      min-width: 120px;\\n    }\\n\\n    .status-value[_ngcontent-%COMP%] {\\n      padding: 2px 8px;\\n      border-radius: 4px;\\n    }\\n\\n    .status-connected[_ngcontent-%COMP%] {\\n      background-color: #d4edda;\\n      color: #155724;\\n    }\\n\\n    .status-connecting[_ngcontent-%COMP%] {\\n      background-color: #fff3cd;\\n      color: #856404;\\n    }\\n\\n    .status-disconnected[_ngcontent-%COMP%] {\\n      background-color: #f8d7da;\\n      color: #721c24;\\n    }\\n\\n    .video-container[_ngcontent-%COMP%] {\\n      position: relative;\\n      background: #000;\\n      border-radius: 4px;\\n      overflow: hidden;\\n    }\\n\\n    .video-player[_ngcontent-%COMP%] {\\n      width: 100%;\\n      height: auto;\\n      max-height: 600px;\\n      display: block;\\n    }\\n\\n    .video-player.hidden[_ngcontent-%COMP%] {\\n      display: none;\\n    }\\n\\n    .video-placeholder[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      justify-content: center;\\n      height: 300px;\\n      color: #666;\\n      text-align: center;\\n    }\\n\\n    .error-panel[_ngcontent-%COMP%] {\\n      background-color: #f8d7da;\\n      border: 1px solid #f5c6cb;\\n    }\\n\\n    .error-message[_ngcontent-%COMP%] {\\n      color: #721c24;\\n      margin-bottom: 10px;\\n    }\\n\\n    h1[_ngcontent-%COMP%], h3[_ngcontent-%COMP%] {\\n      color: #333;\\n      margin-bottom: 15px;\\n    }\\n\\n    h1[_ngcontent-%COMP%] {\\n      text-align: center;\\n      border-bottom: 2px solid #007bff;\\n      padding-bottom: 10px;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;IAuEQA,+BAAiD;IACpBA,0BAAU;IAAAA,iBAAO;IAC5CA,gCAA2B;IAAAA,YAAqB;IAAAA,iBAAO;;;;IAA5BA,eAAqB;IAArBA,4CAAqB;;;;;IAGlDA,+BAA+C;IAClBA,+BAAe;IAAAA,iBAAO;IACjDA,gCAA2B;IAAAA,YAA+B;IAAAA,iBAAO;;;;IAAtCA,eAA+B;IAA/BA,6FAA+B;;;;;IAkB1DA,+BAAkD;IAC7CA,mCAAmB;IAAAA,iBAAI;IAC1BA,yBAAG;IAAAA,sEAAoD;IAAAA,iBAAI;;;;;;IAMjEA,+BAA8C;IACxCA,qBAAK;IAAAA,iBAAK;IACdA,6BAAyB;IAAAA,YAAkB;IAAAA,iBAAI;IAC/CA,kCAAqD;IAA7CA;MAAAA;MAAA;MAAA,OAASA,kCAAY;IAAA,EAAC;IAAuBA,qBAAK;IAAAA,iBAAS;;;;IAD1CA,eAAkB;IAAlBA,yCAAkB;;;AA2KnD,OAAM,MAAOC,qBAAqB;EAgBhCC,YAAoBC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAbjC;IACA,oBAAe,GAAW,cAAc;IACxC,oBAAe,GAAkB,IAAI;IACrC,kBAAa,GAAQ,IAAI;IACzB,iBAAY,GAAkB,IAAI;IAClC,cAAS,GAAY,KAAK;IAE1B;IACA,YAAO,GAAG,sCAAsC;IAChD,aAAQ,GAAG,CAAC;IAEJ,kBAAa,GAAmB,EAAE;EAES;EAEnDC,QAAQ;IACN;IACA,IAAI,CAACC,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACI,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACJ,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACQ,OAAO,CAACH,SAAS,CAACI,MAAM,IAAG;MAC5C,IAAIA,MAAM,IAAI,IAAI,CAACC,YAAY,EAAE;QAC/B,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAGH,MAAM;QAClD,IAAI,CAACI,SAAS,GAAG,IAAI;QACrB,IAAI,CAACC,UAAU,EAAE;OAClB,MAAM;QACL,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,IAAI,IAAI,CAACH,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI;;;IAGtD,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACG,cAAc,EAAE;EACvB;EAEAC,WAAW;IACT,IAAI,CAACd,aAAa,CAACe,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEMC,WAAW;IAAA;IAAA;MACf,IAAI;QACF,KAAI,CAACP,UAAU,EAAE;QAEjB,IAAI,CAAC,KAAI,CAACQ,OAAO,CAACC,IAAI,EAAE,EAAE;UACxB,KAAI,CAACC,YAAY,GAAG,+BAA+B;UACnD;;QAGF,MAAMC,aAAa,GAAkB;UACnCC,IAAI,EAAE,KAAI,CAACJ,OAAO,CAACC,IAAI,EAAE;UACzBI,MAAM,EAAE,KAAI,CAACC;SACd;QAED,MAAMC,QAAQ,SAAyB,KAAI,CAAC7B,aAAa,CAACqB,WAAW,CAACI,aAAa,CAAC;QACpF,KAAI,CAACK,eAAe,GAAGD,QAAQ,CAACE,SAAS;QAEzCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,QAAQ,CAAC;OAEtD,CAAC,OAAOK,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,KAAI,CAACV,YAAY,GAAG,2BAA2BU,KAAK,EAAE;;IACvD;EACH;EAEMd,UAAU;IAAA;IAAA;MACd,IAAI;QACF,MAAM,MAAI,CAACpB,aAAa,CAACoB,UAAU,CAAC,MAAI,CAACU,eAAe,IAAIK,SAAS,CAAC;QACtE,MAAI,CAACL,eAAe,GAAG,IAAI;QAC3B,MAAI,CAACjB,SAAS,GAAG,KAAK;QACtBmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACV,YAAY,GAAG,0BAA0BU,KAAK,EAAE;;IACtD;EACH;EAEMnB,cAAc;IAAA;IAAA;MAClB,IAAI;QACF,MAAMqB,OAAO,SAAS,MAAI,CAACpC,aAAa,CAACqC,gBAAgB,EAAE;QAC3D,MAAI,CAACC,aAAa,GAAGF,OAAO;OAC7B,CAAC,OAAOF,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAEApB,UAAU;IACR,IAAI,CAACU,YAAY,GAAG,IAAI;EAC1B;EAEAe,YAAY;IACV,OAAO,IAAI,CAAChC,eAAe,KAAK,YAAY;EAC9C;EAEAiC,WAAW;IACT,OAAO,IAAI,CAACjC,eAAe,KAAK,WAAW;EAC7C;;;uBA3GWT,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAA2C;MAAAC;QAAA;;;;;;;;;;;;;UA9Q9B7C,8BAA8B;UACxBA,4CAA4B;UAAAA,iBAAK;UAGrCA,8BAA0B;UACpBA,oCAAoB;UAAAA,iBAAK;UAC7BA,8BAAwB;UACDA,yBAAS;UAAAA,iBAAQ;UACtCA,gCAME;UAHAA;YAAA;UAAA,EAAqB;UAHvBA,iBAME;UAGJA,+BAAwB;UACAA,2BAAU;UAAAA,iBAAQ;UACxCA,iCAME;UAHAA;YAAA;UAAA,EAAsB;UAHxBA,iBAME;UAGJA,+BAA0B;UAEtBA;YAAA,OAAS8C,iBAAa;UAAA,EAAC;UAIvB9C,aACF;UAAAA,iBAAS;UAETA,kCAIC;UAHCA;YAAA,OAAS8C,gBAAY;UAAA,EAAC;UAItB9C,8BACF;UAAAA,iBAAS;UAETA,mCAGC;UAFCA;YAAA,OAAS8C,oBAAgB;UAAA,EAAC;UAG1B9C,yCACF;UAAAA,iBAAS;UAKbA,gCAA0B;UACpBA,kCAAiB;UAAAA,iBAAK;UAC1BA,gCAAyB;UACIA,uBAAM;UAAAA,iBAAO;UACxCA,6BAAyD;UACvDA,aACF;UAAAA,iBAAO;UAGTA,yEAGM;UAENA,yEAGM;UACRA,iBAAM;UAGNA,gCAAyB;UACnBA,4BAAW;UAAAA,iBAAK;UACpBA,gCAA6B;UAC3BA,iCAQS;UAETA,yEAGM;UACRA,iBAAM;UAIRA,yEAIM;UACRA,iBAAM;;;UA3FEA,eAAqB;UAArBA,qCAAqB;UAWrBA,eAAsB;UAAtBA,sCAAsB;UAStBA,eAA4C;UAA5CA,kEAA4C;UAG5CA,eACF;UADEA,sFACF;UAIEA,eAA8C;UAA9CA,oEAA8C;UAoB1CA,gBAAkD;UAAlDA,2DAAkD;UACtDA,eACF;UADEA,oDACF;UAGwBA,eAAqB;UAArBA,0CAAqB;UAKrBA,eAAmB;UAAnBA,wCAAmB;UAiBzCA,eAA2B;UAA3BA,wCAA2B;UAGGA,eAAgB;UAAhBA,qCAAgB;UAQ1BA,eAAkB;UAAlBA,uCAAkB", "names": ["i0", "StreamViewerComponent", "constructor", "webrtcService", "ngOnInit", "subscriptions", "push", "connectionState$", "subscribe", "state", "connectionState", "stream$", "stream", "videoElement", "nativeElement", "srcObject", "hasStream", "clearError", "refreshStreams", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "stopStream", "startStream", "rtspUrl", "trim", "errorMessage", "streamRequest", "rtsp", "camera", "cameraId", "response", "currentStreamId", "stream_id", "console", "log", "error", "undefined", "streams", "getActiveStreams", "activeStreams", "isConnecting", "isConnected", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\stream-viewer.component.ts"], "sourcesContent": ["import { Component, ElementRef, ViewChild, OnInit, OnDestroy } from '@angular/core';\nimport { WebRTCService, StreamRequest, StreamResponse } from '../services/webrtc.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-stream-viewer',\n  template: `\n    <div class=\"stream-container\">\n      <h1>RTSP to WebRTC Stream Viewer</h1>\n\n      <!-- Stream Configuration -->\n      <div class=\"config-panel\">\n        <h3>Stream Configuration</h3>\n        <div class=\"form-group\">\n          <label for=\"rtspUrl\">RTSP URL:</label>\n          <input\n            id=\"rtspUrl\"\n            type=\"text\"\n            [(ngModel)]=\"rtspUrl\"\n            placeholder=\"rtsp://admin:Admin@123@*************\"\n            class=\"form-control\"\n          />\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"cameraId\">Camera ID:</label>\n          <input\n            id=\"cameraId\"\n            type=\"number\"\n            [(ngModel)]=\"cameraId\"\n            placeholder=\"1\"\n            class=\"form-control\"\n          />\n        </div>\n\n        <div class=\"button-group\">\n          <button\n            (click)=\"startStream()\"\n            [disabled]=\"isConnecting() || isConnected()\"\n            class=\"btn btn-primary\"\n          >\n            {{ isConnecting() ? 'Connecting...' : 'Start Stream' }}\n          </button>\n\n          <button\n            (click)=\"stopStream()\"\n            [disabled]=\"!isConnected() && !isConnecting()\"\n            class=\"btn btn-secondary\"\n          >\n            Stop Stream\n          </button>\n\n          <button\n            (click)=\"refreshStreams()\"\n            class=\"btn btn-info\"\n          >\n            Refresh Active Streams\n          </button>\n        </div>\n      </div>\n\n      <!-- Connection Status -->\n      <div class=\"status-panel\">\n        <h3>Connection Status</h3>\n        <div class=\"status-item\">\n          <span class=\"status-label\">State:</span>\n          <span [class]=\"'status-value status-' + connectionState\">\n            {{ connectionState }}\n          </span>\n        </div>\n\n        <div class=\"status-item\" *ngIf=\"currentStreamId\">\n          <span class=\"status-label\">Stream ID:</span>\n          <span class=\"status-value\">{{ currentStreamId }}</span>\n        </div>\n\n        <div class=\"status-item\" *ngIf=\"activeStreams\">\n          <span class=\"status-label\">Active Streams:</span>\n          <span class=\"status-value\">{{ activeStreams?.count || 0 }}</span>\n        </div>\n      </div>\n\n      <!-- Video Player -->\n      <div class=\"video-panel\">\n        <h3>Live Stream</h3>\n        <div class=\"video-container\">\n          <video\n            #videoElement\n            autoplay\n            playsinline\n            muted\n            controls\n            class=\"video-player\"\n            [class.hidden]=\"!hasStream\"\n          ></video>\n\n          <div class=\"video-placeholder\" *ngIf=\"!hasStream\">\n            <p>No stream available</p>\n            <p>Configure RTSP URL and click \"Start Stream\" to begin</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Error Display -->\n      <div class=\"error-panel\" *ngIf=\"errorMessage\">\n        <h3>Error</h3>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n        <button (click)=\"clearError()\" class=\"btn btn-small\">Clear</button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stream-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n      font-family: Arial, sans-serif;\n    }\n\n    .config-panel, .status-panel, .video-panel, .error-panel {\n      background: #f5f5f5;\n      border-radius: 8px;\n      padding: 20px;\n      margin-bottom: 20px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .form-group {\n      margin-bottom: 15px;\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: 5px;\n      font-weight: bold;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 10px;\n      flex-wrap: wrap;\n    }\n\n    .btn {\n      padding: 10px 20px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-size: 14px;\n      transition: background-color 0.3s;\n    }\n\n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .btn-primary {\n      background-color: #007bff;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background-color: #0056b3;\n    }\n\n    .btn-secondary {\n      background-color: #6c757d;\n      color: white;\n    }\n\n    .btn-secondary:hover:not(:disabled) {\n      background-color: #545b62;\n    }\n\n    .btn-info {\n      background-color: #17a2b8;\n      color: white;\n    }\n\n    .btn-info:hover:not(:disabled) {\n      background-color: #117a8b;\n    }\n\n    .btn-small {\n      padding: 5px 10px;\n      font-size: 12px;\n    }\n\n    .status-item {\n      display: flex;\n      margin-bottom: 10px;\n    }\n\n    .status-label {\n      font-weight: bold;\n      min-width: 120px;\n    }\n\n    .status-value {\n      padding: 2px 8px;\n      border-radius: 4px;\n    }\n\n    .status-connected {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-connecting {\n      background-color: #fff3cd;\n      color: #856404;\n    }\n\n    .status-disconnected {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .video-container {\n      position: relative;\n      background: #000;\n      border-radius: 4px;\n      overflow: hidden;\n    }\n\n    .video-player {\n      width: 100%;\n      height: auto;\n      max-height: 600px;\n      display: block;\n    }\n\n    .video-player.hidden {\n      display: none;\n    }\n\n    .video-placeholder {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 300px;\n      color: #666;\n      text-align: center;\n    }\n\n    .error-panel {\n      background-color: #f8d7da;\n      border: 1px solid #f5c6cb;\n    }\n\n    .error-message {\n      color: #721c24;\n      margin-bottom: 10px;\n    }\n\n    h1, h3 {\n      color: #333;\n      margin-bottom: 15px;\n    }\n\n    h1 {\n      text-align: center;\n      border-bottom: 2px solid #007bff;\n      padding-bottom: 10px;\n    }\n  `]\n})\nexport class StreamViewerComponent implements OnInit, OnDestroy {\n  @ViewChild('videoElement', { static: true }) videoElement!: ElementRef<HTMLVideoElement>;\n\n  // Component properties\n  connectionState: string = 'disconnected';\n  currentStreamId: string | null = null;\n  activeStreams: any = null;\n  errorMessage: string | null = null;\n  hasStream: boolean = false;\n\n  // default Form data\n  rtspUrl = 'rtsp://admin:Admin@123@*************';\n  cameraId = 1;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(private webrtcService: WebRTCService) {}\n\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n\n    // Subscribe to stream changes\n    this.subscriptions.push(\n      this.webrtcService.stream$.subscribe(stream => {\n        if (stream && this.videoElement) {\n          this.videoElement.nativeElement.srcObject = stream;\n          this.hasStream = true;\n          this.clearError();\n        } else {\n          this.hasStream = false;\n          if (this.videoElement) {\n            this.videoElement.nativeElement.srcObject = null;\n          }\n        }\n      })\n    );\n\n    // Load active streams on init\n    this.refreshStreams();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n\n  async startStream() {\n    try {\n      this.clearError();\n\n      if (!this.rtspUrl.trim()) {\n        this.errorMessage = 'Please enter a valid RTSP URL';\n        return;\n      }\n\n      const streamRequest: StreamRequest = {\n        rtsp: this.rtspUrl.trim(),\n        camera: this.cameraId\n      };\n\n      const response: StreamResponse = await this.webrtcService.startStream(streamRequest);\n      this.currentStreamId = response.stream_id;\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream: ${error}`;\n    }\n  }\n\n  async stopStream() {\n    try {\n      await this.webrtcService.stopStream(this.currentStreamId || undefined);\n      this.currentStreamId = null;\n      this.hasStream = false;\n      console.log('Stream stopped successfully');\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream: ${error}`;\n    }\n  }\n\n  async refreshStreams() {\n    try {\n      const streams = await this.webrtcService.getActiveStreams();\n      this.activeStreams = streams;\n    } catch (error) {\n      console.error('Failed to refresh streams:', error);\n    }\n  }\n\n  clearError() {\n    this.errorMessage = null;\n  }\n\n  isConnecting(): boolean {\n    return this.connectionState === 'connecting';\n  }\n\n  isConnected(): boolean {\n    return this.connectionState === 'connected';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}