{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/webrtc.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"videoElement\"];\nfunction StreamViewerComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2, \"Stream ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.currentStreamId);\n  }\n}\nfunction StreamViewerComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2, \"Active Streams:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.activeStreams == null ? null : ctx_r1.activeStreams.count) || 0);\n  }\n}\nfunction StreamViewerComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p\");\n    i0.ɵɵtext(2, \"No stream available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Configure RTSP URL and click \\\"Start Stream\\\" to begin\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StreamViewerComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function StreamViewerComponent_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clearError());\n    });\n    i0.ɵɵtext(6, \"Clear\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.errorMessage);\n  }\n}\nexport class StreamViewerComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    // Component properties\n    this.connectionState = 'disconnected';\n    this.currentStreamId = null;\n    this.activeStreams = null;\n    this.errorMessage = null;\n    this.hasStream = false;\n    // default Form data\n    this.rtspUrl = 'rtsp://admin:Admin@123@*************';\n    this.cameraId = 1;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n    // Subscribe to stream changes\n    this.subscriptions.push(this.webrtcService.stream$.subscribe(stream => {\n      if (stream && this.videoElement) {\n        this.videoElement.nativeElement.srcObject = stream;\n        this.hasStream = true;\n        this.clearError();\n      } else {\n        this.hasStream = false;\n        if (this.videoElement) {\n          this.videoElement.nativeElement.srcObject = null;\n        }\n      }\n    }));\n    // Load active streams on init\n    this.refreshStreams();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n  startStream() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.clearError();\n        if (!_this.rtspUrl.trim()) {\n          _this.errorMessage = 'Please enter a valid RTSP URL';\n          return;\n        }\n        const streamRequest = {\n          rtsp: _this.rtspUrl.trim(),\n          camera: _this.cameraId\n        };\n        const response = yield _this.webrtcService.startStream(streamRequest);\n        _this.currentStreamId = response.stream_id;\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this.errorMessage = `Failed to start stream: ${error}`;\n      }\n    })();\n  }\n  stopStream() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.webrtcService.stopStream(_this2.currentStreamId || undefined);\n        _this2.currentStreamId = null;\n        _this2.hasStream = false;\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this2.errorMessage = `Failed to stop stream: ${error}`;\n      }\n    })();\n  }\n  refreshStreams() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const streams = yield _this3.webrtcService.getActiveStreams();\n        _this3.activeStreams = streams;\n      } catch (error) {\n        console.error('Failed to refresh streams:', error);\n      }\n    })();\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  isConnecting() {\n    return this.connectionState === 'connecting';\n  }\n  isConnected() {\n    return this.connectionState === 'connected';\n  }\n  static {\n    this.ɵfac = function StreamViewerComponent_Factory(t) {\n      return new (t || StreamViewerComponent)(i0.ɵɵdirectiveInject(i1.WebRTCService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StreamViewerComponent,\n      selectors: [[\"app-stream-viewer\"]],\n      viewQuery: function StreamViewerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElement = _t.first);\n        }\n      },\n      decls: 39,\n      vars: 14,\n      consts: [[1, \"stream-container\"], [1, \"config-panel\"], [1, \"form-group\"], [\"for\", \"rtspUrl\"], [\"id\", \"rtspUrl\", \"type\", \"text\", \"placeholder\", \"rtsp://admin:Admin@123@*************\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"cameraId\"], [\"id\", \"cameraId\", \"type\", \"number\", \"placeholder\", \"1\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"button-group\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"status-panel\"], [1, \"status-item\"], [1, \"status-label\"], [\"class\", \"status-item\", 4, \"ngIf\"], [1, \"video-panel\"], [1, \"video-container\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", \"controls\", \"\", 1, \"video-player\"], [\"videoElement\", \"\"], [\"class\", \"video-placeholder\", 4, \"ngIf\"], [\"class\", \"error-panel\", 4, \"ngIf\"], [1, \"status-value\"], [1, \"video-placeholder\"], [1, \"error-panel\"], [1, \"error-message\"], [1, \"btn\", \"btn-small\", 3, \"click\"]],\n      template: function StreamViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"RTSP to WebRTC Stream Viewer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"h3\");\n          i0.ɵɵtext(5, \"Stream Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"label\", 3);\n          i0.ɵɵtext(8, \"RTSP URL:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function StreamViewerComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.rtspUrl = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 2)(11, \"label\", 5);\n          i0.ɵɵtext(12, \"Camera ID:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function StreamViewerComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.cameraId = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_15_listener() {\n            return ctx.startStream();\n          });\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_17_listener() {\n            return ctx.stopStream();\n          });\n          i0.ɵɵtext(18, \" Stop Stream \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function StreamViewerComponent_Template_button_click_19_listener() {\n            return ctx.refreshStreams();\n          });\n          i0.ɵɵtext(20, \" Refresh Active Streams \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h3\");\n          i0.ɵɵtext(23, \"Connection Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"State:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, StreamViewerComponent_div_29_Template, 5, 1, \"div\", 14);\n          i0.ɵɵtemplate(30, StreamViewerComponent_div_30_Template, 5, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"h3\");\n          i0.ɵɵtext(33, \"Live Stream\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 16);\n          i0.ɵɵelement(35, \"video\", 17, 18);\n          i0.ɵɵtemplate(37, StreamViewerComponent_div_37_Template, 5, 0, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(38, StreamViewerComponent_div_38_Template, 7, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.rtspUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.cameraId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting() || ctx.isConnected());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isConnecting() ? \"Connecting...\" : \"Start Stream\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.isConnected() && !ctx.isConnecting());\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassMap(\"status-value status-\" + ctx.connectionState);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.connectionState, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStreamId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeStreams);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"hidden\", !ctx.hasStream);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasStream);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.NgIf, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".stream-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  font-family: Arial, sans-serif;\\n}\\n\\n.config-panel[_ngcontent-%COMP%], .status-panel[_ngcontent-%COMP%], .video-panel[_ngcontent-%COMP%], .error-panel[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: bold;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n\\n.button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  flex-wrap: wrap;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #545b62;\\n}\\n\\n.btn-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n\\n.btn-info[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #117a8b;\\n}\\n\\n.btn-small[_ngcontent-%COMP%] {\\n  padding: 5px 10px;\\n  font-size: 12px;\\n}\\n\\n.status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 10px;\\n}\\n\\n.status-label[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  min-width: 120px;\\n}\\n\\n.status-value[_ngcontent-%COMP%] {\\n  padding: 2px 8px;\\n  border-radius: 4px;\\n}\\n\\n.status-connected[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.status-connecting[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n\\n.status-disconnected[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #000;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.video-player[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-height: 600px;\\n  display: block;\\n}\\n\\n.video-player.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 300px;\\n  color: #666;\\n  text-align: center;\\n}\\n\\n.error-panel[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #721c24;\\n  margin-bottom: 10px;\\n}\\n\\nh1[_ngcontent-%COMP%], h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  border-bottom: 2px solid #007bff;\\n  padding-bottom: 10px;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .stream-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .config-panel[_ngcontent-%COMP%], .status-panel[_ngcontent-%COMP%], .video-panel[_ngcontent-%COMP%], .error-panel[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  \\n  .button-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 5px;\\n  }\\n  \\n  .status-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  \\n  .status-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    margin-bottom: 5px;\\n  }\\n  \\n  .video-player[_ngcontent-%COMP%] {\\n    max-height: 300px;\\n  }\\n  \\n  .video-placeholder[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n}\\n\\n\\n.video-container[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  z-index: 1;\\n}\\n\\n.video-container.has-stream[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: translate(-50%, -50%) rotate(0deg); }\\n  100% { transform: translate(-50%, -50%) rotate(360deg); }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;IAgEIA,+BAAiD;IACpBA,0BAAU;IAAAA,iBAAO;IAC5CA,gCAA2B;IAAAA,YAAqB;IAAAA,iBAAO;;;;IAA5BA,eAAqB;IAArBA,4CAAqB;;;;;IAGlDA,+BAA+C;IAClBA,+BAAe;IAAAA,iBAAO;IACjDA,gCAA2B;IAAAA,YAA+B;IAAAA,iBAAO;;;;IAAtCA,eAA+B;IAA/BA,6FAA+B;;;;;IAkB1DA,+BAAkD;IAC7CA,mCAAmB;IAAAA,iBAAI;IAC1BA,yBAAG;IAAAA,sEAAoD;IAAAA,iBAAI;;;;;;IAMjEA,+BAA8C;IACxCA,qBAAK;IAAAA,iBAAK;IACdA,6BAAyB;IAAAA,YAAkB;IAAAA,iBAAI;IAC/CA,kCAAqD;IAA7CA;MAAAA;MAAA;MAAA,OAASA,kCAAY;IAAA,EAAC;IAAuBA,qBAAK;IAAAA,iBAAS;;;;IAD1CA,eAAkB;IAAlBA,yCAAkB;;;AC1F/C,OAAM,MAAOC,qBAAqB;EAgBhCC,YAAoBC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAbjC;IACA,oBAAe,GAAW,cAAc;IACxC,oBAAe,GAAkB,IAAI;IACrC,kBAAa,GAAQ,IAAI;IACzB,iBAAY,GAAkB,IAAI;IAClC,cAAS,GAAY,KAAK;IAE1B;IACA,YAAO,GAAG,sCAAsC;IAChD,aAAQ,GAAG,CAAC;IAEJ,kBAAa,GAAmB,EAAE;EAES;EAEnDC,QAAQ;IACN;IACA,IAAI,CAACC,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACI,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACJ,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACQ,OAAO,CAACH,SAAS,CAACI,MAAM,IAAG;MAC5C,IAAIA,MAAM,IAAI,IAAI,CAACC,YAAY,EAAE;QAC/B,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAGH,MAAM;QAClD,IAAI,CAACI,SAAS,GAAG,IAAI;QACrB,IAAI,CAACC,UAAU,EAAE;OAClB,MAAM;QACL,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,IAAI,IAAI,CAACH,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI;;;IAGtD,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACG,cAAc,EAAE;EACvB;EAEAC,WAAW;IACT,IAAI,CAACd,aAAa,CAACe,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEMC,WAAW;IAAA;IAAA;MACf,IAAI;QACF,KAAI,CAACP,UAAU,EAAE;QAEjB,IAAI,CAAC,KAAI,CAACQ,OAAO,CAACC,IAAI,EAAE,EAAE;UACxB,KAAI,CAACC,YAAY,GAAG,+BAA+B;UACnD;;QAGF,MAAMC,aAAa,GAAkB;UACnCC,IAAI,EAAE,KAAI,CAACJ,OAAO,CAACC,IAAI,EAAE;UACzBI,MAAM,EAAE,KAAI,CAACC;SACd;QAED,MAAMC,QAAQ,SAAyB,KAAI,CAAC7B,aAAa,CAACqB,WAAW,CAACI,aAAa,CAAC;QACpF,KAAI,CAACK,eAAe,GAAGD,QAAQ,CAACE,SAAS;QAEzCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,QAAQ,CAAC;OAEtD,CAAC,OAAOK,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,KAAI,CAACV,YAAY,GAAG,2BAA2BU,KAAK,EAAE;;IACvD;EACH;EAEMd,UAAU;IAAA;IAAA;MACd,IAAI;QACF,MAAM,MAAI,CAACpB,aAAa,CAACoB,UAAU,CAAC,MAAI,CAACU,eAAe,IAAIK,SAAS,CAAC;QACtE,MAAI,CAACL,eAAe,GAAG,IAAI;QAC3B,MAAI,CAACjB,SAAS,GAAG,KAAK;QACtBmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACV,YAAY,GAAG,0BAA0BU,KAAK,EAAE;;IACtD;EACH;EAEMnB,cAAc;IAAA;IAAA;MAClB,IAAI;QACF,MAAMqB,OAAO,SAAS,MAAI,CAACpC,aAAa,CAACqC,gBAAgB,EAAE;QAC3D,MAAI,CAACC,aAAa,GAAGF,OAAO;OAC7B,CAAC,OAAOF,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAEApB,UAAU;IACR,IAAI,CAACU,YAAY,GAAG,IAAI;EAC1B;EAEAe,YAAY;IACV,OAAO,IAAI,CAAChC,eAAe,KAAK,YAAY;EAC9C;EAEAiC,WAAW;IACT,OAAO,IAAI,CAACjC,eAAe,KAAK,WAAW;EAC7C;;;uBA3GWT,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAA2C;MAAAC;QAAA;;;;;;;;;;;;;UDTlC7C,8BAA8B;UACxBA,4CAA4B;UAAAA,iBAAK;UAGrCA,8BAA0B;UACpBA,oCAAoB;UAAAA,iBAAK;UAC7BA,8BAAwB;UACDA,yBAAS;UAAAA,iBAAQ;UACtCA,gCAME;UAHAA;YAAA;UAAA,EAAqB;UAHvBA,iBAME;UAGJA,+BAAwB;UACAA,2BAAU;UAAAA,iBAAQ;UACxCA,iCAME;UAHAA;YAAA;UAAA,EAAsB;UAHxBA,iBAME;UAGJA,+BAA0B;UAEtBA;YAAA,OAAS8C,iBAAa;UAAA,EAAC;UAIvB9C,aACF;UAAAA,iBAAS;UAETA,kCAIC;UAHCA;YAAA,OAAS8C,gBAAY;UAAA,EAAC;UAItB9C,8BACF;UAAAA,iBAAS;UAETA,mCAGC;UAFCA;YAAA,OAAS8C,oBAAgB;UAAA,EAAC;UAG1B9C,yCACF;UAAAA,iBAAS;UAKbA,gCAA0B;UACpBA,kCAAiB;UAAAA,iBAAK;UAC1BA,gCAAyB;UACIA,uBAAM;UAAAA,iBAAO;UACxCA,6BAAyD;UACvDA,aACF;UAAAA,iBAAO;UAGTA,yEAGM;UAENA,yEAGM;UACRA,iBAAM;UAGNA,gCAAyB;UACnBA,4BAAW;UAAAA,iBAAK;UACpBA,gCAA6B;UAC3BA,iCAQS;UAETA,yEAGM;UACRA,iBAAM;UAIRA,yEAIM;UACRA,iBAAM;;;UA3FEA,eAAqB;UAArBA,qCAAqB;UAWrBA,eAAsB;UAAtBA,sCAAsB;UAStBA,eAA4C;UAA5CA,kEAA4C;UAG5CA,eACF;UADEA,sFACF;UAIEA,eAA8C;UAA9CA,oEAA8C;UAoB1CA,gBAAkD;UAAlDA,2DAAkD;UACtDA,eACF;UADEA,oDACF;UAGwBA,eAAqB;UAArBA,0CAAqB;UAKrBA,eAAmB;UAAnBA,wCAAmB;UAiBzCA,eAA2B;UAA3BA,wCAA2B;UAGGA,eAAgB;UAAhBA,qCAAgB;UAQ1BA,eAAkB;UAAlBA,uCAAkB", "names": ["i0", "StreamViewerComponent", "constructor", "webrtcService", "ngOnInit", "subscriptions", "push", "connectionState$", "subscribe", "state", "connectionState", "stream$", "stream", "videoElement", "nativeElement", "srcObject", "hasStream", "clearError", "refreshStreams", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "stopStream", "startStream", "rtspUrl", "trim", "errorMessage", "streamRequest", "rtsp", "camera", "cameraId", "response", "currentStreamId", "stream_id", "console", "log", "error", "undefined", "streams", "getActiveStreams", "activeStreams", "isConnecting", "isConnected", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\stream-viewer.component.html", "E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\stream-viewer.component.ts"], "sourcesContent": ["<div class=\"stream-container\">\n  <h1>RTSP to WebRTC Stream Viewer</h1>\n\n  <!-- Stream Configuration -->\n  <div class=\"config-panel\">\n    <h3>Stream Configuration</h3>\n    <div class=\"form-group\">\n      <label for=\"rtspUrl\">RTSP URL:</label>\n      <input\n        id=\"rtspUrl\"\n        type=\"text\"\n        [(ngModel)]=\"rtspUrl\"\n        placeholder=\"rtsp://admin:Admin@123@*************\"\n        class=\"form-control\"\n      />\n    </div>\n\n    <div class=\"form-group\">\n      <label for=\"cameraId\">Camera ID:</label>\n      <input\n        id=\"cameraId\"\n        type=\"number\"\n        [(ngModel)]=\"cameraId\"\n        placeholder=\"1\"\n        class=\"form-control\"\n      />\n    </div>\n\n    <div class=\"button-group\">\n      <button\n        (click)=\"startStream()\"\n        [disabled]=\"isConnecting() || isConnected()\"\n        class=\"btn btn-primary\"\n      >\n        {{ isConnecting() ? 'Connecting...' : 'Start Stream' }}\n      </button>\n\n      <button\n        (click)=\"stopStream()\"\n        [disabled]=\"!isConnected() && !isConnecting()\"\n        class=\"btn btn-secondary\"\n      >\n        Stop Stream\n      </button>\n\n      <button\n        (click)=\"refreshStreams()\"\n        class=\"btn btn-info\"\n      >\n        Refresh Active Streams\n      </button>\n    </div>\n  </div>\n\n  <!-- Connection Status -->\n  <div class=\"status-panel\">\n    <h3>Connection Status</h3>\n    <div class=\"status-item\">\n      <span class=\"status-label\">State:</span>\n      <span [class]=\"'status-value status-' + connectionState\">\n        {{ connectionState }}\n      </span>\n    </div>\n\n    <div class=\"status-item\" *ngIf=\"currentStreamId\">\n      <span class=\"status-label\">Stream ID:</span>\n      <span class=\"status-value\">{{ currentStreamId }}</span>\n    </div>\n\n    <div class=\"status-item\" *ngIf=\"activeStreams\">\n      <span class=\"status-label\">Active Streams:</span>\n      <span class=\"status-value\">{{ activeStreams?.count || 0 }}</span>\n    </div>\n  </div>\n\n  <!-- Video Player -->\n  <div class=\"video-panel\">\n    <h3>Live Stream</h3>\n    <div class=\"video-container\">\n      <video\n        #videoElement\n        autoplay\n        playsinline\n        muted\n        controls\n        class=\"video-player\"\n        [class.hidden]=\"!hasStream\"\n      ></video>\n\n      <div class=\"video-placeholder\" *ngIf=\"!hasStream\">\n        <p>No stream available</p>\n        <p>Configure RTSP URL and click \"Start Stream\" to begin</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error Display -->\n  <div class=\"error-panel\" *ngIf=\"errorMessage\">\n    <h3>Error</h3>\n    <p class=\"error-message\">{{ errorMessage }}</p>\n    <button (click)=\"clearError()\" class=\"btn btn-small\">Clear</button>\n  </div>\n</div>\n", "import { Compo<PERSON>, ElementRef, <PERSON>Child, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { WebRTCService, StreamRequest, StreamResponse } from '../services/webrtc.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-stream-viewer',\n  templateUrl: './stream-viewer.component.html',\n  styleUrls: ['./stream-viewer.component.css']\n})\nexport class StreamViewerComponent implements OnInit, OnD<PERSON>roy {\n  @ViewChild('videoElement', { static: true }) videoElement!: ElementRef<HTMLVideoElement>;\n\n  // Component properties\n  connectionState: string = 'disconnected';\n  currentStreamId: string | null = null;\n  activeStreams: any = null;\n  errorMessage: string | null = null;\n  hasStream: boolean = false;\n\n  // default Form data\n  rtspUrl = 'rtsp://admin:Admin@123@*************';\n  cameraId = 1;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(private webrtcService: WebRTCService) {}\n\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n\n    // Subscribe to stream changes\n    this.subscriptions.push(\n      this.webrtcService.stream$.subscribe(stream => {\n        if (stream && this.videoElement) {\n          this.videoElement.nativeElement.srcObject = stream;\n          this.hasStream = true;\n          this.clearError();\n        } else {\n          this.hasStream = false;\n          if (this.videoElement) {\n            this.videoElement.nativeElement.srcObject = null;\n          }\n        }\n      })\n    );\n\n    // Load active streams on init\n    this.refreshStreams();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n\n  async startStream() {\n    try {\n      this.clearError();\n\n      if (!this.rtspUrl.trim()) {\n        this.errorMessage = 'Please enter a valid RTSP URL';\n        return;\n      }\n\n      const streamRequest: StreamRequest = {\n        rtsp: this.rtspUrl.trim(),\n        camera: this.cameraId\n      };\n\n      const response: StreamResponse = await this.webrtcService.startStream(streamRequest);\n      this.currentStreamId = response.stream_id;\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream: ${error}`;\n    }\n  }\n\n  async stopStream() {\n    try {\n      await this.webrtcService.stopStream(this.currentStreamId || undefined);\n      this.currentStreamId = null;\n      this.hasStream = false;\n      console.log('Stream stopped successfully');\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream: ${error}`;\n    }\n  }\n\n  async refreshStreams() {\n    try {\n      const streams = await this.webrtcService.getActiveStreams();\n      this.activeStreams = streams;\n    } catch (error) {\n      console.error('Failed to refresh streams:', error);\n    }\n  }\n\n  clearError() {\n    this.errorMessage = null;\n  }\n\n  isConnecting(): boolean {\n    return this.connectionState === 'connecting';\n  }\n\n  isConnected(): boolean {\n    return this.connectionState === 'connected';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}