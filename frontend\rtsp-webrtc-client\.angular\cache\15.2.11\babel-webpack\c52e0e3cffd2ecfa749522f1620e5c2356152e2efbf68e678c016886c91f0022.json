{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nlet StreamViewerComponent = class StreamViewerComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    // Component properties\n    this.connectionState = 'disconnected';\n    this.currentStreamId = null;\n    this.activeStreams = null;\n    this.errorMessage = null;\n    this.hasStream = false;\n    // default Form data\n    this.rtspUrl = 'rtsp://admin:Admin@123@*************';\n    this.cameraId = 1;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n    // Subscribe to stream changes\n    this.subscriptions.push(this.webrtcService.stream$.subscribe(stream => {\n      if (stream && this.videoElement) {\n        this.videoElement.nativeElement.srcObject = stream;\n        this.hasStream = true;\n        this.clearError();\n      } else {\n        this.hasStream = false;\n        if (this.videoElement) {\n          this.videoElement.nativeElement.srcObject = null;\n        }\n      }\n    }));\n    // Load active streams on init\n    this.refreshStreams();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n  startStream() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.clearError();\n        if (!_this.rtspUrl.trim()) {\n          _this.errorMessage = 'Please enter a valid RTSP URL';\n          return;\n        }\n        const streamRequest = {\n          rtsp: _this.rtspUrl.trim(),\n          camera: _this.cameraId\n        };\n        const response = yield _this.webrtcService.startStream(streamRequest);\n        _this.currentStreamId = response.stream_id;\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this.errorMessage = `Failed to start stream: ${error}`;\n      }\n    })();\n  }\n  stopStream() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.webrtcService.stopStream(_this2.currentStreamId || undefined);\n        _this2.currentStreamId = null;\n        _this2.hasStream = false;\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this2.errorMessage = `Failed to stop stream: ${error}`;\n      }\n    })();\n  }\n  refreshStreams() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const streams = yield _this3.webrtcService.getActiveStreams();\n        _this3.activeStreams = streams;\n      } catch (error) {\n        console.error('Failed to refresh streams:', error);\n      }\n    })();\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  isConnecting() {\n    return this.connectionState === 'connecting';\n  }\n  isConnected() {\n    return this.connectionState === 'connected';\n  }\n};\n__decorate([ViewChild('videoElement', {\n  static: true\n})], StreamViewerComponent.prototype, \"videoElement\", void 0);\nStreamViewerComponent = __decorate([Component({\n  selector: 'app-stream-viewer',\n  templateUrl: './stream-viewer.component.html',\n  styleUrls: ['./stream-viewer.component.css']\n})], StreamViewerComponent);\nexport { StreamViewerComponent };", "map": {"version": 3, "mappings": ";;AAAA,SAASA,SAAS,EAAcC,SAAS,QAA2B,eAAe;AAS5E,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAgBhCC,YAAoBC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAbjC;IACA,oBAAe,GAAW,cAAc;IACxC,oBAAe,GAAkB,IAAI;IACrC,kBAAa,GAAQ,IAAI;IACzB,iBAAY,GAAkB,IAAI;IAClC,cAAS,GAAY,KAAK;IAE1B;IACA,YAAO,GAAG,sCAAsC;IAChD,aAAQ,GAAG,CAAC;IAEJ,kBAAa,GAAmB,EAAE;EAES;EAEnDC,QAAQ;IACN;IACA,IAAI,CAACC,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACI,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACJ,aAAa,CAACC,IAAI,CACrB,IAAI,CAACH,aAAa,CAACQ,OAAO,CAACH,SAAS,CAACI,MAAM,IAAG;MAC5C,IAAIA,MAAM,IAAI,IAAI,CAACC,YAAY,EAAE;QAC/B,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAGH,MAAM;QAClD,IAAI,CAACI,SAAS,GAAG,IAAI;QACrB,IAAI,CAACC,UAAU,EAAE;OAClB,MAAM;QACL,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,IAAI,IAAI,CAACH,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI;;;IAGtD,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACG,cAAc,EAAE;EACvB;EAEAC,WAAW;IACT,IAAI,CAACd,aAAa,CAACe,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEMC,WAAW;IAAA;IAAA;MACf,IAAI;QACF,KAAI,CAACP,UAAU,EAAE;QAEjB,IAAI,CAAC,KAAI,CAACQ,OAAO,CAACC,IAAI,EAAE,EAAE;UACxB,KAAI,CAACC,YAAY,GAAG,+BAA+B;UACnD;;QAGF,MAAMC,aAAa,GAAkB;UACnCC,IAAI,EAAE,KAAI,CAACJ,OAAO,CAACC,IAAI,EAAE;UACzBI,MAAM,EAAE,KAAI,CAACC;SACd;QAED,MAAMC,QAAQ,SAAyB,KAAI,CAAC7B,aAAa,CAACqB,WAAW,CAACI,aAAa,CAAC;QACpF,KAAI,CAACK,eAAe,GAAGD,QAAQ,CAACE,SAAS;QAEzCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,QAAQ,CAAC;OAEtD,CAAC,OAAOK,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,KAAI,CAACV,YAAY,GAAG,2BAA2BU,KAAK,EAAE;;IACvD;EACH;EAEMd,UAAU;IAAA;IAAA;MACd,IAAI;QACF,MAAM,MAAI,CAACpB,aAAa,CAACoB,UAAU,CAAC,MAAI,CAACU,eAAe,IAAIK,SAAS,CAAC;QACtE,MAAI,CAACL,eAAe,GAAG,IAAI;QAC3B,MAAI,CAACjB,SAAS,GAAG,KAAK;QACtBmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACV,YAAY,GAAG,0BAA0BU,KAAK,EAAE;;IACtD;EACH;EAEMnB,cAAc;IAAA;IAAA;MAClB,IAAI;QACF,MAAMqB,OAAO,SAAS,MAAI,CAACpC,aAAa,CAACqC,gBAAgB,EAAE;QAC3D,MAAI,CAACC,aAAa,GAAGF,OAAO;OAC7B,CAAC,OAAOF,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAEApB,UAAU;IACR,IAAI,CAACU,YAAY,GAAG,IAAI;EAC1B;EAEAe,YAAY;IACV,OAAO,IAAI,CAAChC,eAAe,KAAK,YAAY;EAC9C;EAEAiC,WAAW;IACT,OAAO,IAAI,CAACjC,eAAe,KAAK,WAAW;EAC7C;CACD;AA3G8CkC,YAA5C5C,SAAS,CAAC,cAAc,EAAE;EAAE6C,MAAM,EAAE;AAAI,CAAE,CAAC,2DAA6C;AAD9E5C,qBAAqB,eALjCF,SAAS,CAAC;EACT+C,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,GACW/C,qBAAqB,CA4GjC;SA5GYA,qBAAqB", "names": ["Component", "ViewChild", "StreamViewerComponent", "constructor", "webrtcService", "ngOnInit", "subscriptions", "push", "connectionState$", "subscribe", "state", "connectionState", "stream$", "stream", "videoElement", "nativeElement", "srcObject", "hasStream", "clearError", "refreshStreams", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "stopStream", "startStream", "rtspUrl", "trim", "errorMessage", "streamRequest", "rtsp", "camera", "cameraId", "response", "currentStreamId", "stream_id", "console", "log", "error", "undefined", "streams", "getActiveStreams", "activeStreams", "isConnecting", "isConnected", "__decorate", "static", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\stream-viewer.component.ts"], "sourcesContent": ["import { Compo<PERSON>, ElementRef, <PERSON>Child, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { WebRTCService, StreamRequest, StreamResponse } from '../services/webrtc.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-stream-viewer',\n  templateUrl: './stream-viewer.component.html',\n  styleUrls: ['./stream-viewer.component.css']\n})\nexport class StreamViewerComponent implements OnInit, OnD<PERSON>roy {\n  @ViewChild('videoElement', { static: true }) videoElement!: ElementRef<HTMLVideoElement>;\n\n  // Component properties\n  connectionState: string = 'disconnected';\n  currentStreamId: string | null = null;\n  activeStreams: any = null;\n  errorMessage: string | null = null;\n  hasStream: boolean = false;\n\n  // default Form data\n  rtspUrl = 'rtsp://admin:Admin@123@*************';\n  cameraId = 1;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(private webrtcService: WebRTCService) {}\n\n  ngOnInit() {\n    // Subscribe to connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n\n    // Subscribe to stream changes\n    this.subscriptions.push(\n      this.webrtcService.stream$.subscribe(stream => {\n        if (stream && this.videoElement) {\n          this.videoElement.nativeElement.srcObject = stream;\n          this.hasStream = true;\n          this.clearError();\n        } else {\n          this.hasStream = false;\n          if (this.videoElement) {\n            this.videoElement.nativeElement.srcObject = null;\n          }\n        }\n      })\n    );\n\n    // Load active streams on init\n    this.refreshStreams();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopStream();\n  }\n\n  async startStream() {\n    try {\n      this.clearError();\n\n      if (!this.rtspUrl.trim()) {\n        this.errorMessage = 'Please enter a valid RTSP URL';\n        return;\n      }\n\n      const streamRequest: StreamRequest = {\n        rtsp: this.rtspUrl.trim(),\n        camera: this.cameraId\n      };\n\n      const response: StreamResponse = await this.webrtcService.startStream(streamRequest);\n      this.currentStreamId = response.stream_id;\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream: ${error}`;\n    }\n  }\n\n  async stopStream() {\n    try {\n      await this.webrtcService.stopStream(this.currentStreamId || undefined);\n      this.currentStreamId = null;\n      this.hasStream = false;\n      console.log('Stream stopped successfully');\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream: ${error}`;\n    }\n  }\n\n  async refreshStreams() {\n    try {\n      const streams = await this.webrtcService.getActiveStreams();\n      this.activeStreams = streams;\n    } catch (error) {\n      console.error('Failed to refresh streams:', error);\n    }\n  }\n\n  clearError() {\n    this.errorMessage = null;\n  }\n\n  isConnecting(): boolean {\n    return this.connectionState === 'connecting';\n  }\n\n  isConnected(): boolean {\n    return this.connectionState === 'connected';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}