{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { StreamViewerComponent } from './components/stream-viewer.component';\nimport { WebRTCCameraComponent } from './components/webrtc-camera.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule, CommonModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, StreamViewerComponent, WebRTCCameraComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, CommonModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;;AAmB5E,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,YAFRJ,YAAY;IAAA;EAAA;;;gBAPtBL,aAAa,EACbI,gBAAgB,EAChBH,WAAW,EACXC,YAAY,EACZC,gBAAgB;IAAA;EAAA;;;2EAKPK,SAAS;IAAAE,eAdlBL,YAAY,EACZC,qBAAqB,EACrBC,qBAAqB;IAAAI,UAGrBX,aAAa,EACbI,gBAAgB,EAChBH,WAAW,EACXC,YAAY,EACZC,gBAAgB;EAAA;AAAA", "names": ["BrowserModule", "FormsModule", "CommonModule", "HttpClientModule", "AppRoutingModule", "AppComponent", "StreamViewerComponent", "WebRTCCameraComponent", "AppModule", "bootstrap", "declarations", "imports"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { StreamViewerComponent } from './components/stream-viewer.component';\nimport { WebRTCCameraComponent } from './components/webrtc-camera.component';\nimport { IcccCameraComponent } from './components/iccc-camera.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    StreamViewerComponent,\n    WebRTCCameraComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    CommonModule,\n    HttpClientModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}