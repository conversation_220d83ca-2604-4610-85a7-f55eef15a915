{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.css?ngResource\";\nimport { Component } from '@angular/core';\nlet AppComponent = class AppComponent {\n  constructor() {\n    this.title = 'RTSP WebRTC Client';\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAAlBC;IACL,UAAK,GAAG,oBAAoB;EAC9B;CAAC;AAFYD,YAAY,eALxBD,SAAS,CAAC;EACTG,QAAQ,EAAE,UAAU;EACpBC,8BAAyB;;CAE1B,CAAC,GACWH,YAAY,CAExB;SAFYA,YAAY", "names": ["Component", "AppComponent", "constructor", "selector", "template"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.html',\r\n  styleUrls: ['./app.css']\r\n})\r\nexport class AppComponent {\r\n  title = 'RTSP WebRTC Client';\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}