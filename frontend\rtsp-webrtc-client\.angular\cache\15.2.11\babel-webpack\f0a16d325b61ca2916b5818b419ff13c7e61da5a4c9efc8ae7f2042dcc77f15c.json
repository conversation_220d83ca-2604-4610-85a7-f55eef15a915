{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class WebRTCService {\n  constructor(http) {\n    this.http = http;\n    this.peerConnection = null;\n    this.websocket = null;\n    this.localStream = null;\n    this.connectionStateSubject = new BehaviorSubject('disconnected');\n    this.connectionState$ = this.connectionStateSubject.asObservable();\n    this.streamSubject = new BehaviorSubject(null);\n    this.stream$ = this.streamSubject.asObservable();\n    this.API_BASE_URL = environment.ICCC_WEBRTC_API || 'http://206.1.32.67:8000';\n    // Remove trailing slash if present to avoid double slashes in URLs\n    this.API_BASE_URL = this.API_BASE_URL.replace(/\\/$/, '');\n  }\n  startStream(streamRequest) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Call the backend API to start the stream\n        const response = yield fetch(`${_this.API_BASE_URL}/start_stream`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(streamRequest)\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const streamResponse = yield response.json();\n        console.log('Backend response:', streamResponse);\n        // Initialize WebRTC connection using the WebSocket URL from backend\n        yield _this.initializeWebRTC(streamResponse.stream_id, streamResponse.webrtc_url);\n        return streamResponse;\n      } catch (error) {\n        console.error('Error starting stream:', error);\n        throw error;\n      }\n    })();\n  }\n  initializeWebRTC(streamId, webrtcUrl) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Create peer connection\n        _this2.peerConnection = new RTCPeerConnection({\n          iceServers: [{\n            urls: 'stun:stun.l.google.com:19302'\n          }, {\n            urls: 'stun:stun1.l.google.com:19302'\n          }]\n        });\n        // Handle connection state changes\n        _this2.peerConnection.onconnectionstatechange = () => {\n          if (_this2.peerConnection) {\n            console.log('WebRTC Connection state:', _this2.peerConnection.connectionState);\n            _this2.connectionStateSubject.next(_this2.peerConnection.connectionState);\n            if (_this2.peerConnection.connectionState === 'failed') {\n              console.error('WebRTC connection failed');\n            } else if (_this2.peerConnection.connectionState === 'connected') {\n              console.log('WebRTC connection established successfully');\n            }\n          }\n        };\n        // Handle ICE gathering state\n        _this2.peerConnection.onicegatheringstatechange = () => {\n          if (_this2.peerConnection) {\n            console.log('ICE gathering state:', _this2.peerConnection.iceGatheringState);\n          }\n        };\n        // Handle incoming tracks (video stream)\n        _this2.peerConnection.ontrack = event => {\n          console.log('Received remote track:', event.track.kind, event);\n          if (event.streams && event.streams[0]) {\n            console.log('Setting stream in service:', event.streams[0]);\n            _this2.streamSubject.next(event.streams[0]);\n          } else {\n            console.warn('No streams in track event');\n          }\n        };\n        // Handle ICE candidates\n        _this2.peerConnection.onicecandidate = event => {\n          if (event.candidate && _this2.websocket) {\n            console.log('Sending ICE candidate:', event.candidate.candidate);\n            _this2.websocket.send(JSON.stringify({\n              type: 'ice-candidate',\n              candidate: event.candidate\n            }));\n          } else if (!event.candidate) {\n            console.log('ICE gathering completed');\n          }\n        };\n        // Connect to WebSocket using the URL from backend\n        yield _this2.connectWebSocket(webrtcUrl);\n      } catch (error) {\n        console.error('Error initializing WebRTC:', error);\n        throw error;\n      }\n    })();\n  }\n  connectWebSocket(webrtcUrl) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        console.log('Connecting to WebSocket:', webrtcUrl);\n        _this3.websocket = new WebSocket(webrtcUrl);\n        _this3.websocket.onopen = /*#__PURE__*/_asyncToGenerator(function* () {\n          console.log('WebSocket connected');\n          try {\n            yield _this3.createOffer();\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n        _this3.websocket.onmessage = /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (event) {\n            try {\n              const message = JSON.parse(event.data);\n              yield _this3.handleWebSocketMessage(message);\n            } catch (error) {\n              console.error('Error handling WebSocket message:', error);\n            }\n          });\n          return function (_x) {\n            return _ref2.apply(this, arguments);\n          };\n        }();\n        _this3.websocket.onerror = error => {\n          console.error('WebSocket error:', error);\n          reject(error);\n        };\n        _this3.websocket.onclose = () => {\n          console.log('WebSocket disconnected');\n          _this3.connectionStateSubject.next('disconnected');\n        };\n      });\n    })();\n  }\n  createOffer() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.peerConnection || !_this4.websocket) {\n        throw new Error('Peer connection or WebSocket not initialized');\n      }\n      try {\n        // Create offer\n        const offer = yield _this4.peerConnection.createOffer({\n          offerToReceiveVideo: true,\n          offerToReceiveAudio: true\n        });\n        // Set local description\n        yield _this4.peerConnection.setLocalDescription(offer);\n        // Send offer to server\n        _this4.websocket.send(JSON.stringify({\n          type: 'offer',\n          sdp: offer.sdp\n        }));\n      } catch (error) {\n        console.error('Error creating offer:', error);\n        throw error;\n      }\n    })();\n  }\n  handleWebSocketMessage(message) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.peerConnection) {\n        return;\n      }\n      try {\n        switch (message.type) {\n          case 'answer':\n            const answer = new RTCSessionDescription({\n              type: 'answer',\n              sdp: message.sdp\n            });\n            yield _this5.peerConnection.setRemoteDescription(answer);\n            break;\n          case 'ice-candidate':\n            if (message.candidate) {\n              yield _this5.peerConnection.addIceCandidate(new RTCIceCandidate(message.candidate));\n            }\n            break;\n          default:\n            console.log('Unknown message type:', message.type);\n        }\n      } catch (error) {\n        console.error('Error handling WebSocket message:', error);\n      }\n    })();\n  }\n  stopStream(streamId) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Stop the stream on the server if streamId is provided\n        if (streamId) {\n          yield fetch(`${_this6.API_BASE_URL}/stop_stream/${streamId}`, {\n            method: 'DELETE'\n          });\n        }\n        // Close WebSocket\n        if (_this6.websocket) {\n          _this6.websocket.close();\n          _this6.websocket = null;\n        }\n        // Close peer connection\n        if (_this6.peerConnection) {\n          _this6.peerConnection.close();\n          _this6.peerConnection = null;\n        }\n        // Reset streams\n        _this6.streamSubject.next(null);\n        _this6.connectionStateSubject.next('disconnected');\n      } catch (error) {\n        console.error('Error stopping stream:', error);\n        throw error;\n      }\n    })();\n  }\n  getActiveStreams() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch(`${_this7.API_BASE_URL}/streams`);\n        return yield response.json();\n      } catch (error) {\n        console.error('Error getting active streams:', error);\n        throw error;\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function WebRTCService_Factory(t) {\n      return new (t || WebRTCService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WebRTCService,\n      factory: WebRTCService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAe,QAAoB,MAAM;AAElD,SAASC,WAAW,QAAQ,gCAAgC;;;AAiB5D,OAAM,MAAOC,aAAa;EAYxBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAVhB,mBAAc,GAA6B,IAAI;IAC/C,cAAS,GAAqB,IAAI;IAClC,gBAAW,GAAuB,IAAI;IAEtC,2BAAsB,GAAG,IAAIJ,eAAe,CAAS,cAAc,CAAC;IACrE,qBAAgB,GAAG,IAAI,CAACK,sBAAsB,CAACC,YAAY,EAAE;IAE5D,kBAAa,GAAG,IAAIN,eAAe,CAAqB,IAAI,CAAC;IAC9D,YAAO,GAAG,IAAI,CAACO,aAAa,CAACD,YAAY,EAAE;IAGhD,IAAI,CAACE,YAAY,GAAGP,WAAW,CAACQ,eAAe,IAAI,yBAAyB;IAC5E;IACA,IAAI,CAACD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAC1D;EAEMC,WAAW,CAACC,aAA4B;IAAA;IAAA;MAC5C,IAAI;QACF;QACA,MAAMC,QAAQ,SAASC,KAAK,CAAC,GAAG,KAAI,CAACN,YAAY,eAAe,EAAE;UAChEO,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;WACjB;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,aAAa;SACnC,CAAC;QAEF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;;QAG3D,MAAMC,cAAc,SAAyBV,QAAQ,CAACW,IAAI,EAAE;QAC5DC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,cAAc,CAAC;QAEhD;QACA,MAAM,KAAI,CAACI,gBAAgB,CAACJ,cAAc,CAACK,SAAS,EAAEL,cAAc,CAACM,UAAU,CAAC;QAEhF,OAAON,cAAc;OACtB,CAAC,OAAOO,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;;IACZ;EACH;EAEcH,gBAAgB,CAACI,QAAgB,EAAEC,SAAiB;IAAA;IAAA;MAChE,IAAI;QACF;QACA,MAAI,CAACC,cAAc,GAAG,IAAIC,iBAAiB,CAAC;UAC1CC,UAAU,EAAE,CACV;YAAEC,IAAI,EAAE;UAA8B,CAAE,EACxC;YAAEA,IAAI,EAAE;UAA+B,CAAE;SAE5C,CAAC;QAEF;QACA,MAAI,CAACH,cAAc,CAACI,uBAAuB,GAAG,MAAK;UACjD,IAAI,MAAI,CAACJ,cAAc,EAAE;YACvBR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,MAAI,CAACO,cAAc,CAACK,eAAe,CAAC;YAC5E,MAAI,CAACjC,sBAAsB,CAACkC,IAAI,CAAC,MAAI,CAACN,cAAc,CAACK,eAAe,CAAC;YAErE,IAAI,MAAI,CAACL,cAAc,CAACK,eAAe,KAAK,QAAQ,EAAE;cACpDb,OAAO,CAACK,KAAK,CAAC,0BAA0B,CAAC;aAC1C,MAAM,IAAI,MAAI,CAACG,cAAc,CAACK,eAAe,KAAK,WAAW,EAAE;cAC9Db,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;;QAG/D,CAAC;QAED;QACA,MAAI,CAACO,cAAc,CAACO,yBAAyB,GAAG,MAAK;UACnD,IAAI,MAAI,CAACP,cAAc,EAAE;YACvBR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,MAAI,CAACO,cAAc,CAACQ,iBAAiB,CAAC;;QAE9E,CAAC;QAED;QACA,MAAI,CAACR,cAAc,CAACS,OAAO,GAAIC,KAAK,IAAI;UACtClB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiB,KAAK,CAACC,KAAK,CAACC,IAAI,EAAEF,KAAK,CAAC;UAC9D,IAAIA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;YACrCrB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiB,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAI,CAACvC,aAAa,CAACgC,IAAI,CAACI,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;WAC1C,MAAM;YACLrB,OAAO,CAACsB,IAAI,CAAC,2BAA2B,CAAC;;QAE7C,CAAC;QAED;QACA,MAAI,CAACd,cAAc,CAACe,cAAc,GAAIL,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACM,SAAS,IAAI,MAAI,CAACC,SAAS,EAAE;YACrCzB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiB,KAAK,CAACM,SAAS,CAACA,SAAS,CAAC;YAChE,MAAI,CAACC,SAAS,CAACC,IAAI,CAACjC,IAAI,CAACC,SAAS,CAAC;cACjCiC,IAAI,EAAE,eAAe;cACrBH,SAAS,EAAEN,KAAK,CAACM;aAClB,CAAC,CAAC;WACJ,MAAM,IAAI,CAACN,KAAK,CAACM,SAAS,EAAE;YAC3BxB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;;QAE1C,CAAC;QAED;QACA,MAAM,MAAI,CAAC2B,gBAAgB,CAACrB,SAAS,CAAC;OAEvC,CAAC,OAAOF,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;;IACZ;EACH;EAEcuB,gBAAgB,CAACrB,SAAiB;IAAA;IAAA;MAC9C,OAAO,IAAIsB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC/B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEM,SAAS,CAAC;QAClD,MAAI,CAACkB,SAAS,GAAG,IAAIO,SAAS,CAACzB,SAAS,CAAC;QAEzC,MAAI,CAACkB,SAAS,CAACQ,MAAM,kCAAG,aAAW;UACjCjC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClC,IAAI;YACF,MAAM,MAAI,CAACiC,WAAW,EAAE;YACxBJ,OAAO,EAAE;WACV,CAAC,OAAOzB,KAAK,EAAE;YACd0B,MAAM,CAAC1B,KAAK,CAAC;;QAEjB,CAAC;QAED,MAAI,CAACoB,SAAS,CAACU,SAAS;UAAA,8BAAG,WAAOjB,KAAK,EAAI;YACzC,IAAI;cACF,MAAMkB,OAAO,GAAG3C,IAAI,CAAC4C,KAAK,CAACnB,KAAK,CAACoB,IAAI,CAAC;cACtC,MAAM,MAAI,CAACC,sBAAsB,CAACH,OAAO,CAAC;aAC3C,CAAC,OAAO/B,KAAK,EAAE;cACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;UAE7D,CAAC;UAAA;YAAA;UAAA;QAAA;QAED,MAAI,CAACoB,SAAS,CAACe,OAAO,GAAInC,KAAK,IAAI;UACjCL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxC0B,MAAM,CAAC1B,KAAK,CAAC;QACf,CAAC;QAED,MAAI,CAACoB,SAAS,CAACgB,OAAO,GAAG,MAAK;UAC5BzC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrC,MAAI,CAACrB,sBAAsB,CAACkC,IAAI,CAAC,cAAc,CAAC;QAClD,CAAC;MACH,CAAC,CAAC;IAAC;EACL;EAEcoB,WAAW;IAAA;IAAA;MACvB,IAAI,CAAC,MAAI,CAAC1B,cAAc,IAAI,CAAC,MAAI,CAACiB,SAAS,EAAE;QAC3C,MAAM,IAAI7B,KAAK,CAAC,8CAA8C,CAAC;;MAGjE,IAAI;QACF;QACA,MAAM8C,KAAK,SAAS,MAAI,CAAClC,cAAc,CAAC0B,WAAW,CAAC;UAClDS,mBAAmB,EAAE,IAAI;UACzBC,mBAAmB,EAAE;SACtB,CAAC;QAEF;QACA,MAAM,MAAI,CAACpC,cAAc,CAACqC,mBAAmB,CAACH,KAAK,CAAC;QAEpD;QACA,MAAI,CAACjB,SAAS,CAACC,IAAI,CAACjC,IAAI,CAACC,SAAS,CAAC;UACjCiC,IAAI,EAAE,OAAO;UACbmB,GAAG,EAAEJ,KAAK,CAACI;SACZ,CAAC,CAAC;OAEJ,CAAC,OAAOzC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;;IACZ;EACH;EAEckC,sBAAsB,CAACH,OAAY;IAAA;IAAA;MAC/C,IAAI,CAAC,MAAI,CAAC5B,cAAc,EAAE;QACxB;;MAGF,IAAI;QACF,QAAQ4B,OAAO,CAACT,IAAI;UAClB,KAAK,QAAQ;YACX,MAAMoB,MAAM,GAAG,IAAIC,qBAAqB,CAAC;cACvCrB,IAAI,EAAE,QAAQ;cACdmB,GAAG,EAAEV,OAAO,CAACU;aACd,CAAC;YACF,MAAM,MAAI,CAACtC,cAAc,CAACyC,oBAAoB,CAACF,MAAM,CAAC;YACtD;UAEF,KAAK,eAAe;YAClB,IAAIX,OAAO,CAACZ,SAAS,EAAE;cACrB,MAAM,MAAI,CAAChB,cAAc,CAAC0C,eAAe,CACvC,IAAIC,eAAe,CAACf,OAAO,CAACZ,SAAS,CAAC,CACvC;;YAEH;UAEF;YACExB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,OAAO,CAACT,IAAI,CAAC;QAAC;OAExD,CAAC,OAAOtB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;IAC1D;EACH;EAEM+C,UAAU,CAAC9C,QAAiB;IAAA;IAAA;MAChC,IAAI;QACF;QACA,IAAIA,QAAQ,EAAE;UACZ,MAAMjB,KAAK,CAAC,GAAG,MAAI,CAACN,YAAY,gBAAgBuB,QAAQ,EAAE,EAAE;YAC1DhB,MAAM,EAAE;WACT,CAAC;;QAGJ;QACA,IAAI,MAAI,CAACmC,SAAS,EAAE;UAClB,MAAI,CAACA,SAAS,CAAC4B,KAAK,EAAE;UACtB,MAAI,CAAC5B,SAAS,GAAG,IAAI;;QAGvB;QACA,IAAI,MAAI,CAACjB,cAAc,EAAE;UACvB,MAAI,CAACA,cAAc,CAAC6C,KAAK,EAAE;UAC3B,MAAI,CAAC7C,cAAc,GAAG,IAAI;;QAG5B;QACA,MAAI,CAAC1B,aAAa,CAACgC,IAAI,CAAC,IAAI,CAAC;QAC7B,MAAI,CAAClC,sBAAsB,CAACkC,IAAI,CAAC,cAAc,CAAC;OAEjD,CAAC,OAAOT,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;;IACZ;EACH;EAEMiD,gBAAgB;IAAA;IAAA;MACpB,IAAI;QACF,MAAMlE,QAAQ,SAASC,KAAK,CAAC,GAAG,MAAI,CAACN,YAAY,UAAU,CAAC;QAC5D,aAAaK,QAAQ,CAACW,IAAI,EAAE;OAC7B,CAAC,OAAOM,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;;IACZ;EACH;;;uBAnPW5B,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAA8E,SAAb9E,aAAa;MAAA+E,YAFZ;IAAM;EAAA", "names": ["BehaviorSubject", "environment", "WebRTCService", "constructor", "http", "connectionStateSubject", "asObservable", "streamSubject", "API_BASE_URL", "ICCC_WEBRTC_API", "replace", "startStream", "streamRequest", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "streamResponse", "json", "console", "log", "initializeWebRTC", "stream_id", "webrtc_url", "error", "streamId", "webrtcUrl", "peerConnection", "RTCPeerConnection", "iceServers", "urls", "onconnectionstatechange", "connectionState", "next", "onicegatheringstatechange", "iceGatheringState", "ontrack", "event", "track", "kind", "streams", "warn", "onicecandidate", "candidate", "websocket", "send", "type", "connectWebSocket", "Promise", "resolve", "reject", "WebSocket", "onopen", "createOffer", "onmessage", "message", "parse", "data", "handleWebSocketMessage", "onerror", "onclose", "offer", "offerToReceiveVideo", "offerToReceiveAudio", "setLocalDescription", "sdp", "answer", "RTCSessionDescription", "setRemoteDescription", "addIceCandidate", "RTCIceCandidate", "stopStream", "close", "getActiveStreams", "factory", "providedIn"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\services\\webrtc.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\n\nexport interface StreamRequest {\n  rtsp: string;\n  camera: number;\n}\n\nexport interface StreamResponse {\n  stream_id: string;\n  webrtc_url: string;\n  status: string;\n  camera: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WebRTCService {\n  private readonly API_BASE_URL: string;\n  private peerConnection: RTCPeerConnection | null = null;\n  private websocket: WebSocket | null = null;\n  private localStream: MediaStream | null = null;\n\n  private connectionStateSubject = new BehaviorSubject<string>('disconnected');\n  public connectionState$ = this.connectionStateSubject.asObservable();\n\n  private streamSubject = new BehaviorSubject<MediaStream | null>(null);\n  public stream$ = this.streamSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    this.API_BASE_URL = environment.ICCC_WEBRTC_API || 'http://206.1.32.67:8000';\n    // Remove trailing slash if present to avoid double slashes in URLs\n    this.API_BASE_URL = this.API_BASE_URL.replace(/\\/$/, '');\n  }\n\n  async startStream(streamRequest: StreamRequest): Promise<StreamResponse> {\n    try {\n      // Call the backend API to start the stream\n      const response = await fetch(`${this.API_BASE_URL}/start_stream`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(streamRequest)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const streamResponse: StreamResponse = await response.json();\n      console.log('Backend response:', streamResponse);\n\n      // Initialize WebRTC connection using the WebSocket URL from backend\n      await this.initializeWebRTC(streamResponse.stream_id, streamResponse.webrtc_url);\n\n      return streamResponse;\n    } catch (error) {\n      console.error('Error starting stream:', error);\n      throw error;\n    }\n  }\n\n  private async initializeWebRTC(streamId: string, webrtcUrl: string): Promise<void> {\n    try {\n      // Create peer connection\n      this.peerConnection = new RTCPeerConnection({\n        iceServers: [\n          { urls: 'stun:stun.l.google.com:19302' },\n          { urls: 'stun:stun1.l.google.com:19302' }\n        ]\n      });\n\n      // Handle connection state changes\n      this.peerConnection.onconnectionstatechange = () => {\n        if (this.peerConnection) {\n          console.log('WebRTC Connection state:', this.peerConnection.connectionState);\n          this.connectionStateSubject.next(this.peerConnection.connectionState);\n\n          if (this.peerConnection.connectionState === 'failed') {\n            console.error('WebRTC connection failed');\n          } else if (this.peerConnection.connectionState === 'connected') {\n            console.log('WebRTC connection established successfully');\n          }\n        }\n      };\n\n      // Handle ICE gathering state\n      this.peerConnection.onicegatheringstatechange = () => {\n        if (this.peerConnection) {\n          console.log('ICE gathering state:', this.peerConnection.iceGatheringState);\n        }\n      };\n\n      // Handle incoming tracks (video stream)\n      this.peerConnection.ontrack = (event) => {\n        console.log('Received remote track:', event.track.kind, event);\n        if (event.streams && event.streams[0]) {\n          console.log('Setting stream in service:', event.streams[0]);\n          this.streamSubject.next(event.streams[0]);\n        } else {\n          console.warn('No streams in track event');\n        }\n      };\n\n      // Handle ICE candidates\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.websocket) {\n          console.log('Sending ICE candidate:', event.candidate.candidate);\n          this.websocket.send(JSON.stringify({\n            type: 'ice-candidate',\n            candidate: event.candidate\n          }));\n        } else if (!event.candidate) {\n          console.log('ICE gathering completed');\n        }\n      };\n\n      // Connect to WebSocket using the URL from backend\n      await this.connectWebSocket(webrtcUrl);\n\n    } catch (error) {\n      console.error('Error initializing WebRTC:', error);\n      throw error;\n    }\n  }\n\n  private async connectWebSocket(webrtcUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      console.log('Connecting to WebSocket:', webrtcUrl);\n      this.websocket = new WebSocket(webrtcUrl);\n\n      this.websocket.onopen = async () => {\n        console.log('WebSocket connected');\n        try {\n          await this.createOffer();\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      };\n\n      this.websocket.onmessage = async (event) => {\n        try {\n          const message = JSON.parse(event.data);\n          await this.handleWebSocketMessage(message);\n        } catch (error) {\n          console.error('Error handling WebSocket message:', error);\n        }\n      };\n\n      this.websocket.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        reject(error);\n      };\n\n      this.websocket.onclose = () => {\n        console.log('WebSocket disconnected');\n        this.connectionStateSubject.next('disconnected');\n      };\n    });\n  }\n\n  private async createOffer(): Promise<void> {\n    if (!this.peerConnection || !this.websocket) {\n      throw new Error('Peer connection or WebSocket not initialized');\n    }\n\n    try {\n      // Create offer\n      const offer = await this.peerConnection.createOffer({\n        offerToReceiveVideo: true,\n        offerToReceiveAudio: true\n      });\n\n      // Set local description\n      await this.peerConnection.setLocalDescription(offer);\n\n      // Send offer to server\n      this.websocket.send(JSON.stringify({\n        type: 'offer',\n        sdp: offer.sdp\n      }));\n\n    } catch (error) {\n      console.error('Error creating offer:', error);\n      throw error;\n    }\n  }\n\n  private async handleWebSocketMessage(message: any): Promise<void> {\n    if (!this.peerConnection) {\n      return;\n    }\n\n    try {\n      switch (message.type) {\n        case 'answer':\n          const answer = new RTCSessionDescription({\n            type: 'answer',\n            sdp: message.sdp\n          });\n          await this.peerConnection.setRemoteDescription(answer);\n          break;\n\n        case 'ice-candidate':\n          if (message.candidate) {\n            await this.peerConnection.addIceCandidate(\n              new RTCIceCandidate(message.candidate)\n            );\n          }\n          break;\n\n        default:\n          console.log('Unknown message type:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n    }\n  }\n\n  async stopStream(streamId?: string): Promise<void> {\n    try {\n      // Stop the stream on the server if streamId is provided\n      if (streamId) {\n        await fetch(`${this.API_BASE_URL}/stop_stream/${streamId}`, {\n          method: 'DELETE'\n        });\n      }\n\n      // Close WebSocket\n      if (this.websocket) {\n        this.websocket.close();\n        this.websocket = null;\n      }\n\n      // Close peer connection\n      if (this.peerConnection) {\n        this.peerConnection.close();\n        this.peerConnection = null;\n      }\n\n      // Reset streams\n      this.streamSubject.next(null);\n      this.connectionStateSubject.next('disconnected');\n\n    } catch (error) {\n      console.error('Error stopping stream:', error);\n      throw error;\n    }\n  }\n\n  async getActiveStreams(): Promise<any> {\n    try {\n      const response = await fetch(`${this.API_BASE_URL}/streams`);\n      return await response.json();\n    } catch (error) {\n      console.error('Error getting active streams:', error);\n      throw error;\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}