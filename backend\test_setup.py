#!/usr/bin/env python3
"""
Test script to verify the RTSP to WebRTC setup is working correctly.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_imports():
    """Test if all required imports work."""
    print("Testing imports...")
    
    try:
        import fastapi
        print(f"✓ FastAPI {fastapi.__version__}")
    except ImportError as e:
        print(f"✗ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print(f"✓ Uvicorn {uvicorn.__version__}")
    except ImportError as e:
        print(f"✗ Uvicorn import failed: {e}")
        return False
    
    try:
        import aiortc
        print(f"✓ aiortc {aiortc.__version__}")
    except ImportError as e:
        print(f"✗ aiortc import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    return True

async def test_webrtc_creation():
    """Test WebRTC peer connection creation."""
    print("\nTesting WebRTC peer connection...")
    
    try:
        from aiortc import RTCPeerConnection
        
        pc = RTCPeerConnection()
        print("✓ RTCPeerConnection created successfully")
        
        # Test creating an offer
        offer = await pc.createOffer()
        print("✓ SDP offer created successfully")
        
        await pc.close()
        print("✓ Peer connection closed successfully")
        
        return True
    except Exception as e:
        print(f"✗ WebRTC test failed: {e}")
        return False

async def test_opencv_camera():
    """Test OpenCV camera access (optional)."""
    print("\nTesting OpenCV camera access...")
    
    try:
        import cv2
        
        # Try to create a VideoCapture object (won't actually connect)
        cap = cv2.VideoCapture()
        print("✓ VideoCapture object created")
        
        # Test if we can set properties
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        print("✓ VideoCapture properties can be set")
        
        cap.release()
        print("✓ VideoCapture released successfully")
        
        return True
    except Exception as e:
        print(f"✗ OpenCV camera test failed: {e}")
        return False

def test_file_structure():
    """Test if required files exist."""
    print("\nTesting file structure...")
    
    current_dir = Path(__file__).parent
    required_files = [
        "main.py",
        "requirements.txt"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = current_dir / file_name
        if file_path.exists():
            print(f"✓ {file_name} exists")
        else:
            print(f"✗ {file_name} missing")
            all_exist = False
    
    return all_exist

async def main():
    """Run all tests."""
    print("RTSP to WebRTC Setup Test")
    print("=" * 30)
    
    tests_passed = 0
    total_tests = 4
    
    # Test file structure
    if test_file_structure():
        tests_passed += 1
    
    # Test imports
    if await test_imports():
        tests_passed += 1
    
    # Test WebRTC
    if await test_webrtc_creation():
        tests_passed += 1
    
    # Test OpenCV
    if await test_opencv_camera():
        tests_passed += 1
    
    print(f"\nTest Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Setup is ready.")
        print("\nNext steps:")
        print("1. Run 'python main.py' to start the server")
        print("2. Open the Angular frontend at http://localhost:4200")
        print("3. Configure your RTSP URL and start streaming")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ required)")
        print("3. Verify OpenCV installation")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
