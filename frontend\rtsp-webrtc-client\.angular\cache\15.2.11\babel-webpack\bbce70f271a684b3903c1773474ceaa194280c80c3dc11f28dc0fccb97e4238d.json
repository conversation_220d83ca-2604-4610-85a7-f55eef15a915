{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CameraChannel } from '../models/camera.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/camera.service\";\nimport * as i2 from \"../services/webrtc.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"videoElem\"];\nfunction IcccCameraComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"input\", 14);\n    i0.ɵɵlistener(\"change\", function IcccCameraComponent_div_11_Template_input_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const camera_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleStream(camera_r2.username, camera_r2.password, camera_r2.channelip, camera_r2.channelid, $event.target.checked));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const camera_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"camera-\" + camera_r2.channelid)(\"checked\", camera_r2.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"camera-\" + camera_r2.channelid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", camera_r2.channelname, \" \");\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_video_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 22);\n  }\n  if (rf & 2) {\n    const data_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", \"video-\" + data_r6.channelid);\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"p\");\n    i0.ɵɵtext(2, \"No stream available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IcccCameraComponent_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 3)(2, \"div\", 4)(3, \"h6\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 5);\n    i0.ɵɵtemplate(6, IcccCameraComponent_div_14_div_3_video_6_Template, 1, 1, \"video\", 20);\n    i0.ɵɵtemplate(7, IcccCameraComponent_div_14_div_3_div_7_Template, 3, 0, \"div\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(data_r6.channelname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", data_r6.webrtcUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !data_r6.webrtcUrl);\n  }\n}\nfunction IcccCameraComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵtemplate(3, IcccCameraComponent_div_14_div_3_Template, 8, 3, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.resultLive);\n  }\n}\nexport let IcccCameraComponent = /*#__PURE__*/(() => {\n  class IcccCameraComponent {\n    constructor(cameraService, webrtcService, http) {\n      this.cameraService = cameraService;\n      this.webrtcService = webrtcService;\n      this.http = http;\n      this.filteredCamera = [];\n    }\n    ngOnInit() {\n      this.getCamera();\n      window.addEventListener('beforeunload', () => {\n        sessionStorage.removeItem('cameraDataWebRTC');\n      });\n    }\n    getCamera() {\n      const storedData = sessionStorage.getItem('cameraDataWebRTC');\n      if (storedData) {\n        this.camera = JSON.parse(storedData);\n        this.filteredCamera = this.camera;\n      } else {\n        // For demo purposes, using mock data. Replace with actual API call\n        this.camera = [new CameraChannel(1, null, 'Camera 1', '192.168.4.244', null, null, null, null, null, 'admin', 'Admin@123', null, null, null, null, null, false, null, null, null, null), new CameraChannel(2, null, 'Camera 2', '192.168.4.243', null, null, null, null, null, 'admin', 'Admin@123', null, null, null, null, null, false, null, null, null, null), new CameraChannel(3, null, 'Camera 3', '192.168.4.242', null, null, null, null, null, 'admin', 'Admin@123', null, null, null, null, null, false, null, null, null, null)];\n        this.filteredCamera = this.camera;\n        sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n      }\n    }\n    // WebRTC play method similar to HLS playStream\n    playWebRTC(stream, cam) {\n      const videoElement = document.getElementById(`video-${cam}`);\n      if (!stream) {\n        console.error(`WebRTC stream not available for ${cam}`);\n        return;\n      }\n      if (videoElement) {\n        videoElement.srcObject = stream;\n        videoElement.play().catch(error => {\n          console.error('Error playing WebRTC stream:', error);\n        });\n        console.log(`WebRTC stream started for ${cam}`);\n      }\n    }\n    toggleStream(username, password, channelip, channelid, checked) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!checked) {\n          _this.stopStream(channelip, channelid, username, password, checked);\n        } else {\n          const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n          let cam = String(channelid);\n          try {\n            const response = yield _this.webrtcService.startCameraStream(rtsp, cam);\n            const camera = _this.camera.find(data => data.channelid === channelid);\n            if (camera) {\n              camera.webrtcUrl = response.webrtc_url;\n              camera.streamId = response.stream_id;\n              camera.checked = true;\n              // Subscribe to WebRTC stream and play it\n              setTimeout(() => {\n                _this.webrtcService.stream$.subscribe(ms => {\n                  if (ms) {\n                    _this.playWebRTC(ms, cam);\n                  }\n                });\n                console.log(`WebRTC URL for ${cam}:`, response.webrtc_url);\n              }, 1000);\n            }\n          } catch (error) {\n            console.error('Error starting WebRTC stream:', error);\n          }\n        }\n      })();\n    }\n    stopStream(channelip, channelid, username, password, checked) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const rtsp = `rtsp://${username}:${password}@${channelip}/profile2`;\n        let cam = String(channelid);\n        try {\n          yield _this2.webrtcService.stopCameraStream(rtsp, cam);\n          const camera = _this2.camera.find(data => data.channelid === channelid);\n          if (camera) {\n            camera.webrtcUrl = null;\n            camera.streamId = null;\n            camera.checked = false;\n            // Stop video element\n            const videoElement = document.getElementById(`video-${cam}`);\n            if (videoElement) {\n              videoElement.srcObject = null;\n            }\n          }\n        } catch (error) {\n          console.error('Error stopping WebRTC stream:', error);\n        }\n      })();\n    }\n    filterCameras() {\n      if (!this.searchTerm) {\n        this.filteredCamera = this.camera;\n      } else {\n        this.filteredCamera = this.camera.filter(camera => camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()) || camera.channelip.includes(this.searchTerm));\n      }\n    }\n    get resultLive() {\n      return this.camera ? this.camera.filter(item => item.checked) : [];\n    }\n    static {\n      this.ɵfac = function IcccCameraComponent_Factory(t) {\n        return new (t || IcccCameraComponent)(i0.ɵɵdirectiveInject(i1.CameraService), i0.ɵɵdirectiveInject(i2.WebRTCService), i0.ɵɵdirectiveInject(i3.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: IcccCameraComponent,\n        selectors: [[\"app-iccc-camera\"]],\n        viewQuery: function IcccCameraComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElem = _t.first);\n          }\n        },\n        decls: 15,\n        vars: 3,\n        consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\", \"col-lg-3\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\"], [1, \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Search cameras...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"camera-list\", 2, \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"class\", \"camera-item mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-lg-9\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"camera-item\", \"mb-2\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"mb-4\"], [1, \"video-grid\"], [\"class\", \"video-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"video-card\"], [\"class\", \"w-100\", \"style\", \"height: 200px; background: #000;\", \"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 3, \"id\", 4, \"ngIf\"], [\"class\", \"video-placeholder\", 4, \"ngIf\"], [\"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 1, \"w-100\", 2, \"height\", \"200px\", \"background\", \"#000\", 3, \"id\"], [1, \"video-placeholder\"]],\n        template: function IcccCameraComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h5\");\n            i0.ɵɵtext(6, \"Camera Search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"input\", 7);\n            i0.ɵɵlistener(\"ngModelChange\", function IcccCameraComponent_Template_input_ngModelChange_9_listener($event) {\n              return ctx.searchTerm = $event;\n            })(\"input\", function IcccCameraComponent_Template_input_input_9_listener() {\n              return ctx.filterCameras();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8);\n            i0.ɵɵtemplate(11, IcccCameraComponent_div_11_Template, 5, 4, \"div\", 9);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\");\n            i0.ɵɵtemplate(14, IcccCameraComponent_div_14_Template, 4, 1, \"div\", 11);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.filteredCamera);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.resultLive);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".video-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:1rem}.video-card[_ngcontent-%COMP%]{border:1px solid #ddd;border-radius:8px;overflow:hidden}.video-placeholder[_ngcontent-%COMP%]{height:200px;display:flex;align-items:center;justify-content:center;background:#f8f9fa;color:#6c757d}.camera-item[_ngcontent-%COMP%]{padding:.5rem;border:1px solid #e9ecef;border-radius:4px;background:#f8f9fa}\"]\n      });\n    }\n  }\n  return IcccCameraComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}