{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const API = {\n  BASEPATH_LOCAL: 'http://206.1.32.64:9108/',\n  BASEPATH_CAMERA: 'http://172.16.19.56:8000/',\n  BASEPATH_WEBRTC: 'http://172.16.19.56:8000'\n};\nexport const environment = {\n  production: false,\n  ICCC_AUTH: API.BASEPATH_LOCAL,\n  ICCC_CAMERA_PYTHON: API.BASEPATH_CAMERA,\n  ICCC_WEBRTC_API: API.BASEPATH_WEBRTC,\n  ICCC_EVS_KAFKA: API.BASEPATH_LOCAL + 'kafka-evs/',\n  ICCC_BOOKMARK: API.BASEPATH_LOCAL + 'bookmark/',\n  ICCC_EVS_DEVICE: API.BASEPATH_LOCAL + 'evsDevice-api/',\n  ICCC_ITMS_CAMERA: API.BASEPATH_LOCAL + 'itms-camera/',\n  ICCC_IVMS_CAMERA: API.BASEPATH_LOCAL + 'ivms-camera/',\n  ICCC_MAGA_IVMS_CAMERA: API.BASEPATH_LOCAL + 'mega/',\n  ICCC_IVMS_ALERT: API.BASEPATH_LOCAL + 'alert/',\n  ICCC_ITMS_ALERT: API.BASEPATH_LOCAL + 'alert/',\n  ICCC_EVENT: API.BASEPATH_LOCAL + 'event/',\n  ICCC_ITMS_SOP: API.BASEPATH_LOCAL + 'sop/',\n  ICCC_SOP: API.BASEPATH_LOCAL + 'sop/',\n  ICCC_USER_DEPARTMENT: API.BASEPATH_LOCAL + 'department-management/',\n  ICCC_MAP_BASEPATH: API.BASEPATH_LOCAL + 'map/',\n  ICCC_ASSET_BASEPATH: API.BASEPATH_LOCAL + 'map/',\n  ICCC_ALERT_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'alertreport/',\n  ICCC_EVENT_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'eventreport/',\n  ICCC_SOP_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'sopreport/',\n  ICCC_SOS_BASEPATH: API.BASEPATH_LOCAL + 'sos/'\n};\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\n// import 'zone.js/dist/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,GAAG,GAAG;EACjBC,cAAc,EAAE,0BAA0B;EAC1CC,eAAe,EAAE,2BAA2B;EAC5CC,eAAe,EAAE;CAClB;AAED,OAAO,MAAMC,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EAEjBC,SAAS,EAAEN,GAAG,CAACC,cAAc;EAC7BM,kBAAkB,EAAEP,GAAG,CAACE,eAAe;EACvCM,eAAe,EAAER,GAAG,CAACG,eAAe;EAEpCM,cAAc,EAAET,GAAG,CAACC,cAAc,GAAG,YAAY;EACjDS,aAAa,EAAEV,GAAG,CAACC,cAAc,GAAG,WAAW;EAC/CU,eAAe,EAAEX,GAAG,CAACC,cAAc,GAAG,gBAAgB;EAEtDW,gBAAgB,EAAEZ,GAAG,CAACC,cAAc,GAAG,cAAc;EACrDY,gBAAgB,EAAEb,GAAG,CAACC,cAAc,GAAG,cAAc;EACrDa,qBAAqB,EAAEd,GAAG,CAACC,cAAc,GAAG,OAAO;EAEnDc,eAAe,EAAEf,GAAG,CAACC,cAAc,GAAG,QAAQ;EAC9Ce,eAAe,EAAEhB,GAAG,CAACC,cAAc,GAAG,QAAQ;EAC9CgB,UAAU,EAAEjB,GAAG,CAACC,cAAc,GAAG,QAAQ;EAEzCiB,aAAa,EAAElB,GAAG,CAACC,cAAc,GAAG,MAAM;EAC1CkB,QAAQ,EAAEnB,GAAG,CAACC,cAAc,GAAG,MAAM;EACrCmB,oBAAoB,EAAEpB,GAAG,CAACC,cAAc,GAAG,wBAAwB;EAEnEoB,iBAAiB,EAAErB,GAAG,CAACC,cAAc,GAAG,MAAM;EAC9CqB,mBAAmB,EAAEtB,GAAG,CAACC,cAAc,GAAG,MAAM;EAEhDsB,0BAA0B,EAAEvB,GAAG,CAACC,cAAc,GAAG,cAAc;EAC/DuB,0BAA0B,EAAExB,GAAG,CAACC,cAAc,GAAG,cAAc;EAC/DwB,wBAAwB,EAAEzB,GAAG,CAACC,cAAc,GAAG,YAAY;EAE3DyB,iBAAiB,EAAE1B,GAAG,CAACC,cAAc,GAAG;CACzC;AAED;;;;;;;AAOA", "names": ["API", "BASEPATH_LOCAL", "BASEPATH_CAMERA", "BASEPATH_WEBRTC", "environment", "production", "ICCC_AUTH", "ICCC_CAMERA_PYTHON", "ICCC_WEBRTC_API", "ICCC_EVS_KAFKA", "ICCC_BOOKMARK", "ICCC_EVS_DEVICE", "ICCC_ITMS_CAMERA", "ICCC_IVMS_CAMERA", "ICCC_MAGA_IVMS_CAMERA", "ICCC_IVMS_ALERT", "ICCC_ITMS_ALERT", "ICCC_EVENT", "ICCC_ITMS_SOP", "ICCC_SOP", "ICCC_USER_DEPARTMENT", "ICCC_MAP_BASEPATH", "ICCC_ASSET_BASEPATH", "ICCC_ALERT_REPORT_BASEPATH", "ICCC_EVENT_REPORT_BASEPATH", "ICCC_SOP_REPORT_BASEPATH", "ICCC_SOS_BASEPATH"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\n\nexport const API = {\n  BASEPATH_LOCAL: 'http://206.1.32.64:9108/',\n  BASEPATH_CAMERA: 'http://172.16.19.56:8000/',\n  BASEPATH_WEBRTC: 'http://172.16.19.56:8000'\n};\n\nexport const environment = {\n  production: false,\n\n  ICCC_AUTH: API.BASEPATH_LOCAL,\n  ICCC_CAMERA_PYTHON: API.BASEPATH_CAMERA,\n  ICCC_WEBRTC_API: API.BASEPATH_WEBRTC,\n\n  ICCC_EVS_KAFKA: API.BASEPATH_LOCAL + 'kafka-evs/',\n  ICCC_BOOKMARK: API.BASEPATH_LOCAL + 'bookmark/',\n  ICCC_EVS_DEVICE: API.BASEPATH_LOCAL + 'evsDevice-api/',\n\n  ICCC_ITMS_CAMERA: API.BASEPATH_LOCAL + 'itms-camera/',\n  ICCC_IVMS_CAMERA: API.BASEPATH_LOCAL + 'ivms-camera/',\n  ICCC_MAGA_IVMS_CAMERA: API.BASEPATH_LOCAL + 'mega/',\n\n  ICCC_IVMS_ALERT: API.BASEPATH_LOCAL + 'alert/',\n  ICCC_ITMS_ALERT: API.BASEPATH_LOCAL + 'alert/',\n  ICCC_EVENT: API.BASEPATH_LOCAL + 'event/',\n\n  ICCC_ITMS_SOP: API.BASEPATH_LOCAL + 'sop/',\n  ICCC_SOP: API.BASEPATH_LOCAL + 'sop/',\n  ICCC_USER_DEPARTMENT: API.BASEPATH_LOCAL + 'department-management/',\n\n  ICCC_MAP_BASEPATH: API.BASEPATH_LOCAL + 'map/',\n  ICCC_ASSET_BASEPATH: API.BASEPATH_LOCAL + 'map/',\n\n  ICCC_ALERT_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'alertreport/',\n  ICCC_EVENT_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'eventreport/',\n  ICCC_SOP_REPORT_BASEPATH: API.BASEPATH_LOCAL + 'sopreport/',\n\n  ICCC_SOS_BASEPATH: API.BASEPATH_LOCAL + 'sos/'\n};\n\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/dist/zone-error';  // Included with Angular CLI.\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}