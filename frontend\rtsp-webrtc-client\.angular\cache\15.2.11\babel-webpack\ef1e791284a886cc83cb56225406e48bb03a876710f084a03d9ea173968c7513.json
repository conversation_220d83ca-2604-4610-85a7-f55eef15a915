{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function (...args) {\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function (...args) {\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n  return function (...args) {\n    const subject = new AsyncSubject();\n    let uninitialized = true;\n    return new Observable(subscriber => {\n      const subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        let isAsync = false;\n        let isComplete = false;\n        callbackFunc.apply(this, [...args, (...results) => {\n          if (isNodeStyle) {\n            const err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete = true;\n          if (isAsync) {\n            subject.complete();\n          }\n        }]);\n        if (isComplete) {\n          subject.complete();\n        }\n        isAsync = true;\n      }\n      return subs;\n    });\n  };\n}\n//# sourceMappingURL=bindCallbackInternals.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}