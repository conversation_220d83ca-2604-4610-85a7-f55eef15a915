{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { WebRTCCameraComponent } from './components/webrtc-camera.component';\nimport { StreamViewerComponent } from './components/stream-viewer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/cameras',\n  pathMatch: 'full'\n}, {\n  path: 'cameras',\n  component: WebRTCCameraComponent\n}, {\n  path: 'viewer',\n  component: StreamViewerComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;;;AAE5E,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,EACvD;EAAEF,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEN;AAAqB,CAAE,EACrD;EAAEG,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEL;AAAqB,CAAE,CACrD;AAMD,OAAM,MAAOM,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBR,YAAY,CAACS,OAAO,CAACN,MAAM,CAAC,EAC5BH,YAAY;IAAA;EAAA;;;2EAEXQ,gBAAgB;IAAAE;IAAAC,UAFjBX,YAAY;EAAA;AAAA", "names": ["RouterModule", "WebRTCCameraComponent", "StreamViewerComponent", "routes", "path", "redirectTo", "pathMatch", "component", "AppRoutingModule", "forRoot", "imports", "exports"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { WebRTCCameraComponent } from './components/webrtc-camera.component';\nimport { StreamViewerComponent } from './components/stream-viewer.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/cameras', pathMatch: 'full' },\n  { path: 'cameras', component: WebRTCCameraComponent },\n  { path: 'viewer', component: StreamViewerComponent }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}