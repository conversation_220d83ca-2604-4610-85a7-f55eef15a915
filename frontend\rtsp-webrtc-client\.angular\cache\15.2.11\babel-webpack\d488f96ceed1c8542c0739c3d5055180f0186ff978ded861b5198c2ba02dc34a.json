{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebRTCService } from '../services/webrtc.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/webrtc.service\";\nimport * as i2 from \"@angular/common\";\nfunction WebRTCCameraComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12)(3, \"h6\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14)(6, \"input\", 15);\n    i0.ɵɵlistener(\"change\", function WebRTCCameraComponent_div_8_Template_input_change_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const camera_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleStream(camera_r3, $event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 16);\n    i0.ɵɵelement(8, \"video\", 17);\n    i0.ɵɵelementStart(9, \"small\", 18);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const camera_r3 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(camera_r3.channelname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", camera_r3.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"video-\" + camera_r3.channelid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"IP: \", camera_r3.channelip, \"\");\n  }\n}\nfunction WebRTCCameraComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function WebRTCCameraComponent_div_9_div_4_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const stream_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.stopStreamById(stream_r7.streamId || \"\", stream_r7));\n    });\n    i0.ɵɵtext(5, \" Stop \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const stream_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", stream_r7.channelname, \" - \", stream_r7.streamId, \"\");\n  }\n}\nfunction WebRTCCameraComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h5\");\n    i0.ɵɵtext(2, \"Active Streams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, WebRTCCameraComponent_div_9_div_4_Template, 6, 2, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activeStreams);\n  }\n}\nfunction WebRTCCameraComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.errorMessage, \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-success\": a0,\n    \"bg-warning\": a1,\n    \"bg-danger\": a2\n  };\n};\nexport class WebRTCCameraComponent {\n  constructor(webrtcService) {\n    this.webrtcService = webrtcService;\n    this.camera = [];\n    this.filteredCamera = [];\n    this.searchTerm = '';\n    // WebRTC properties\n    this.connectionState = 'disconnected';\n    this.errorMessage = null;\n    this.activeStreams = [];\n    // Store individual WebRTC services for each camera\n    this.cameraWebRTCServices = new Map();\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.getCamera();\n    this.setupGlobalWebRTCSubscriptions();\n    // Clean up session storage on page unload\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopAllStreams();\n  }\n  setupGlobalWebRTCSubscriptions() {\n    // Subscribe to global connection state changes\n    this.subscriptions.push(this.webrtcService.connectionState$.subscribe(state => {\n      this.connectionState = state;\n    }));\n  }\n  getCamera() {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [{\n        channelid: 1,\n        channelname: 'Camera 1',\n        channelip: '*************',\n        username: 'admin',\n        password: 'Admin@123',\n        checked: false\n      }, {\n        channelid: 2,\n        channelname: 'Camera 2',\n        channelip: '*************',\n        username: 'admin',\n        password: 'Admin@123',\n        checked: false\n      }, {\n        channelid: 3,\n        channelname: 'Camera 3',\n        channelip: '*************',\n        username: 'admin',\n        password: 'Admin@123',\n        checked: false\n      }];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n  toggleStream(camera, event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const checked = event.target.checked;\n      // Update camera state immediately for UI responsiveness\n      _this.updateCameraCheckedState(camera.channelip, camera.channelid, checked);\n      if (checked) {\n        yield _this.startStream(camera);\n      } else {\n        yield _this.stopStream(camera);\n      }\n    })();\n  }\n  startStream(camera) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.clearError();\n        const rtsp = `rtsp://${camera.username}:${camera.password}@${camera.channelip}/profile2`;\n        // Create individual WebRTC service for this camera\n        const cameraWebRTCService = WebRTCService.createInstance();\n        _this2.cameraWebRTCServices.set(camera.channelid, cameraWebRTCService);\n        // Subscribe to this camera's stream\n        const streamSubscription = cameraWebRTCService.stream$.subscribe(stream => {\n          if (stream) {\n            _this2.playWebRTC(stream, camera.channelid);\n          }\n        });\n        _this2.subscriptions.push(streamSubscription);\n        // Start the stream\n        const response = yield cameraWebRTCService.startStream({\n          rtsp: rtsp,\n          camera: camera.channelid\n        });\n        // Update camera state\n        camera.streamId = response.stream_id;\n        camera.webrtcUrl = response.webrtc_url;\n        // Add to active streams if not already there\n        if (!_this2.activeStreams.find(s => s.channelid === camera.channelid)) {\n          _this2.activeStreams.push(camera);\n        }\n        // Update session storage\n        _this2.updateSessionStorage();\n        console.log('Stream started successfully:', response);\n      } catch (error) {\n        console.error('Failed to start stream:', error);\n        _this2.errorMessage = `Failed to start stream for ${camera.channelname}: ${error}`;\n        // Revert checked state on error\n        _this2.updateCameraCheckedState(camera.channelip, camera.channelid, false);\n      }\n    })();\n  }\n  // Method to play WebRTC stream similar to HLS playStream\n  playWebRTC(stream, cameraId) {\n    const videoElement = document.getElementById(`video-${cameraId}`);\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing video:', error);\n      });\n    }\n  }\n  stopStream(camera) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Get the individual WebRTC service for this camera\n        const cameraWebRTCService = _this3.cameraWebRTCServices.get(camera.channelid);\n        if (cameraWebRTCService && camera.streamId) {\n          yield cameraWebRTCService.stopStream(camera.streamId);\n        }\n        // Clean up the service\n        _this3.cameraWebRTCServices.delete(camera.channelid);\n        // Update camera state\n        camera.streamId = undefined;\n        camera.webrtcUrl = undefined;\n        // Remove from active streams\n        _this3.activeStreams = _this3.activeStreams.filter(s => s.channelid !== camera.channelid);\n        // Clear video element\n        const videoElement = document.getElementById(`video-${camera.channelid}`);\n        if (videoElement) {\n          videoElement.srcObject = null;\n        }\n        // Update session storage\n        _this3.updateSessionStorage();\n        console.log('Stream stopped successfully');\n      } catch (error) {\n        console.error('Failed to stop stream:', error);\n        _this3.errorMessage = `Failed to stop stream for ${camera.channelname}: ${error}`;\n      }\n    })();\n  }\n  stopStreamById(_streamId, camera) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4.stopStream(camera);\n    })();\n  }\n  stopAllStreams() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      // Create a copy of activeStreams to avoid modification during iteration\n      const streamsToStop = [..._this5.activeStreams];\n      for (const camera of streamsToStop) {\n        yield _this5.stopStream(camera);\n      }\n    })();\n  }\n  onSearchKeyup(event) {\n    this.searchTerm = event.target.value;\n    if (this.searchTerm && this.searchTerm.trim() !== '') {\n      this.filteredCamera = this.camera.filter(camera => camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    } else {\n      // Reload from session storage if search is cleared\n      const storedData = sessionStorage.getItem('cameraDataWebRTC');\n      if (storedData) {\n        this.camera = JSON.parse(storedData);\n        this.filteredCamera = this.camera;\n      } else {\n        this.getCamera();\n      }\n    }\n  }\n  // Helper method to update camera checked state similar to HLS version\n  updateCameraCheckedState(channelip, channelid, checked) {\n    // Update main camera array\n    this.camera = this.camera.map(cam => {\n      if (cam.channelip === channelip && cam.channelid === channelid) {\n        return {\n          ...cam,\n          checked: checked\n        };\n      }\n      return cam;\n    });\n    // Update filtered camera array\n    this.filteredCamera = this.filteredCamera.map(cam => {\n      if (cam.channelip === channelip && cam.channelid === channelid) {\n        return {\n          ...cam,\n          checked: checked\n        };\n      }\n      return cam;\n    });\n    // Update session storage\n    this.updateSessionStorage();\n  }\n  // Get active streams similar to HLS version\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n  updateSessionStorage() {\n    sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n  }\n  clearError() {\n    this.errorMessage = null;\n  }\n  static {\n    this.ɵfac = function WebRTCCameraComponent_Factory(t) {\n      return new (t || WebRTCCameraComponent)(i0.ɵɵdirectiveInject(i1.WebRTCService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WebRTCCameraComponent,\n      selectors: [[\"app-webrtc-camera\"]],\n      decls: 15,\n      vars: 11,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [1, \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Search cameras...\", 1, \"form-control\", 3, \"keyup\"], [\"class\", \"col-md-6 col-lg-4 mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-3\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-3\", 4, \"ngIf\"], [1, \"col-md-6\", \"col-lg-4\", \"mb-3\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"card-body\"], [\"controls\", \"\", \"muted\", \"\", \"autoplay\", \"\", 1, \"w-100\", 2, \"height\", \"200px\", \"background\", \"#000\", 3, \"id\"], [1, \"text-muted\"], [1, \"mt-4\"], [1, \"list-group\"], [\"class\", \"list-group-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [1, \"alert\", \"alert-danger\", \"mt-3\"]],\n      template: function WebRTCCameraComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n          i0.ɵɵtext(4, \"WebRTC Camera Streaming\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"input\", 4);\n          i0.ɵɵlistener(\"keyup\", function WebRTCCameraComponent_Template_input_keyup_6_listener($event) {\n            return ctx.onSearchKeyup($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 1);\n          i0.ɵɵtemplate(8, WebRTCCameraComponent_div_8_Template, 11, 4, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, WebRTCCameraComponent_div_9_Template, 5, 1, \"div\", 6);\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"span\", 8);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"titlecase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, WebRTCCameraComponent_div_14_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredCamera);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeStreams.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx.connectionState === \"connected\", ctx.connectionState === \"connecting\", ctx.connectionState === \"disconnected\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 5, ctx.connectionState), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.TitleCasePipe],\n      styles: [\".card[_ngcontent-%COMP%] {\\n      transition: transform 0.2s;\\n    }\\n    .card[_ngcontent-%COMP%]:hover {\\n      transform: translateY(-2px);\\n    }\\n    video[_ngcontent-%COMP%] {\\n      border-radius: 4px;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy93ZWJydGMtY2FtZXJhLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSwwQkFBMEI7SUFDNUI7SUFDQTtNQUNFLDJCQUEyQjtJQUM3QjtJQUNBO01BQ0Usa0JBQWtCO0lBQ3BCIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmNhcmQge1xuICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnM7XG4gICAgfVxuICAgIC5jYXJkOmhvdmVyIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICB9XG4gICAgdmlkZW8ge1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAEA,SAASA,aAAa,QAAuC,4BAA4B;;;;;;;IAsB7EC,+BAA0E;IAGnDA,YAAsB;IAAAA,iBAAK;IAC5CA,+BAAoC;IAKhCA;MAAA;MAAA;MAAA;MAAA,OAAUA,qDAA4B;IAAA,EAAC;IAJzCA,iBAI0C;IAG9CA,+BAAuB;IACrBA,4BAOQ;IACRA,iCAA0B;IAAAA,aAAwB;IAAAA,iBAAQ;;;;IAlBzCA,eAAsB;IAAtBA,2CAAsB;IAKnCA,eAA0B;IAA1BA,2CAA0B;IAM5BA,eAAkC;IAAlCA,mDAAkC;IAOVA,eAAwB;IAAxBA,sDAAwB;;;;;;IAUtDA,+BAAkE;IAExDA,YAA4C;IAAAA,iBAAO;IACzDA,kCAE0D;IAAxDA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAkC,EAAE,YAAS;IAAA,EAAC;IACvDA,sBACF;IAAAA,iBAAS;;;;IALHA,eAA4C;IAA5CA,+EAA4C;;;;;IAL1DA,+BAAmD;IAC7CA,8BAAc;IAAAA,iBAAK;IACvBA,+BAAwB;IACtBA,6EASM;IACRA,iBAAM;;;;IAV4CA,eAAgB;IAAhBA,8CAAgB;;;;;IAyBpEA,+BAA0D;IACxDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,oDACF;;;;;;;;;;AAiBV,OAAM,MAAOC,qBAAqB;EAchCC,YACUC,aAA4B;IAA5B,kBAAa,GAAbA,aAAa;IAdvB,WAAM,GAAoB,EAAE;IAC5B,mBAAc,GAAoB,EAAE;IACpC,eAAU,GAAW,EAAE;IAEvB;IACA,oBAAe,GAAW,cAAc;IACxC,iBAAY,GAAkB,IAAI;IAClC,kBAAa,GAAoB,EAAE;IAEnC;IACQ,yBAAoB,GAA+B,IAAIC,GAAG,EAAE;IAC5D,kBAAa,GAAmB,EAAE;EAIvC;EAEHC,QAAQ;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,8BAA8B,EAAE;IAErC;IACAC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAK;MAC3CC,cAAc,CAACC,UAAU,CAAC,kBAAkB,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAC,WAAW;IACT,IAAI,CAACC,aAAa,CAACC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQV,8BAA8B;IACpC;IACA,IAAI,CAACM,aAAa,CAACK,IAAI,CACrB,IAAI,CAACf,aAAa,CAACgB,gBAAgB,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAI,CAACC,eAAe,GAAGD,KAAK;IAC9B,CAAC,CAAC,CACH;EACH;EAEAf,SAAS;IACP,MAAMiB,UAAU,GAAGb,cAAc,CAACc,OAAO,CAAC,kBAAkB,CAAC;IAC7D,IAAID,UAAU,EAAE;MACd,IAAI,CAACE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MACpC,IAAI,CAACK,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL;MACA,IAAI,CAACA,MAAM,GAAG,CACZ;QAAEI,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,SAAS,EAAE,eAAe;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAE,EAC/H;QAAEL,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,SAAS,EAAE,eAAe;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAE,EAC/H;QAAEL,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,SAAS,EAAE,eAAe;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAE,CAChI;MACD,IAAI,CAACN,cAAc,GAAG,IAAI,CAACH,MAAM;MACjCf,cAAc,CAACyB,OAAO,CAAC,kBAAkB,EAAET,IAAI,CAACU,SAAS,CAAC,IAAI,CAACX,MAAM,CAAC,CAAC;;EAE3E;EAEMY,YAAY,CAACZ,MAAqB,EAAEa,KAAU;IAAA;IAAA;MAClD,MAAMJ,OAAO,GAAGI,KAAK,CAACC,MAAM,CAACL,OAAO;MAEpC;MACA,KAAI,CAACM,wBAAwB,CAACf,MAAM,CAACM,SAAS,EAAEN,MAAM,CAACI,SAAS,EAAEK,OAAO,CAAC;MAE1E,IAAIA,OAAO,EAAE;QACX,MAAM,KAAI,CAACO,WAAW,CAAChB,MAAM,CAAC;OAC/B,MAAM;QACL,MAAM,KAAI,CAACiB,UAAU,CAACjB,MAAM,CAAC;;IAC9B;EACH;EAEMgB,WAAW,CAAChB,MAAqB;IAAA;IAAA;MACrC,IAAI;QACF,MAAI,CAACkB,UAAU,EAAE;QAEjB,MAAMC,IAAI,GAAG,UAAUnB,MAAM,CAACO,QAAQ,IAAIP,MAAM,CAACQ,QAAQ,IAAIR,MAAM,CAACM,SAAS,WAAW;QAExF;QACA,MAAMc,mBAAmB,GAAG9C,aAAa,CAAC+C,cAAc,EAAE;QAC1D,MAAI,CAACC,oBAAoB,CAACC,GAAG,CAACvB,MAAM,CAACI,SAAS,EAAEgB,mBAAmB,CAAC;QAEpE;QACA,MAAMI,kBAAkB,GAAGJ,mBAAmB,CAACK,OAAO,CAAC9B,SAAS,CAAC+B,MAAM,IAAG;UACxE,IAAIA,MAAM,EAAE;YACV,MAAI,CAACC,UAAU,CAACD,MAAM,EAAE1B,MAAM,CAACI,SAAS,CAAC;;QAE7C,CAAC,CAAC;QACF,MAAI,CAAChB,aAAa,CAACK,IAAI,CAAC+B,kBAAkB,CAAC;QAE3C;QACA,MAAMI,QAAQ,SAASR,mBAAmB,CAACJ,WAAW,CAAC;UACrDG,IAAI,EAAEA,IAAI;UACVnB,MAAM,EAAEA,MAAM,CAACI;SAChB,CAAC;QAEF;QACAJ,MAAM,CAAC6B,QAAQ,GAAGD,QAAQ,CAACE,SAAS;QACpC9B,MAAM,CAAC+B,SAAS,GAAGH,QAAQ,CAACI,UAAU;QAEtC;QACA,IAAI,CAAC,MAAI,CAACC,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,SAAS,KAAKJ,MAAM,CAACI,SAAS,CAAC,EAAE;UACnE,MAAI,CAAC6B,aAAa,CAACxC,IAAI,CAACO,MAAM,CAAC;;QAGjC;QACA,MAAI,CAACoC,oBAAoB,EAAE;QAE3BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEV,QAAQ,CAAC;OAEtD,CAAC,OAAOW,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAI,CAACC,YAAY,GAAG,8BAA8BxC,MAAM,CAACK,WAAW,KAAKkC,KAAK,EAAE;QAChF;QACA,MAAI,CAACxB,wBAAwB,CAACf,MAAM,CAACM,SAAS,EAAEN,MAAM,CAACI,SAAS,EAAE,KAAK,CAAC;;IACzE;EACH;EAEA;EACAuB,UAAU,CAACD,MAAmB,EAAEe,QAAgB;IAC9C,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASH,QAAQ,EAAE,CAAqB;IACrF,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACG,SAAS,GAAGnB,MAAM;MAC/BgB,YAAY,CAACI,IAAI,EAAE,CAACC,KAAK,CAACR,KAAK,IAAG;QAChCF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;;EAEN;EAEMtB,UAAU,CAACjB,MAAqB;IAAA;IAAA;MACpC,IAAI;QACF;QACA,MAAMoB,mBAAmB,GAAG,MAAI,CAACE,oBAAoB,CAAC0B,GAAG,CAAChD,MAAM,CAACI,SAAS,CAAC;QAE3E,IAAIgB,mBAAmB,IAAIpB,MAAM,CAAC6B,QAAQ,EAAE;UAC1C,MAAMT,mBAAmB,CAACH,UAAU,CAACjB,MAAM,CAAC6B,QAAQ,CAAC;;QAGvD;QACA,MAAI,CAACP,oBAAoB,CAAC2B,MAAM,CAACjD,MAAM,CAACI,SAAS,CAAC;QAElD;QACAJ,MAAM,CAAC6B,QAAQ,GAAGqB,SAAS;QAC3BlD,MAAM,CAAC+B,SAAS,GAAGmB,SAAS;QAE5B;QACA,MAAI,CAACjB,aAAa,GAAG,MAAI,CAACA,aAAa,CAACkB,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAAC/B,SAAS,KAAKJ,MAAM,CAACI,SAAS,CAAC;QAErF;QACA,MAAMsC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS5C,MAAM,CAACI,SAAS,EAAE,CAAqB;QAC7F,IAAIsC,YAAY,EAAE;UAChBA,YAAY,CAACG,SAAS,GAAG,IAAI;;QAG/B;QACA,MAAI,CAACT,oBAAoB,EAAE;QAE3BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;OAE3C,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAI,CAACC,YAAY,GAAG,6BAA6BxC,MAAM,CAACK,WAAW,KAAKkC,KAAK,EAAE;;IAChF;EACH;EAEMa,cAAc,CAACC,SAAiB,EAAErD,MAAqB;IAAA;IAAA;MAC3D,MAAM,MAAI,CAACiB,UAAU,CAACjB,MAAM,CAAC;IAAC;EAChC;EAEMR,cAAc;IAAA;IAAA;MAClB;MACA,MAAM8D,aAAa,GAAG,CAAC,GAAG,MAAI,CAACrB,aAAa,CAAC;MAC7C,KAAK,MAAMjC,MAAM,IAAIsD,aAAa,EAAE;QAClC,MAAM,MAAI,CAACrC,UAAU,CAACjB,MAAM,CAAC;;IAC9B;EACH;EAEAuD,aAAa,CAAC1C,KAAoB;IAChC,IAAI,CAAC2C,UAAU,GAAI3C,KAAK,CAACC,MAA2B,CAAC2C,KAAK;IAE1D,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACA,UAAU,CAACE,IAAI,EAAE,KAAK,EAAE,EAAE;MACpD,IAAI,CAACvD,cAAc,GAAG,IAAI,CAACH,MAAM,CAACmD,MAAM,CAACnD,MAAM,IAC7CA,MAAM,CAACK,WAAW,CAACsD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACG,WAAW,EAAE,CAAC,CACzE;KACF,MAAM;MACL;MACA,MAAM7D,UAAU,GAAGb,cAAc,CAACc,OAAO,CAAC,kBAAkB,CAAC;MAC7D,IAAID,UAAU,EAAE;QACd,IAAI,CAACE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QACpC,IAAI,CAACK,cAAc,GAAG,IAAI,CAACH,MAAM;OAClC,MAAM;QACL,IAAI,CAACnB,SAAS,EAAE;;;EAGtB;EAEA;EACQkC,wBAAwB,CAACT,SAAiB,EAAEF,SAAiB,EAAEK,OAAgB;IACrF;IACA,IAAI,CAACT,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC6D,GAAG,CAACC,GAAG,IAAG;MAClC,IAAIA,GAAG,CAACxD,SAAS,KAAKA,SAAS,IAAIwD,GAAG,CAAC1D,SAAS,KAAKA,SAAS,EAAE;QAC9D,OAAO;UAAE,GAAG0D,GAAG;UAAErD,OAAO,EAAEA;QAAO,CAAE;;MAErC,OAAOqD,GAAG;IACZ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3D,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC0D,GAAG,CAACC,GAAG,IAAG;MAClD,IAAIA,GAAG,CAACxD,SAAS,KAAKA,SAAS,IAAIwD,GAAG,CAAC1D,SAAS,KAAKA,SAAS,EAAE;QAC9D,OAAO;UAAE,GAAG0D,GAAG;UAAErD,OAAO,EAAEA;QAAO,CAAE;;MAErC,OAAOqD,GAAG;IACZ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1B,oBAAoB,EAAE;EAC7B;EAEA;EACA,IAAI2B,UAAU;IACZ,OAAO,IAAI,CAAC/D,MAAM,GAAG,IAAI,CAACA,MAAM,CAACmD,MAAM,CAACa,IAAI,IAAIA,IAAI,CAACvD,OAAO,CAAC,GAAG,EAAE;EACpE;EAEQ2B,oBAAoB;IAC1BnD,cAAc,CAACyB,OAAO,CAAC,kBAAkB,EAAET,IAAI,CAACU,SAAS,CAAC,IAAI,CAACX,MAAM,CAAC,CAAC;EACzE;EAEQkB,UAAU;IAChB,IAAI,CAACsB,YAAY,GAAG,IAAI;EAC1B;;;uBArOWhE,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAyF;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UA3F9B9F,8BAA6B;UAGnBA,uCAAuB;UAAAA,iBAAK;UAGhCA,8BAAkB;UAKdA;YAAA,OAAS+F,yBAAqB;UAAA,EAAC;UAJjC/F,iBAIkC;UAIpCA,8BAAiB;UACfA,uEAwBM;UACRA,iBAAM;UAGNA,sEAcM;UAGNA,+BAAkB;UAMdA,aACF;;UAAAA,iBAAO;UAITA,wEAEM;UACRA,iBAAM;;;UA3DqDA,eAAiB;UAAjBA,4CAAiB;UA4BvDA,eAA8B;UAA9BA,mDAA8B;UAkB3BA,eAIlB;UAJkBA,uKAIlB;UACAA,eACF;UADEA,2EACF;UAIoCA,eAAkB;UAAlBA,uCAAkB", "names": ["WebRTCService", "i0", "WebRTCCameraComponent", "constructor", "webrtcService", "Map", "ngOnInit", "getCamera", "setupGlobalWebRTCSubscriptions", "window", "addEventListener", "sessionStorage", "removeItem", "ngOnDestroy", "subscriptions", "for<PERSON>ach", "sub", "unsubscribe", "stopAllStreams", "push", "connectionState$", "subscribe", "state", "connectionState", "storedData", "getItem", "camera", "JSON", "parse", "filteredCamera", "channelid", "channelname", "channelip", "username", "password", "checked", "setItem", "stringify", "toggleStream", "event", "target", "updateCameraCheckedState", "startStream", "stopStream", "clearError", "rtsp", "cameraWebRTCService", "createInstance", "cameraWebRTCServices", "set", "streamSubscription", "stream$", "stream", "playWebRTC", "response", "streamId", "stream_id", "webrtcUrl", "webrtc_url", "activeStreams", "find", "s", "updateSessionStorage", "console", "log", "error", "errorMessage", "cameraId", "videoElement", "document", "getElementById", "srcObject", "play", "catch", "get", "delete", "undefined", "filter", "stopStreamById", "_streamId", "streamsToStop", "onSearchKeyup", "searchTerm", "value", "trim", "toLowerCase", "includes", "map", "cam", "resultLive", "item", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\components\\webrtc-camera.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ElementRef, ViewChild } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { WebRTCService, StreamRequest, StreamResponse } from '../services/webrtc.service';\nimport { CameraChannel } from '../models/camera.models';\n\n@Component({\n  selector: 'app-webrtc-camera',\n  template: `\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <h3>WebRTC Camera Streaming</h3>\n\n          <!-- Search -->\n          <div class=\"mb-3\">\n            <input\n              type=\"text\"\n              class=\"form-control\"\n              placeholder=\"Search cameras...\"\n              (keyup)=\"onSearchKeyup($event)\">\n          </div>\n\n          <!-- Camera List -->\n          <div class=\"row\">\n            <div class=\"col-md-6 col-lg-4 mb-3\" *ngFor=\"let camera of filteredCamera\">\n              <div class=\"card\">\n                <div class=\"card-header d-flex justify-content-between align-items-center\">\n                  <h6 class=\"mb-0\">{{camera.channelname}}</h6>\n                  <div class=\"form-check form-switch\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      [checked]=\"camera.checked\"\n                      (change)=\"toggleStream(camera, $event)\">\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <video\n                    [id]=\"'video-' + camera.channelid\"\n                    class=\"w-100\"\n                    style=\"height: 200px; background: #000;\"\n                    controls\n                    muted\n                    autoplay>\n                  </video>\n                  <small class=\"text-muted\">IP: {{camera.channelip}}</small>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Active Streams -->\n          <div class=\"mt-4\" *ngIf=\"activeStreams.length > 0\">\n            <h5>Active Streams</h5>\n            <div class=\"list-group\">\n              <div class=\"list-group-item\" *ngFor=\"let stream of activeStreams\">\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span>{{stream.channelname}} - {{stream.streamId}}</span>\n                  <button\n                    class=\"btn btn-sm btn-danger\"\n                    (click)=\"stopStreamById(stream.streamId || '', stream)\">\n                    Stop\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Connection Status -->\n          <div class=\"mt-3\">\n            <span class=\"badge\" [ngClass]=\"{\n              'bg-success': connectionState === 'connected',\n              'bg-warning': connectionState === 'connecting',\n              'bg-danger': connectionState === 'disconnected'\n            }\">\n              {{connectionState | titlecase}}\n            </span>\n          </div>\n\n          <!-- Error Message -->\n          <div class=\"alert alert-danger mt-3\" *ngIf=\"errorMessage\">\n            {{errorMessage}}\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .card {\n      transition: transform 0.2s;\n    }\n    .card:hover {\n      transform: translateY(-2px);\n    }\n    video {\n      border-radius: 4px;\n    }\n  `]\n})\nexport class WebRTCCameraComponent implements OnInit, OnDestroy {\n  camera: CameraChannel[] = [];\n  filteredCamera: CameraChannel[] = [];\n  searchTerm: string = '';\n\n  // WebRTC properties\n  connectionState: string = 'disconnected';\n  errorMessage: string | null = null;\n  activeStreams: CameraChannel[] = [];\n\n  // Store individual WebRTC services for each camera\n  private cameraWebRTCServices: Map<number, WebRTCService> = new Map();\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private webrtcService: WebRTCService\n  ) {}\n\n  ngOnInit(): void {\n    this.getCamera();\n    this.setupGlobalWebRTCSubscriptions();\n\n    // Clean up session storage on page unload\n    window.addEventListener('beforeunload', () => {\n      sessionStorage.removeItem('cameraDataWebRTC');\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.stopAllStreams();\n  }\n\n  private setupGlobalWebRTCSubscriptions(): void {\n    // Subscribe to global connection state changes\n    this.subscriptions.push(\n      this.webrtcService.connectionState$.subscribe(state => {\n        this.connectionState = state;\n      })\n    );\n  }\n\n  getCamera(): void {\n    const storedData = sessionStorage.getItem('cameraDataWebRTC');\n    if (storedData) {\n      this.camera = JSON.parse(storedData);\n      this.filteredCamera = this.camera;\n    } else {\n      // For demo purposes, using mock data. Replace with actual API call\n      this.camera = [\n        { channelid: 1, channelname: 'Camera 1', channelip: '*************', username: 'admin', password: 'Admin@123', checked: false },\n        { channelid: 2, channelname: 'Camera 2', channelip: '*************', username: 'admin', password: 'Admin@123', checked: false },\n        { channelid: 3, channelname: 'Camera 3', channelip: '*************', username: 'admin', password: 'Admin@123', checked: false }\n      ];\n      this.filteredCamera = this.camera;\n      sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n    }\n  }\n\n  async toggleStream(camera: CameraChannel, event: any): Promise<void> {\n    const checked = event.target.checked;\n\n    // Update camera state immediately for UI responsiveness\n    this.updateCameraCheckedState(camera.channelip, camera.channelid, checked);\n\n    if (checked) {\n      await this.startStream(camera);\n    } else {\n      await this.stopStream(camera);\n    }\n  }\n\n  async startStream(camera: CameraChannel): Promise<void> {\n    try {\n      this.clearError();\n\n      const rtsp = `rtsp://${camera.username}:${camera.password}@${camera.channelip}/profile2`;\n\n      // Create individual WebRTC service for this camera\n      const cameraWebRTCService = WebRTCService.createInstance();\n      this.cameraWebRTCServices.set(camera.channelid, cameraWebRTCService);\n\n      // Subscribe to this camera's stream\n      const streamSubscription = cameraWebRTCService.stream$.subscribe(stream => {\n        if (stream) {\n          this.playWebRTC(stream, camera.channelid);\n        }\n      });\n      this.subscriptions.push(streamSubscription);\n\n      // Start the stream\n      const response = await cameraWebRTCService.startStream({\n        rtsp: rtsp,\n        camera: camera.channelid\n      });\n\n      // Update camera state\n      camera.streamId = response.stream_id;\n      camera.webrtcUrl = response.webrtc_url;\n\n      // Add to active streams if not already there\n      if (!this.activeStreams.find(s => s.channelid === camera.channelid)) {\n        this.activeStreams.push(camera);\n      }\n\n      // Update session storage\n      this.updateSessionStorage();\n\n      console.log('Stream started successfully:', response);\n\n    } catch (error) {\n      console.error('Failed to start stream:', error);\n      this.errorMessage = `Failed to start stream for ${camera.channelname}: ${error}`;\n      // Revert checked state on error\n      this.updateCameraCheckedState(camera.channelip, camera.channelid, false);\n    }\n  }\n\n  // Method to play WebRTC stream similar to HLS playStream\n  playWebRTC(stream: MediaStream, cameraId: number): void {\n    const videoElement = document.getElementById(`video-${cameraId}`) as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.srcObject = stream;\n      videoElement.play().catch(error => {\n        console.error('Error playing video:', error);\n      });\n    }\n  }\n\n  async stopStream(camera: CameraChannel): Promise<void> {\n    try {\n      // Get the individual WebRTC service for this camera\n      const cameraWebRTCService = this.cameraWebRTCServices.get(camera.channelid);\n\n      if (cameraWebRTCService && camera.streamId) {\n        await cameraWebRTCService.stopStream(camera.streamId);\n      }\n\n      // Clean up the service\n      this.cameraWebRTCServices.delete(camera.channelid);\n\n      // Update camera state\n      camera.streamId = undefined;\n      camera.webrtcUrl = undefined;\n\n      // Remove from active streams\n      this.activeStreams = this.activeStreams.filter(s => s.channelid !== camera.channelid);\n\n      // Clear video element\n      const videoElement = document.getElementById(`video-${camera.channelid}`) as HTMLVideoElement;\n      if (videoElement) {\n        videoElement.srcObject = null;\n      }\n\n      // Update session storage\n      this.updateSessionStorage();\n\n      console.log('Stream stopped successfully');\n\n    } catch (error) {\n      console.error('Failed to stop stream:', error);\n      this.errorMessage = `Failed to stop stream for ${camera.channelname}: ${error}`;\n    }\n  }\n\n  async stopStreamById(_streamId: string, camera: CameraChannel): Promise<void> {\n    await this.stopStream(camera);\n  }\n\n  async stopAllStreams(): Promise<void> {\n    // Create a copy of activeStreams to avoid modification during iteration\n    const streamsToStop = [...this.activeStreams];\n    for (const camera of streamsToStop) {\n      await this.stopStream(camera);\n    }\n  }\n\n  onSearchKeyup(event: KeyboardEvent): void {\n    this.searchTerm = (event.target as HTMLInputElement).value;\n\n    if (this.searchTerm && this.searchTerm.trim() !== '') {\n      this.filteredCamera = this.camera.filter(camera =>\n        camera.channelname.toLowerCase().includes(this.searchTerm.toLowerCase())\n      );\n    } else {\n      // Reload from session storage if search is cleared\n      const storedData = sessionStorage.getItem('cameraDataWebRTC');\n      if (storedData) {\n        this.camera = JSON.parse(storedData);\n        this.filteredCamera = this.camera;\n      } else {\n        this.getCamera();\n      }\n    }\n  }\n\n  // Helper method to update camera checked state similar to HLS version\n  private updateCameraCheckedState(channelip: string, channelid: number, checked: boolean): void {\n    // Update main camera array\n    this.camera = this.camera.map(cam => {\n      if (cam.channelip === channelip && cam.channelid === channelid) {\n        return { ...cam, checked: checked };\n      }\n      return cam;\n    });\n\n    // Update filtered camera array\n    this.filteredCamera = this.filteredCamera.map(cam => {\n      if (cam.channelip === channelip && cam.channelid === channelid) {\n        return { ...cam, checked: checked };\n      }\n      return cam;\n    });\n\n    // Update session storage\n    this.updateSessionStorage();\n  }\n\n  // Get active streams similar to HLS version\n  get resultLive() {\n    return this.camera ? this.camera.filter(item => item.checked) : [];\n  }\n\n  private updateSessionStorage(): void {\n    sessionStorage.setItem('cameraDataWebRTC', JSON.stringify(this.camera));\n  }\n\n  private clearError(): void {\n    this.errorMessage = null;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}