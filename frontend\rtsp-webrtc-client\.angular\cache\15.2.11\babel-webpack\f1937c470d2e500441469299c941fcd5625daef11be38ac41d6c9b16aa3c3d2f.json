{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AppComponent } from './app.component';\nimport { StreamViewerComponent } from './components/stream-viewer.component';\ndescribe('AppComponent', () => {\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [RouterTestingModule],\n      declarations: [AppComponent, StreamViewerComponent]\n    }).compileComponents();\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it(`should have as title 'RTSP WebRTC Client'`, () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app.title).toEqual('RTSP WebRTC Client');\n  });\n});", "map": {"version": 3, "mappings": ";AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,sCAAsC;AAE5EC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5BC,UAAU,iCAAC,aAAW;IACpB,MAAML,OAAO,CAACM,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPN,mBAAmB,CACpB;MACDO,YAAY,EAAE,CACZN,YAAY,EACZC,qBAAqB;KAExB,CAAC,CAACM,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGX,OAAO,CAACY,eAAe,CAACV,YAAY,CAAC;IACrD,MAAMW,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,2CAA2C,EAAE,MAAK;IACnD,MAAMC,OAAO,GAAGX,OAAO,CAACY,eAAe,CAACV,YAAY,CAAC;IACrD,MAAMW,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACjD,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["TestBed", "RouterTestingModule", "AppComponent", "StreamViewerComponent", "describe", "beforeEach", "configureTestingModule", "imports", "declarations", "compileComponents", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "title", "toEqual"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\nimport { RouterTestingModule } from '@angular/router/testing';\r\nimport { AppComponent } from './app.component';\r\nimport { StreamViewerComponent } from './components/stream-viewer.component';\r\n\r\ndescribe('AppComponent', () => {\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [\r\n        RouterTestingModule\r\n      ],\r\n      declarations: [\r\n        AppComponent,\r\n        StreamViewerComponent\r\n      ],\r\n    }).compileComponents();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app).toBeTruthy();\r\n  });\r\n\r\n  it(`should have as title 'RTSP WebRTC Client'`, () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app.title).toEqual('RTSP WebRTC Client');\r\n  });\r\n});\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}