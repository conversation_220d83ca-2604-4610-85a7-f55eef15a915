{"ast": null, "code": "import _asyncToGenerator from \"E:/test_stream/frontend/rtsp-webrtc-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { provideZonelessChangeDetection } from '@angular/core';\nimport { TestBed } from '@angular/core/testing';\nimport { App } from './app';\ndescribe('App', () => {\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [App],\n      providers: [provideZonelessChangeDetection()]\n    }).compileComponents();\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(App);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it('should render title', () => {\n    const fixture = TestBed.createComponent(App);\n    fixture.detectChanges();\n    const compiled = fixture.nativeElement;\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, rtsp-webrtc-client');\n  });\n});", "map": {"version": 3, "mappings": ";AAAA,SAASA,8BAA8B,QAAQ,eAAe;AAC9D,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,GAAG,QAAQ,OAAO;AAE3BC,QAAQ,CAAC,KAAK,EAAE,MAAK;EACnBC,UAAU,iCAAC,aAAW;IACpB,MAAMH,OAAO,CAACI,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACJ,GAAG,CAAC;MACdK,SAAS,EAAE,CAACP,8BAA8B,EAAE;KAC7C,CAAC,CAACQ,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACT,GAAG,CAAC;IAC5C,MAAMU,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACT,GAAG,CAAC;IAC5CQ,OAAO,CAACM,aAAa,EAAE;IACvB,MAAMC,QAAQ,GAAGP,OAAO,CAACQ,aAA4B;IACrDJ,MAAM,CAACG,QAAQ,CAACE,aAAa,CAAC,IAAI,CAAC,EAAEC,WAAW,CAAC,CAACC,SAAS,CAAC,2BAA2B,CAAC;EAC1F,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["provideZonelessChangeDetection", "TestBed", "App", "describe", "beforeEach", "configureTestingModule", "imports", "providers", "compileComponents", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "detectChanges", "compiled", "nativeElement", "querySelector", "textContent", "toContain"], "sourceRoot": "", "sources": ["E:\\test_stream\\frontend\\rtsp-webrtc-client\\src\\app\\app.spec.ts"], "sourcesContent": ["import { provideZonelessChangeDetection } from '@angular/core';\r\nimport { TestBed } from '@angular/core/testing';\r\nimport { App } from './app';\r\n\r\ndescribe('App', () => {\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [App],\r\n      providers: [provideZonelessChangeDetection()]\r\n    }).compileComponents();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    const fixture = TestBed.createComponent(App);\r\n    const app = fixture.componentInstance;\r\n    expect(app).toBeTruthy();\r\n  });\r\n\r\n  it('should render title', () => {\r\n    const fixture = TestBed.createComponent(App);\r\n    fixture.detectChanges();\r\n    const compiled = fixture.nativeElement as HTMLElement;\r\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, rtsp-webrtc-client');\r\n  });\r\n});\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}